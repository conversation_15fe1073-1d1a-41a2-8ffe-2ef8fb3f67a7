/**
 * Error handling and logging service
 * Provides centralized error handling and logging functionality
 */

// Define log levels
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  CRITICAL = 'CRITICAL'
}

// Define error categories
export enum ErrorCategory {
  DATABASE = 'DATABASE',
  MAP = 'MAP',
  UI = 'UI',
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION',
  UNKNOWN = 'UNKNOWN'
}

// Interface for structured log entries
export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  category?: ErrorCategory;
  details?: any;
  stack?: string;
}

// Class to handle logging and error reporting
export class ErrorService {
  private static instance: ErrorService;
  private logs: LogEntry[] = [];
  private maxLogs: number = 100;
  private consoleEnabled: boolean = true;
  private errorCallback?: (error: LogEntry) => void;

  private constructor() {
    // Private constructor for singleton pattern
  }

  // Get singleton instance
  public static getInstance(): ErrorService {
    if (!ErrorService.instance) {
      ErrorService.instance = new ErrorService();
    }
    return ErrorService.instance;
  }

  // Set error callback function
  public setErrorCallback(callback: (error: LogEntry) => void): void {
    this.errorCallback = callback;
  }

  // Enable/disable console logging
  public setConsoleEnabled(enabled: boolean): void {
    this.consoleEnabled = enabled;
  }

  // Log a message with specified level
  public log(level: LogLevel, message: string, category: ErrorCategory = ErrorCategory.UNKNOWN, details?: any): void {
    const logEntry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      category,
      details
    };

    // Add to logs array, maintaining max size
    this.logs.push(logEntry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    // Log to console if enabled
    if (this.consoleEnabled) {
      this.logToConsole(logEntry);
    }

    // Call error callback if it's an error and callback is defined
    if ((level === LogLevel.ERROR || level === LogLevel.CRITICAL) && this.errorCallback) {
      this.errorCallback(logEntry);
    }
  }

  // Log error with stack trace
  public logError(error: Error, message: string, category: ErrorCategory = ErrorCategory.UNKNOWN, details?: any): void {
    const logEntry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: LogLevel.ERROR,
      message,
      category,
      details,
      stack: error.stack
    };

    // Add to logs array, maintaining max size
    this.logs.push(logEntry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    // Log to console if enabled
    if (this.consoleEnabled) {
      this.logToConsole(logEntry);
    }

    // Call error callback if defined
    if (this.errorCallback) {
      this.errorCallback(logEntry);
    }
  }

  // Helper methods for different log levels
  public debug(message: string, category: ErrorCategory = ErrorCategory.UNKNOWN, details?: any): void {
    this.log(LogLevel.DEBUG, message, category, details);
  }

  public info(message: string, category: ErrorCategory = ErrorCategory.UNKNOWN, details?: any): void {
    this.log(LogLevel.INFO, message, category, details);
  }

  public warn(message: string, category: ErrorCategory = ErrorCategory.UNKNOWN, details?: any): void {
    this.log(LogLevel.WARN, message, category, details);
  }

  public error(message: string, category: ErrorCategory = ErrorCategory.UNKNOWN, details?: any): void {
    this.log(LogLevel.ERROR, message, category, details);
  }

  public critical(message: string, category: ErrorCategory = ErrorCategory.UNKNOWN, details?: any): void {
    this.log(LogLevel.CRITICAL, message, category, details);
  }

  // Get all logs
  public getLogs(): LogEntry[] {
    return [...this.logs];
  }

  // Get logs filtered by level
  public getLogsByLevel(level: LogLevel): LogEntry[] {
    return this.logs.filter(log => log.level === level);
  }

  // Clear logs
  public clearLogs(): void {
    this.logs = [];
  }

  // Format and log to console
  private logToConsole(logEntry: LogEntry): void {
    const { level, message, category, details, stack } = logEntry;
    
    // Define styles for different log levels
    const styles = {
      [LogLevel.DEBUG]: 'color: #6c757d',
      [LogLevel.INFO]: 'color: #0d6efd',
      [LogLevel.WARN]: 'color: #ffc107; font-weight: bold',
      [LogLevel.ERROR]: 'color: #dc3545; font-weight: bold',
      [LogLevel.CRITICAL]: 'color: #fff; background-color: #dc3545; font-weight: bold; padding: 2px 5px; border-radius: 3px'
    };

    // Format the log message
    const formattedCategory = category ? `[${category}]` : '';
    const formattedMessage = `${level} ${formattedCategory}: ${message}`;

    // Log with appropriate styling
    console.log(`%c${formattedMessage}`, styles[level]);
    
    // Log details if provided
    if (details) {
      console.log('Details:', details);
    }
    
    // Log stack trace if provided
    if (stack) {
      console.log('Stack trace:', stack);
    }
  }
}

// Export singleton instance
export const errorService = ErrorService.getInstance();

// Global error handler
export function setupGlobalErrorHandling(): void {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    errorService.logError(
      event.reason instanceof Error ? event.reason : new Error(String(event.reason)),
      'Unhandled Promise Rejection',
      ErrorCategory.UNKNOWN
    );
  });

  // Handle uncaught exceptions
  window.addEventListener('error', (event) => {
    errorService.logError(
      event.error || new Error(event.message),
      'Uncaught Exception',
      ErrorCategory.UNKNOWN,
      {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      }
    );
    
    // Prevent default browser error handling
    event.preventDefault();
  });
}
