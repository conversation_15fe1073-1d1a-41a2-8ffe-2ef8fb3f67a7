import React, { useState, useEffect, useRef } from 'react';
import { format } from 'date-fns';
import { Search, Filter, Shield, Target, Clock, Users, Map, Upload, AlertTriangle, RefreshCcw } from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { useResponseStore } from '@/store/responseStore';
import { useIncidentStore } from '@/store/incidentStore';
import { Response, ActionType, ResponseStatus } from '@/types/incident';

// Add custom CSS for military scrollbar
const militaryScrollbarStyle = `
  /* Custom Military Scrollbar Styles */
  .military-scrollbar::-webkit-scrollbar {
    width: 8px;
    background-color: #1a1e23;
  }
  
  .military-scrollbar::-webkit-scrollbar-thumb {
    background-color: #394049;
    border: 1px solid #586173;
    clip-path: polygon(0 0, calc(100% - 3px) 0, 100% 3px, 100% 100%, 3px 100%, 0 calc(100% - 3px));
  }
  
  .military-scrollbar::-webkit-scrollbar-track {
    background-color: #1a1e23;
    border: 1px solid #2b3038;
  }
`;

// Add style element to head once
if (typeof document !== 'undefined') {
  const styleEl = document.createElement('style');
  styleEl.type = 'text/css';
  styleEl.appendChild(document.createTextNode(militaryScrollbarStyle));
  document.head.appendChild(styleEl);
}

const getIconForResponseType = (type: ActionType): React.ReactNode => {
  switch (type) {
    case ActionType.ADO:
    case ActionType.ASO:
    case ActionType.IBO:
      return <Shield size={16} className="text-military-blue" />;
    case ActionType.SEARCH_OPS:
    case ActionType.SEARCH_AND_CLEARANCE:
    case ActionType.COMPOUND_SEARCH:
    case ActionType.CARDON_AND_SEARCH:
      return <Target size={16} className="text-military-blue" />;
    case ActionType.ROUTE_CLEARANCE:
    case ActionType.ROUTE_PICQUETTING:
    case ActionType.ROUTE_PATROLLING:
    case ActionType.ROUTE_RECCE:
    case ActionType.ROUTE_BD:
      return <Map size={16} className="text-military-blue" />;
    case ActionType.AIR_OPS:
    case ActionType.DRONE_STRIKES:
    case ActionType.ISR_MISSIONS:
      return <Target size={16} className="text-military-blue" />;
    default:
      return <Shield size={16} className="text-military-blue" />;
  }
};

const getStatusColor = (status: ResponseStatus): string => {
  switch (status) {
    case ResponseStatus.PLANNED:
      return 'bg-military-amber';
    case ResponseStatus.IN_PROGRESS:
      return 'bg-military-blue';
    case ResponseStatus.COMPLETED:
      return 'bg-military-darkgreen';
    case ResponseStatus.CANCELLED:
      return 'bg-military-red';
    default:
      return 'bg-military-gray';
  }
};

interface ResponseItemProps {
  response: Response;
  onClick: () => void;
}

const ResponseItem: React.FC<ResponseItemProps> = ({ response, onClick }) => {
  const formattedDate = format(new Date(response.startDate), 'dd MMM yyyy HH:mm');

  // Format the response type for display
  const formatResponseType = (type: ActionType): string => {
    return type.replace(/_/g, ' ');
  };

  const style = {
    animationDelay: '0.1s'
  };

  return (
    <div
      className="p-3 border-b border-military-border hover:bg-military-navy cursor-pointer transition-colors animate-slide-up"
      onClick={onClick}
      style={style}
    >
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-1 p-1.5 bg-military-panel border border-military-border"
             style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}>
          {getIconForResponseType(response.type)}
        </div>
        <div className="flex-grow min-w-0">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-military-body text-military-white uppercase truncate">{response.title}</h4>
            <span className="text-xs font-mono text-military-white opacity-70 ml-2 whitespace-nowrap">{formattedDate}</span>
          </div>
          <div className="flex items-center mt-1 space-x-2">
            <Badge
              label={formatResponseType(response.type)}
              color="military"
              variant="military"
              className="bg-military-blue"
            />
            <Badge
              label={response.status}
              color="military"
              variant="military"
              className={getStatusColor(response.status)}
            />
            {response.priority && (
              <Badge
                label={response.priority.toUpperCase()}
                color="military"
                variant="military"
                className={`bg-${response.priority === 'high' ? 'military-red' : response.priority === 'medium' ? 'military-amber' : 'military-darkgreen'}`}
              />
            )}
          </div>
          <p className="text-xs text-military-white opacity-70 mt-1 line-clamp-2">{response.description}</p>
          <div className="flex items-center mt-1 text-xs text-military-white opacity-70">
            <Clock size={12} className="mr-1" />
            <span>CMD: {response.commander}</span>
            {response.relatedIncidentId && (
              <span className="ml-3 flex items-center">
                <AlertTriangle size={12} className="mr-1" />
                <span>INCIDENT: {response.relatedIncidentId.substring(0, 8)}</span>
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const ResponseList: React.FC = () => {
  const { responses, filteredResponses, selectResponse, setFilter, filter, loadResponses } = useResponseStore();
  const { incidents } = useIncidentStore();
  const [searchTerm, setSearchTerm] = useState(filter.searchTerm || '');
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const listContainerRef = useRef<HTMLDivElement>(null);

  // Load responses when component mounts
  useEffect(() => {
    loadResponses();
  }, [loadResponses]);

  // Effect to handle scroll behavior
  useEffect(() => {
    // Ensure proper scrollbar visibility by forcing a reflow
    if (listContainerRef.current) {
      listContainerRef.current.style.display = 'none';
      void listContainerRef.current.offsetHeight; // Force reflow
      listContainerRef.current.style.display = '';
    }
  }, [filteredResponses.length, showFilterMenu]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setFilter({ searchTerm });
  };

  const handleResponseClick = (id: string) => {
    selectResponse(id);
  };

  const resetFilters = () => {
    setSearchTerm('');
    setFilter({
      types: undefined,
      statuses: undefined,
      searchTerm: '',
      tags: undefined
    });
  };

  // Sort by date (newest first)
  const sortedResponses = [...filteredResponses].sort((a: Response, b: Response) => {
    return new Date(b.startDate).getTime() - new Date(a.startDate).getTime();
  });

  return (
    <Card
      title="TACTICAL RESPONSES"
      variant="military"
      className="h-full flex flex-col overflow-hidden"
      headerAction={
        <div className="flex space-x-1">
          <Button
            size="sm"
            variant="military"
            leftIcon={<Filter size={14} />}
            onClick={() => setShowFilterMenu(!showFilterMenu)}
            className="military-btn bg-military-darkgray hover:bg-military-navy"
            title="Toggle filter panel"
          >
            FILTER
          </Button>
          <Button
            size="sm"
            variant="military"
            leftIcon={<RefreshCcw size={14} />}
            onClick={() => {
              loadResponses();
            }}
            className="military-btn bg-military-darkgray hover:bg-military-navy"
            title="Refresh responses"
          >
            SYNC
          </Button>
        </div>
      }
    >
      <form onSubmit={handleSearch} className="mb-3">
        <div className="relative">
          <input
            type="text"
            placeholder="SEARCH RESPONSES..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-military-panel border border-military-border text-military-white px-3 py-2 text-sm font-military-body placeholder-military-white placeholder-opacity-50 focus:outline-none focus:ring-1 focus:ring-military-accent"
            style={{ clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' }}
          />
          <button
            type="submit"
            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-military-white"
          >
            <Search size={16} />
          </button>
        </div>
      </form>

      {showFilterMenu && (
        <div className="mb-3 p-3 bg-military-panel border border-military-border"
             style={{ clipPath: 'polygon(0 0, calc(100% - 8px) 0, 100% 8px, 100% 100%, 8px 100%, 0 calc(100% - 8px))' }}>
          <h4 className="text-sm font-military-heading text-military-white uppercase mb-2">FILTER PARAMETERS</h4>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="text-xs font-military-body text-military-white opacity-80">TYPE</label>
              <select
                className="block w-full mt-1 text-xs font-military-body uppercase bg-military-navy border border-military-border text-military-white focus:outline-none focus:border-military-accent"
                style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                value={filter.types ? filter.types[0] : ''}
                onChange={(e) => setFilter({ types: e.target.value ? [e.target.value as ActionType] : undefined })}
              >
                <option value="">ALL TYPES</option>
                {Object.values(ActionType).filter(type => type !== ActionType.NONE).map(type => (
                  <option key={type} value={type}>{type.replace(/_/g, ' ')}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="text-xs font-military-body text-military-white opacity-80">STATUS</label>
              <select
                className="block w-full mt-1 text-xs font-military-body uppercase bg-military-navy border border-military-border text-military-white focus:outline-none focus:border-military-accent"
                style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                value={filter.statuses ? filter.statuses[0] : ''}
                onChange={(e) => setFilter({ statuses: e.target.value ? [e.target.value as ResponseStatus] : undefined })}
              >
                <option value="">ALL STATUSES</option>
                {Object.values(ResponseStatus).map(status => (
                  <option key={status} value={status}>{status}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="text-xs font-military-body text-military-white opacity-80">PRIORITY</label>
              <select
                className="block w-full mt-1 text-xs font-military-body uppercase bg-military-navy border border-military-border text-military-white focus:outline-none focus:border-military-accent"
                style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                value={filter.priority || ''}
                onChange={(e) => setFilter({ priority: e.target.value || undefined })}
              >
                <option value="">ALL PRIORITIES</option>
                <option value="high">HIGH</option>
                <option value="medium">MEDIUM</option>
                <option value="low">LOW</option>
              </select>
            </div>

            <div>
              <label className="text-xs font-military-body text-military-white opacity-80">INCIDENT</label>
              <select
                className="block w-full mt-1 text-xs font-military-body uppercase bg-military-navy border border-military-border text-military-white focus:outline-none focus:border-military-accent"
                style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                value={filter.incidentId || ''}
                onChange={(e) => setFilter({ incidentId: e.target.value || undefined })}
              >
                <option value="">ALL INCIDENTS</option>
                {incidents.map(incident => (
                  <option key={incident.id} value={incident.id}>
                    {incident.id.substring(0, 8)}: {incident.title.substring(0, 15)}
                    {incident.title.length > 15 ? '...' : ''}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="flex justify-end mt-3">
            <Button
              size="sm"
              variant="military"
              className="military-btn"
              onClick={resetFilters}
            >
              RESET
            </Button>
          </div>
        </div>
      )}

      <div className="flex-grow overflow-hidden flex flex-col">
        {filteredResponses.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-40 military-container p-4">
            <AlertTriangle size={24} className="text-military-amber mb-2" />
            <p className="text-military-white font-military-body text-center">NO RESPONSES FOUND WITH CURRENT FILTERS</p>
            <Button
              size="sm"
              variant="military"
              onClick={resetFilters}
              className="military-btn bg-military-darkgray mt-2"
            >
              RESET FILTERS
            </Button>
          </div>
        ) : (
          <div className="divide-y divide-military-border flex-1 overflow-y-auto overflow-x-hidden pr-1 military-scrollbar" ref={listContainerRef}>
            {sortedResponses.map((response, index) => (
              <ResponseItem
                key={`${response.id}-${index}`}
                response={response}
                onClick={() => handleResponseClick(response.id)}
              />
            ))}
          </div>
        )}
      </div>
      
      {/* Count display */}
      <div className="mt-2 text-xs text-military-white opacity-70 text-right pt-1 border-t border-military-border">
        {filteredResponses.length} {filteredResponses.length === 1 ? 'RESPONSE' : 'RESPONSES'} DISPLAYED
        {filteredResponses.length !== responses.length && ` (FILTERED FROM ${responses.length})`}
      </div>
    </Card>
  );
};

export default ResponseList;
