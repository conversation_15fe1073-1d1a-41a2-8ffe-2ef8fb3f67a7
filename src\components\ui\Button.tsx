import React, { ButtonHTMLAttributes, ReactNode } from 'react';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'ghost' | 'military';
  size?: 'sm' | 'md' | 'lg';
  children: ReactNode;
  className?: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  isLoading?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  children,
  className = '',
  leftIcon,
  rightIcon,
  isLoading = false,
  disabled,
  ...props
}) => {
  // Check if we should use military styling
  const isMilitary = variant === 'military' || className.includes('military-btn');

  // Base styles
  const baseStyles = isMilitary
    ? 'inline-flex items-center justify-center font-military-body uppercase tracking-wider transition-colors focus:outline-none'
    : 'inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';

  const variantStyles = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500',
    secondary: 'bg-gray-500 hover:bg-gray-600 text-white focus:ring-gray-400',
    success: 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500',
    danger: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500',
    warning: 'bg-yellow-500 hover:bg-yellow-600 text-white focus:ring-yellow-400',
    info: 'bg-cyan-500 hover:bg-cyan-600 text-white focus:ring-cyan-400',
    ghost: isMilitary
      ? 'bg-military-panel border border-military-border text-military-white hover:bg-military-darkgreen'
      : 'bg-transparent hover:bg-gray-100 text-gray-700 dark:text-gray-200 dark:hover:bg-gray-700 focus:ring-gray-400',
    military: 'bg-military-panel border border-military-border text-military-white hover:bg-military-darkgreen hover:border-military-accent'
  };

  const sizeStyles = {
    sm: isMilitary ? 'text-xs px-2 py-1 gap-1' : 'text-xs px-2 py-1 gap-1',
    md: isMilitary ? 'text-xs px-3 py-1.5 gap-1.5' : 'text-sm px-3 py-2 gap-2',
    lg: isMilitary ? 'text-sm px-4 py-2 gap-2' : 'text-base px-4 py-2 gap-2'
  };

  const disabledStyles = 'opacity-50 cursor-not-allowed';

  // Add clip-path for military buttons
  const militaryStyles = isMilitary
    ? 'clip-path: polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px));'
    : '';

  return (
    <button
      className={`
        ${baseStyles}
        ${variantStyles[variant]}
        ${sizeStyles[size]}
        ${(disabled || isLoading) ? disabledStyles : ''}
        ${className}
      `}
      disabled={disabled || isLoading}
      style={isMilitary ? { clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' } : undefined}
      {...props}
      // Remove loading attribute from DOM to avoid React warnings
      loading={undefined}
    >
      {isLoading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      )}

      {!isLoading && leftIcon && <span className="icon-left">{leftIcon}</span>}
      <span>{isMilitary ? children : children}</span>
      {!isLoading && rightIcon && <span className="icon-right">{rightIcon}</span>}
    </button>
  );
};

export default Button;