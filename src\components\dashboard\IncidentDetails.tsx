import React, { useState } from 'react';
import { format } from 'date-fns';
import { X, MapPin, Clock, User, Tag, Trash2 } from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import IncidentForm from './IncidentForm';
import { useIncidentStore } from '@/store/incidentStore';
import { IncidentSeverity, IncidentStatus, ActionType } from '@/types/incident';

const IncidentDetails: React.FC = () => {
  const { selectedIncident, selectIncident, deleteIncident } = useIncidentStore();
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  if (!selectedIncident) {
    return null;
  }

  const formattedReportDate = format(new Date(selectedIncident.reportedAt), 'MMM dd, yyyy h:mm a');
  const formattedResolvedDate = selectedIncident.resolvedAt
    ? format(new Date(selectedIncident.resolvedAt), 'MMM dd, yyyy h:mm a')
    : 'Not resolved';

  const severityColor =
    selectedIncident.severity === IncidentSeverity.CRITICAL ? 'danger' :
    selectedIncident.severity === IncidentSeverity.HIGH ? 'warning' :
    selectedIncident.severity === IncidentSeverity.MEDIUM ? 'info' : 'default';

  const statusColor =
    selectedIncident.status === IncidentStatus.RESOLVED ? 'success' :
    selectedIncident.status === IncidentStatus.IN_PROGRESS ? 'warning' :
    selectedIncident.status === IncidentStatus.CLOSED ? 'success' : 'default';

  const handleDelete = async () => {
    await deleteIncident(selectedIncident.id);
    selectIncident(null);
  };

  // Don't use a nested modal for editing - use a state in the parent component instead
  if (showEditForm) {
    return (
      <div className="w-full max-w-2xl">
        <IncidentForm
          incident={selectedIncident}
          onClose={() => setShowEditForm(false)}
        />
      </div>
    );
  }

  return (
    <div className="bg-military-panel border border-military-border max-w-4xl w-full max-h-[90vh] flex flex-col"
         style={{ clipPath: 'polygon(0 0, calc(100% - 12px) 0, 100% 12px, 100% 100%, 12px 100%, 0 calc(100% - 12px))' }}>
      <div className="flex items-center justify-between px-6 py-4 bg-military-navy border-b border-military-border">
        <h2 className="text-lg font-military-heading text-military-white uppercase tracking-wider">
          TACTICAL INCIDENT DETAILS
        </h2>
        <Button
          variant="military"
          size="sm"
          onClick={() => selectIncident(null)}
          className="military-btn"
        >
          <X size={18} />
        </Button>
      </div>

      <div className="flex-grow overflow-y-auto p-6 bg-military-black">
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-military-heading text-military-white uppercase">{selectedIncident.title}</h3>
            <div className="flex flex-wrap gap-2 mt-2">
              <Badge variant="military" label={selectedIncident.type.replace('TS_', '')} color="primary" />
              {selectedIncident.action && selectedIncident.action !== ActionType.NONE && (
                <Badge variant="military" label={selectedIncident.action} color="secondary" />
              )}
              <Badge variant="military" label={selectedIncident.severity} color={severityColor} />
              <Badge variant="military" label={selectedIncident.status} color={statusColor} />
              {selectedIncident.tags?.map(tag => (
                <Badge key={tag} variant="military" label={tag} color="default" />
              ))}
            </div>
          </div>

          <div className="bg-military-navy border border-military-border p-4"
               style={{ clipPath: 'polygon(0 0, calc(100% - 8px) 0, 100% 8px, 100% 100%, 8px 100%, 0 calc(100% - 8px))' }}>
            <p className="text-military-white font-military-body">{selectedIncident.description}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-start bg-military-navy border border-military-border p-3"
                   style={{ clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' }}>
                <MapPin size={18} className="text-military-accent mt-0.5 mr-2" />
                <div>
                  <p className="text-sm font-military-heading text-military-white uppercase">LOCATION</p>
                  <p className="text-sm font-military-body text-military-white">{selectedIncident.address}</p>
                  <p className="text-xs font-mono text-military-white opacity-70 mt-1">
                    LAT: {selectedIncident.location.latitude.toFixed(6)},
                    LON: {selectedIncident.location.longitude.toFixed(6)}
                  </p>
                </div>
              </div>

              <div className="flex items-start bg-military-navy border border-military-border p-3"
                   style={{ clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' }}>
                <User size={18} className="text-military-accent mt-0.5 mr-2" />
                <div>
                  <p className="text-sm font-military-heading text-military-white uppercase">REPORTED BY</p>
                  <p className="text-sm font-military-body text-military-white">{selectedIncident.reportedBy}</p>

                  {selectedIncident.assignedTo && (
                    <div className="mt-2">
                      <p className="text-sm font-military-heading text-military-white uppercase">ASSIGNED TO</p>
                      <p className="text-sm font-military-body text-military-white">{selectedIncident.assignedTo}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-start bg-military-navy border border-military-border p-3"
                   style={{ clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' }}>
                <Clock size={18} className="text-military-accent mt-0.5 mr-2" />
                <div>
                  <p className="text-sm font-military-heading text-military-white uppercase">REPORTED ON</p>
                  <p className="text-sm font-military-body text-military-white">{formattedReportDate}</p>

                  <div className="mt-2">
                    <p className="text-sm font-military-heading text-military-white uppercase">RESOLVED ON</p>
                    <p className="text-sm font-military-body text-military-white">{formattedResolvedDate}</p>
                  </div>
                </div>
              </div>

              {selectedIncident.tags && selectedIncident.tags.length > 0 && (
                <div className="flex items-start bg-military-navy border border-military-border p-3"
                     style={{ clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' }}>
                  <Tag size={18} className="text-military-accent mt-0.5 mr-2" />
                  <div>
                    <p className="text-sm font-military-heading text-military-white uppercase">TAGS</p>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {selectedIncident.tags.map(tag => (
                        <span key={tag} className="inline-block px-2 py-0.5 text-xs font-military-body text-military-white bg-military-darkgreen border border-military-border"
                              style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}>
                          {tag.toUpperCase()}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {selectedIncident.attachments && selectedIncident.attachments.length > 0 && (
            <div className="bg-military-navy border border-military-border p-3"
                 style={{ clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' }}>
              <h4 className="text-sm font-military-heading text-military-white uppercase mb-2">ATTACHMENTS</h4>
              <div className="flex gap-2">
                {selectedIncident.attachments.map((attachment, index) => (
                  <div
                    key={index}
                    className="bg-military-panel border border-military-border p-2 text-sm font-military-body text-military-white"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                  >
                    {attachment}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-between px-6 py-4 bg-military-navy border-t border-military-border">
        <Button
          variant="military"
          onClick={() => setShowDeleteConfirm(true)}
          className="military-btn bg-military-red"
          leftIcon={<Trash2 size={16} />}
        >
          DELETE
        </Button>
        <div className="flex space-x-2">
          <Button
            variant="military"
            onClick={() => selectIncident(null)}
            className="military-btn"
          >
            CLOSE
          </Button>
          <Button
            variant="military"
            onClick={() => setShowEditForm(true)}
            className="military-btn bg-military-darkgreen"
          >
            EDIT INCIDENT
          </Button>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-military-black bg-opacity-80 flex items-center justify-center z-[1001]">
          <div className="bg-military-panel border border-military-border p-6 max-w-md w-full"
               style={{ clipPath: 'polygon(0 0, calc(100% - 12px) 0, 100% 12px, 100% 100%, 12px 100%, 0 calc(100% - 12px))' }}>
            <h3 className="text-lg font-military-heading text-military-white uppercase mb-4">
              CONFIRM DELETION
            </h3>
            <div className="bg-military-red bg-opacity-20 border border-military-red p-4 mb-6"
                 style={{ clipPath: 'polygon(0 0, calc(100% - 8px) 0, 100% 8px, 100% 100%, 8px 100%, 0 calc(100% - 8px))' }}>
              <p className="text-military-white font-military-body">
                WARNING: Are you sure you want to delete this incident? This action cannot be undone.
              </p>
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                variant="military"
                onClick={() => setShowDeleteConfirm(false)}
                className="military-btn"
              >
                CANCEL
              </Button>
              <Button
                variant="military"
                onClick={handleDelete}
                className="military-btn bg-military-red"
              >
                CONFIRM DELETE
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default IncidentDetails;