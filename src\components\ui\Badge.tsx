import React from 'react';

interface BadgeProps {
  label: string;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info' | 'default' | 'military';
  className?: string;
  variant?: 'default' | 'military';
}

const Badge: React.FC<BadgeProps> = ({
  label,
  color = 'default',
  className = '',
  variant = 'default'
}) => {
  // Check if we should use military styling
  const isMilitary = variant === 'military' || className.includes('military-badge');

  // Base classes
  const baseClasses = isMilitary
    ? 'inline-flex items-center px-2 py-0.5 text-xs font-military-body uppercase tracking-wider'
    : 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium';

  // Standard color classes
  const standardColorClasses = {
    primary: 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100',
    secondary: 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100',
    success: 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100',
    warning: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100',
    danger: 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100',
    info: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-800 dark:text-cyan-100',
    default: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100'
  };

  // Military color classes
  const militaryColorClasses = {
    primary: 'bg-military-navy border border-military-border text-military-white',
    secondary: 'bg-military-panel border border-military-border text-military-white',
    success: 'bg-military-darkgreen border border-military-border text-military-white',
    warning: 'bg-military-amber border border-military-border text-military-black',
    danger: 'bg-military-red border border-military-border text-military-white',
    info: 'bg-military-navy border border-military-border text-military-white',
    default: 'bg-military-panel border border-military-border text-military-white',
    military: 'bg-military-panel border border-military-border text-military-white'
  };

  // Select the appropriate color classes based on variant
  const colorClasses = isMilitary ? militaryColorClasses : standardColorClasses;

  return (
    <span
      className={`${baseClasses} ${colorClasses[color]} ${className}`}
      style={isMilitary ? { clipPath: 'polygon(0 0, calc(100% - 5px) 0, 100% 5px, 100% 100%, 5px 100%, 0 calc(100% - 5px))' } : undefined}
    >
      {isMilitary ? label.toUpperCase() : label}
    </span>
  );
};

export default Badge;