import React, { useState, useRef } from 'react';
import { FileCheck, Upload, Al<PERSON><PERSON>riangle, CheckCircle, Info, Download, FileSpreadsheet, Calendar } from 'lucide-react';
import Button from '@/components/ui/Button';
import { analyzeIncidentFile, analyzeResponseFile, ValidationResult } from '@/utils/fileAnalyzer';
import { exportToExcel, prepareIncidentsForExport, prepareResponsesForExport, DateFormat } from '@/utils/excelUtils';
import { Incident, Response } from '@/types/incident';
import { CoordinateFormat } from '@/utils/coordinateUtils';

interface DataValidationTabProps {
  incidents: Incident[];
  responses: Response[];
}

const DataValidationTab: React.FC<DataValidationTabProps> = ({ incidents, responses }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [fileType, setFileType] = useState<'incident' | 'response'>('incident');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Function to handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsLoading(true);
    setValidationResult(null);

    // Analyze the file based on the selected type
    const analyzePromise = fileType === 'incident'
      ? analyzeIncidentFile(file)
      : analyzeResponseFile(file);

    analyzePromise
      .then(result => {
        setValidationResult(result);
      })
      .catch(error => {
        console.error('File analysis failed:', error);
      })
      .finally(() => {
        setIsLoading(false);
        // Reset file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      });
  };

  // Function to export template files
  const exportTemplate = (type: 'incident' | 'response') => {
    try {
      // Format date in YYYY-MM-DD format (ISO format)
      const today = new Date();
      const formattedDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

      if (type === 'incident') {
        // Create a template with one sample incident
        const template = [{
          id: 'INC-TEMPLATE',
          title: 'Sample Incident',
          description: 'This is a sample incident description',
          type: 'OTHER',
          severity: 'MEDIUM',
          status: 'REPORTED',
          latitude: 34.0151,
          longitude: 71.5249,
          address: 'Sample Address, City, Country',
          reportedAt: formattedDate, // Using ISO format YYYY-MM-DD
          reportedBy: 'Template User',
          tags: 'tag1;tag2;tag3'
        }];

        exportToExcel(template, 'incident_template.xlsx');
      } else {
        // Create a template with one sample response
        const template = [{
          id: 'RESP-TEMPLATE',
          title: 'Sample Response',
          description: 'This is a sample response description',
          type: 'PATROL',
          status: 'PLANNED',
          latitude: 34.0151,
          longitude: 71.5249,
          address: 'Sample Address, City, Country',
          startDate: formattedDate, // Using ISO format YYYY-MM-DD
          endDate: formattedDate, // Using ISO format YYYY-MM-DD
          commander: 'Template Commander',
          units: 'Unit1;Unit2',
          tags: 'tag1;tag2;tag3'
        }];

        exportToExcel(template, 'response_template.xlsx');
      }
    } catch (error) {
      console.error('Failed to export template:', error);
      alert('Failed to export template. Please try again.');
    }
  };

  return (
    <div>
      <h3 className="text-lg font-military-heading text-military-white uppercase mb-6">DATA VALIDATION</h3>

      {/* File Type Selection */}
      <div className="mb-6 p-4 border border-military-border">
        <h4 className="text-md font-military-heading text-military-white uppercase mb-4">SELECT FILE TYPE</h4>
        <p className="text-military-white font-military-body mb-4">
          Select the type of data you want to validate.
        </p>
        <div className="flex space-x-4">
          <Button
            variant="military"
            className={`military-btn ${fileType === 'incident' ? 'bg-military-darkgreen' : 'bg-military-navy'}`}
            onClick={() => setFileType('incident')}
          >
            INCIDENT DATA
          </Button>
          <Button
            variant="military"
            className={`military-btn ${fileType === 'response' ? 'bg-military-darkgreen' : 'bg-military-navy'}`}
            onClick={() => setFileType('response')}
          >
            RESPONSE DATA
          </Button>
        </div>
      </div>

      {/* Date Format Information */}
      <div className="mb-6 p-4 border border-military-border">
        <h4 className="text-md font-military-heading text-military-white uppercase mb-4">DATE FORMAT GUIDE</h4>
        <div className="flex items-start">
          <Calendar size={20} className="text-military-white mr-3 mt-1 flex-shrink-0" />
          <div>
            <p className="text-military-white font-military-body mb-2">
              The system automatically detects and converts various date formats, but for best results, use <strong>YYYY-MM-DD</strong> format.
            </p>
            <p className="text-military-white font-military-body mb-2">
              Supported date formats:
            </p>
            <ul className="list-disc list-inside text-military-white font-military-body ml-4 mb-3">
              <li><strong>ISO format (recommended):</strong> YYYY-MM-DD (e.g., 2024-05-15)</li>
              <li><strong>US format:</strong> MM/DD/YY or MM/DD/YYYY (e.g., 05/15/24 or 05/15/2024)</li>
              <li><strong>European format with dots:</strong> DD.MM.YY or DD.MM.YYYY (e.g., 15.05.24 or 15.05.2024)</li>
              <li><strong>Format with dashes:</strong> DD-MM-YY or DD-MM-YYYY (e.g., 15-05-24 or 15-05-2024)</li>
              <li><strong>Text month format:</strong> DD Month YYYY (e.g., 15 May 2024)</li>
            </ul>
            <p className="text-military-white font-military-body text-sm italic mb-3">
              Note: For two-digit years (YY), years less than 50 are assumed to be 20YY, and years 50 or greater are assumed to be 19YY.
            </p>

            <div className="bg-military-red bg-opacity-20 border border-military-red p-3 mb-3">
              <h5 className="text-sm font-military-heading text-military-white uppercase mb-2 flex items-center">
                <AlertTriangle size={16} className="mr-2" /> Excel Date Format Warning
              </h5>
              <p className="text-military-white font-military-body mb-2">
                Excel sometimes incorrectly interprets dates and displays them in an unusual format like <strong>"01 Jan 45570 00:00"</strong>.
                This happens when Excel misinterprets the date format during file creation or editing.
              </p>
              <p className="text-military-white font-military-body">
                If you encounter this issue:
              </p>
              <ol className="list-decimal list-inside text-military-white font-military-body ml-4 mt-1">
                <li>The system will attempt to extract the original dates from the file</li>
                <li>Always verify that imported dates are correct after import</li>
                <li>To prevent this issue, format your Excel cells as "Text" before entering dates, or use the ISO format (YYYY-MM-DD)</li>
              </ol>
            </div>
          </div>
        </div>
      </div>

      {/* File Upload */}
      <div className="mb-6 p-4 border border-military-border">
        <h4 className="text-md font-military-heading text-military-white uppercase mb-4">VALIDATE FILE</h4>
        <p className="text-military-white font-military-body mb-4">
          Upload a file to check if it can be imported into the system. The validator will identify any issues with the file format.
        </p>

        <input
          type="file"
          accept=".csv,.xlsx,.xls"
          ref={fileInputRef}
          onChange={handleFileSelect}
          className="hidden"
        />

        <div className="flex space-x-4">
          <Button
            variant="military"
            className="military-btn bg-military-blue"
            onClick={() => fileInputRef.current?.click()}
            leftIcon={<Upload size={16} />}
            disabled={isLoading}
          >
            {isLoading ? 'ANALYZING...' : 'SELECT FILE TO VALIDATE'}
          </Button>

          <Button
            variant="military"
            className="military-btn bg-military-darkgreen"
            onClick={() => exportTemplate(fileType)}
            leftIcon={<FileSpreadsheet size={16} />}
            disabled={isLoading}
          >
            DOWNLOAD TEMPLATE
          </Button>
        </div>
      </div>

      {/* Validation Results */}
      {validationResult && (
        <div className="mb-6 p-4 border border-military-border">
          <h4 className="text-md font-military-heading text-military-white uppercase mb-4">VALIDATION RESULTS</h4>

          {/* Summary */}
          <div className="mb-4 p-3 border border-military-navy bg-military-navy bg-opacity-30">
            <h5 className="text-sm font-military-heading text-military-white uppercase mb-2">SUMMARY</h5>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-military-white font-military-body">
                  <span className="font-bold">File Type:</span> {validationResult.fileType === 'csv' ? 'CSV' : validationResult.fileType === 'excel' ? 'Excel' : 'Unknown'}
                </p>
                <p className="text-military-white font-military-body">
                  <span className="font-bold">Total Rows:</span> {validationResult.stats.totalRows}
                </p>
                <p className="text-military-white font-military-body">
                  <span className="font-bold">Valid Rows:</span> {validationResult.stats.validRows}
                </p>
              </div>
              <div>
                <p className="text-military-white font-military-body">
                  <span className="font-bold">Invalid Rows:</span> {validationResult.stats.invalidRows}
                </p>
                <p className="text-military-white font-military-body">
                  <span className="font-bold">Has Location Data:</span> {validationResult.stats.hasLocationData ? 'Yes' : 'No'}
                </p>
                {validationResult.stats.hasLocationData && validationResult.stats.coordinateFormat && (
                  <p className="text-military-white font-military-body">
                    <span className="font-bold">Coordinate Format:</span>{' '}
                    {validationResult.stats.coordinateFormat === CoordinateFormat.DECIMAL_DEGREES ? 'Decimal Degrees' :
                     validationResult.stats.coordinateFormat === CoordinateFormat.DMS ? 'Degrees-Minutes-Seconds (DMS)' : 'Unknown'}
                    {validationResult.stats.needsCoordinateConversion && (
                      <span className="ml-1 text-yellow-500"> (Conversion needed)</span>
                    )}
                  </p>
                )}
                <p className="text-military-white font-military-body">
                  <span className="font-bold">Overall Status:</span>{' '}
                  {validationResult.isValid ? (
                    <span className="text-green-500">Valid</span>
                  ) : (
                    <span className="text-red-500">Invalid</span>
                  )}
                </p>
              </div>
            </div>
          </div>

          {/* Issues List */}
          {validationResult.issues.length > 0 && (
            <div className="mb-4">
              <h5 className="text-sm font-military-heading text-military-white uppercase mb-2">ISSUES FOUND</h5>
              <div className="max-h-60 overflow-y-auto">
                {validationResult.issues.map((issue, index) => (
                  <div
                    key={index}
                    className={`mb-2 p-2 border ${issue.type === 'error' ? 'border-military-red bg-military-red bg-opacity-20' : 'border-military-amber bg-military-amber bg-opacity-20'}`}
                  >
                    <p className="text-military-white font-military-body flex items-start">
                      {issue.type === 'error' ? (
                        <AlertTriangle size={16} className="text-military-red mr-2 mt-1 flex-shrink-0" />
                      ) : (
                        <Info size={16} className="text-military-amber mr-2 mt-1 flex-shrink-0" />
                      )}
                      <span>
                        <strong>{issue.type === 'error' ? 'Error' : 'Warning'}</strong>
                        {issue.field && <span> in field <strong>{issue.field}</strong></span>}
                        {issue.rowIndex && <span> at row <strong>{issue.rowIndex}</strong></span>}:
                        {' '}{issue.message}
                        {issue.suggestion && (
                          <span className="block mt-1 text-sm opacity-80">
                            <strong>Suggestion:</strong> {issue.suggestion}
                          </span>
                        )}
                      </span>
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Missing Required Fields */}
          {validationResult.stats.missingRequiredFields.length > 0 && (
            <div className="mb-4">
              <h5 className="text-sm font-military-heading text-military-white uppercase mb-2">MISSING REQUIRED FIELDS</h5>
              <p className="text-military-white font-military-body">
                The following required fields are missing from your file:
              </p>
              <ul className="list-disc list-inside text-military-white font-military-body ml-4 mt-2">
                {validationResult.stats.missingRequiredFields.map((field, index) => (
                  <li key={index}>{field}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Recommendation */}
          <div className="mt-4">
            <h5 className="text-sm font-military-heading text-military-white uppercase mb-2">RECOMMENDATION</h5>
            {validationResult.isValid ? (
              <p className="text-military-white font-military-body flex items-center">
                <CheckCircle size={16} className="text-green-500 mr-2" />
                This file can be imported into the system. Use the import function in the Data Management tab.
              </p>
            ) : (
              <p className="text-military-white font-military-body flex items-center">
                <AlertTriangle size={16} className="text-military-amber mr-2" />
                This file needs to be corrected before it can be imported. Please fix the issues listed above and try again.
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default DataValidationTab;
