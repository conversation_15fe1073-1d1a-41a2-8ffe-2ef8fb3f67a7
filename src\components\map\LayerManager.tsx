import React from 'react';
import L from 'leaflet';

interface LayerManagerProps {
  drawnItems?: L.FeatureGroup | null;
}

const LayerManager: React.FC<LayerManagerProps> = ({ drawnItems }) => {
  // This is a placeholder component for Leaflet layer management
  return (
    <div className="p-2 text-white text-sm">
      <div className="mb-2 font-bold">LAYERS</div>
      <div className="space-y-1">
        <div className="flex items-center">
          <input type="checkbox" defaultChecked className="mr-2" />
          <span>Base Layer</span>
        </div>
        <div className="flex items-center">
          <input type="checkbox" defaultChecked className="mr-2" />
          <span>Incidents</span>
        </div>
        <div className="flex items-center">
          <input type="checkbox" defaultChecked className="mr-2" />
          <span>Responses</span>
        </div>
      </div>
    </div>
  );
};

export default LayerManager;
