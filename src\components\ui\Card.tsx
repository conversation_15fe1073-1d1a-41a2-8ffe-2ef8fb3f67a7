import React, { ReactNode } from 'react';

interface CardProps {
  title?: string;
  children: ReactNode;
  className?: string;
  headerAction?: ReactNode;
  variant?: 'default' | 'military';
}

const Card: React.FC<CardProps> = ({
  title,
  children,
  className = '',
  headerAction,
  variant = 'default'
}) => {
  // Determine if we should use military styling
  const isMilitary = variant === 'military' || className.includes('military-card');

  // Base classes for different variants
  const baseClasses = isMilitary
    ? 'military-card overflow-hidden'
    : 'bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden';

  // Header classes for different variants
  const headerClasses = isMilitary
    ? 'flex items-center justify-between px-3 py-2 border-b border-military-border'
    : 'flex items-center justify-between px-4 py-3 border-b border-gray-200 dark:border-gray-700';

  // Title classes for different variants
  const titleClasses = isMilitary
    ? 'text-sm font-military-heading text-military-white uppercase tracking-wider'
    : 'text-lg font-medium text-gray-900 dark:text-white';

  // Content classes for different variants
  const contentClasses = isMilitary
    ? 'p-0'  // Remove padding for military cards to eliminate extra space
    : 'p-4';

  return (
    <div className={`${baseClasses} ${className} flex flex-col`}>
      {title && (
        <div className={`${headerClasses} z-10 sticky top-0`}>
          <h3 className={`${titleClasses}`}>{title}</h3>
          {headerAction && (
            <div className="flex items-center">{headerAction}</div>
          )}
        </div>
      )}
      <div className={`${contentClasses} flex-grow`}>{children}</div>
    </div>
  );
};

export default Card;