import React, { useState } from 'react';
import { format } from 'date-fns';
import { Search, Filter, AlertCircle, Shield, Target, Eye, Users, Siren, FileText } from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { useIncidentStore } from '@/store/incidentStore';
import { Incident, IncidentType, IncidentSeverity, IncidentStatus, ActionType } from '@/types/incident';

// Add custom CSS for military scrollbar
const militaryScrollbarStyle = `
  /* Custom Military Scrollbar Styles */
  .military-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #586173 #1a1e23;
  }

  .military-scrollbar::-webkit-scrollbar {
    width: 12px;
    height: 12px;
    background-color: #1a1e23;
    display: block;
  }

  .military-scrollbar::-webkit-scrollbar-thumb {
    background-color: #586173;
    border: 1px solid #8a99ad;
    border-radius: 2px;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
  }

  .military-scrollbar::-webkit-scrollbar-track {
    background-color: #1a1e23;
    border: 1px solid #2b3038;
    box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.3);
  }

  .military-scrollbar::-webkit-scrollbar-corner {
    background-color: #1a1e23;
  }
`;

// Add style element to head once
if (typeof document !== 'undefined') {
  const styleEl = document.createElement('style');
  styleEl.appendChild(document.createTextNode(militaryScrollbarStyle));
  document.head.appendChild(styleEl);
}

const getIconForIncidentType = (type: IncidentType): React.ReactNode => {
  switch (type) {
    case IncidentType.TS_ACTIVITY:
      return <AlertCircle size={16} className="text-military-red" />;
    case IncidentType.TS_INFIL:
      return <Shield size={16} className="text-military-navy" />;
    case IncidentType.TS_PRESENCE:
      return <Eye size={16} className="text-military-amber" />;
    case IncidentType.TS_MOV:
      return <Target size={16} className="text-military-amber" />;
    case IncidentType.TS_TASKEEL:
      return <Users size={16} className="text-military-accent" />;
    case IncidentType.TS_SB:
      return <Siren size={16} className="text-military-darkgreen" />;
    default:
      return <FileText size={16} className="text-military-white" />;
  }
};

interface IncidentItemProps {
  incident: Incident;
  onClick: () => void;
  style?: React.CSSProperties;
}

const IncidentItem: React.FC<IncidentItemProps> = ({ incident, onClick, style }) => {
  const formattedDate = format(new Date(incident.reportedAt), 'MMM dd, yyyy');

  const severityColor =
    incident.severity === IncidentSeverity.CRITICAL ? 'danger' :
    incident.severity === IncidentSeverity.HIGH ? 'warning' :
    incident.severity === IncidentSeverity.MEDIUM ? 'info' : 'default';

  const statusColor =
    incident.status === IncidentStatus.RESOLVED ? 'success' :
    incident.status === IncidentStatus.IN_PROGRESS ? 'warning' :
    incident.status === IncidentStatus.CLOSED ? 'success' : 'default';

  return (
    <div
      className="p-3 border-b border-military-border hover:bg-military-navy cursor-pointer transition-colors animate-slide-up"
      onClick={onClick}
      style={style}
    >
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-1 p-1.5 bg-military-panel border border-military-border"
             style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}>
          {getIconForIncidentType(incident.type)}
        </div>
        <div className="flex-grow min-w-0">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-military-body text-military-white uppercase truncate">{incident.title}</h4>
            <span className="text-xs font-mono text-military-white opacity-70 ml-2 whitespace-nowrap">{formattedDate}</span>
          </div>
          <p className="text-xs font-mono text-military-white opacity-70 mt-1 truncate">{incident.address}</p>
          <div className="flex flex-wrap items-center mt-2 gap-1.5">
            <Badge variant="military" label={incident.type.replace('TS_', '')} color="primary" />
            {incident.action && incident.action !== ActionType.NONE && (
              <Badge variant="military" label={incident.action} color="secondary" />
            )}
            <Badge variant="military" label={incident.severity} color={severityColor} />
            <Badge variant="military" label={incident.status} color={statusColor} />
          </div>
        </div>
      </div>
    </div>
  );
};

const IncidentsList: React.FC = () => {
  const { filteredIncidents, selectIncident, setFilter, filter } = useIncidentStore();
  const [searchTerm, setSearchTerm] = useState(filter.searchTerm || '');
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const [scrollPosition] = useState(0);


  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setFilter({ searchTerm });
  };

  const handleIncidentClick = (id: string) => {
    selectIncident(id);
  };

  // Reorder incidents for continuous scrolling
  const reorderedIncidents = [
    ...filteredIncidents.slice(scrollPosition),
    ...filteredIncidents.slice(0, scrollPosition)
  ];

  return (
    <Card
      title="TACTICAL INCIDENTS"
      variant="military"
      className="h-full flex flex-col overflow-hidden"
      headerAction={
        <div className="flex space-x-1">
          <Button
            size="sm"
            variant="military"
            leftIcon={<Filter size={14} />}
            onClick={() => setShowFilterMenu(!showFilterMenu)}
            className="military-btn"
          >
            FILTER
          </Button>
        </div>
      }
    >
      <div className="mb-3">
        <form onSubmit={handleSearch} className="relative">
          <div className="relative">
            <input
              type="text"
              placeholder="SEARCH INCIDENTS..."
              className="w-full pl-10 pr-4 py-2 bg-military-panel border border-military-border text-military-white font-military-body focus:outline-none focus:border-military-accent"
              style={{ clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' }}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search size={16} className="text-military-accent" />
            </div>
            <button type="submit" className="hidden">Search</button>
          </div>
        </form>
      </div>

      {showFilterMenu && (
        <div className="mb-3 p-3 bg-military-panel border border-military-border"
             style={{ clipPath: 'polygon(0 0, calc(100% - 8px) 0, 100% 8px, 100% 100%, 8px 100%, 0 calc(100% - 8px))' }}>
          <h4 className="text-sm font-military-heading text-military-white uppercase mb-2">FILTER PARAMETERS</h4>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="text-xs font-military-body text-military-white opacity-80">TYPE</label>
              <select
                className="block w-full mt-1 text-xs font-military-body uppercase bg-military-navy border border-military-border text-military-white focus:outline-none focus:border-military-accent"
                style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                value={filter.types ? filter.types[0] : ''}
                onChange={(e) => setFilter({ types: e.target.value ? [e.target.value as IncidentType] : undefined })}
              >
                <option value="">ALL TYPES</option>
                {(Object.values(IncidentType) as string[]).map(type => (
                  <option key={type} value={type}>{type.replace('TS_', '')}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="text-xs font-military-body text-military-white opacity-80">ACTION</label>
              <select
                className="block w-full mt-1 text-xs font-military-body uppercase bg-military-navy border border-military-border text-military-white focus:outline-none focus:border-military-accent"
                style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                value={filter.actions ? filter.actions[0] : ''}
                onChange={(e) => setFilter({ actions: e.target.value ? [e.target.value as ActionType] : undefined })}
              >
                <option value="">ALL ACTIONS</option>
                {(Object.values(ActionType) as string[]).filter(action => action !== ActionType.NONE).map(action => (
                  <option key={action} value={action}>{action.replace('_', ' ')}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="text-xs font-military-body text-military-white opacity-80">SEVERITY</label>
              <select
                className="block w-full mt-1 text-xs font-military-body uppercase bg-military-navy border border-military-border text-military-white focus:outline-none focus:border-military-accent"
                style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                value={filter.severities ? filter.severities[0] : ''}
                onChange={(e) => setFilter({ severities: e.target.value ? [e.target.value as IncidentSeverity] : undefined })}
              >
                <option value="">ALL SEVERITIES</option>
                {(Object.values(IncidentSeverity) as string[]).map(severity => (
                  <option key={severity} value={severity}>{severity}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="text-xs font-military-body text-military-white opacity-80">STATUS</label>
              <select
                className="block w-full mt-1 text-xs font-military-body uppercase bg-military-navy border border-military-border text-military-white focus:outline-none focus:border-military-accent"
                style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                value={filter.statuses ? filter.statuses[0] : ''}
                onChange={(e) => setFilter({ statuses: e.target.value ? [e.target.value as IncidentStatus] : undefined })}
              >
                <option value="">ALL STATUSES</option>
                {(Object.values(IncidentStatus) as string[]).map(status => (
                  <option key={status} value={status}>{status}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="flex justify-end mt-3">
            <Button
              size="sm"
              variant="military"
              className="military-btn"
              onClick={() => {
                setFilter({
                  types: undefined,
                  actions: undefined,
                  severities: undefined,
                  statuses: undefined,
                  searchTerm: undefined
                });
                setSearchTerm('');
              }}
            >
              RESET
            </Button>
          </div>
        </div>
      )}

      <div className="flex-grow overflow-hidden">
        {filteredIncidents.length === 0 ? (
          <div className="flex items-center justify-center h-40 military-container p-4">
            <p className="text-military-white font-military-body">NO INCIDENTS FOUND IN CURRENT PARAMETERS</p>
          </div>
        ) : (
          <div className="divide-y divide-military-border overflow-y-scroll overflow-x-hidden pr-1 military-scrollbar" style={{ height: "calc(100vh - 300px)" }}>
            {reorderedIncidents.map((incident, index) => (
              <IncidentItem
                key={`${incident.id}-${index}`}
                incident={incident}
                onClick={() => handleIncidentClick(incident.id)}
              />
            ))}
          </div>
        )}
      </div>
    </Card>
  );
};

export default IncidentsList;