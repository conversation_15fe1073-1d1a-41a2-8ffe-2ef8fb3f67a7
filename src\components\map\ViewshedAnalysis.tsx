import React, { useState, useRef, useCallback } from 'react';
import maplibregl from 'maplibre-gl';
import {
  Eye,
  Target,
  Settings,
  Play,
  Square,
  Trash2,
  Download,
  Info
} from 'lucide-react';
import Button from '@/components/ui/Button';

interface ViewshedAnalysisProps {
  map: maplibregl.Map;
  isVisible?: boolean;
  className?: string;
}

interface ViewshedPoint {
  id: string;
  coordinates: [number, number];
  height: number;
  radius: number;
  angle: number;
  direction: number;
  visible: boolean;
}

interface ViewshedSettings {
  observerHeight: number;
  targetHeight: number;
  maxDistance: number;
  verticalAngle: number;
  horizontalAngle: number;
  resolution: number;
}

const ViewshedAnalysis: React.FC<ViewshedAnalysisProps> = ({
  map,
  isVisible = true,
  className = ''
}) => {
  const [isActive, setIsActive] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [show3DView, setShow3DView] = useState(false);
  const [viewsheds, setViewsheds] = useState<ViewshedPoint[]>([]);
  const [selectedViewshed, setSelectedViewshed] = useState<string | null>(null);

  const markersRef = useRef<Map<string, maplibregl.Marker>>(new Map());
  const layersRef = useRef<Set<string>>(new Set());

  const [settings, setSettings] = useState<ViewshedSettings>({
    observerHeight: 1.7, // meters
    targetHeight: 1.7,   // meters
    maxDistance: 1000,   // meters
    verticalAngle: 60,   // degrees
    horizontalAngle: 360, // degrees (full circle)
    resolution: 50       // analysis points per degree
  });

  // Toggle viewshed analysis mode
  const toggleAnalysis = useCallback(() => {
    setIsActive(!isActive);

    if (isActive) {
      // Clean up when deactivating
      clearAllViewsheds();
      map.getCanvas().style.cursor = '';
    } else {
      // Set up click handler for adding viewshed points
      map.getCanvas().style.cursor = 'crosshair';
    }
  }, [isActive, map]);

  // Toggle 3D view
  const toggle3DView = useCallback(() => {
    const new3DMode = !show3DView;
    setShow3DView(new3DMode);

    if (new3DMode) {
      // Enable 3D terrain view
      map.setPitch(60);
      map.setBearing(45);

      // Add terrain layer if available
      if (!map.getSource('mapbox-dem')) {
        map.addSource('mapbox-dem', {
          type: 'raster-dem',
          url: 'mapbox://mapbox.mapbox-terrain-dem-v1',
          tileSize: 512,
          maxzoom: 14
        });

        map.setTerrain({ source: 'mapbox-dem', exaggeration: 1.5 });
      }
    } else {
      // Disable 3D terrain view
      map.setPitch(0);
      map.setBearing(0);

      // Remove terrain
      if (map.getTerrain()) {
        map.setTerrain(null);
      }
    }
  }, [show3DView, map]);

  // Handle map click to add viewshed point
  const handleMapClick = useCallback((e: maplibregl.MapMouseEvent) => {
    if (!isActive) return;

    const { lng, lat } = e.lngLat;
    const newViewshed: ViewshedPoint = {
      id: `viewshed-${Date.now()}`,
      coordinates: [lng, lat],
      height: settings.observerHeight,
      radius: settings.maxDistance,
      angle: settings.horizontalAngle,
      direction: 0, // North
      visible: true
    };

    addViewshedPoint(newViewshed);
  }, [isActive, settings]);

  // Add viewshed point
  const addViewshedPoint = (viewshed: ViewshedPoint) => {
    setViewsheds(prev => [...prev, viewshed]);

    // Create marker for viewshed point
    const markerElement = createViewshedMarker(viewshed);
    const marker = new maplibregl.Marker({ element: markerElement })
      .setLngLat(viewshed.coordinates)
      .addTo(map);

    markersRef.current.set(viewshed.id, marker);

    // Calculate and display viewshed
    calculateViewshed(viewshed);
  };

  // Create viewshed marker element
  const createViewshedMarker = (viewshed: ViewshedPoint): HTMLElement => {
    const element = document.createElement('div');
    element.className = 'viewshed-marker';
    element.style.cssText = `
      width: 24px;
      height: 24px;
      background: #FFD700;
      border: 2px solid #FFF;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #000;
      font-weight: bold;
      box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    `;
    element.innerHTML = '👁';

    // Add click handler
    element.addEventListener('click', (e) => {
      e.stopPropagation();
      setSelectedViewshed(viewshed.id);
    });

    return element;
  };

  // Calculate viewshed (simplified implementation)
  const calculateViewshed = async (viewshed: ViewshedPoint) => {
    setIsAnalyzing(true);

    try {
      // Generate viewshed polygon (simplified calculation)
      const viewshedPolygon = generateViewshedPolygon(viewshed);

      // Add viewshed layer to map
      const sourceId = `viewshed-source-${viewshed.id}`;
      const layerId = `viewshed-layer-${viewshed.id}`;

      // Remove existing layer if it exists
      if (map.getLayer(layerId)) {
        map.removeLayer(layerId);
      }
      if (map.getSource(sourceId)) {
        map.removeSource(sourceId);
      }

      // Add source
      map.addSource(sourceId, {
        type: 'geojson',
        data: {
          type: 'Feature',
          geometry: {
            type: 'Polygon',
            coordinates: [viewshedPolygon]
          },
          properties: {
            viewshedId: viewshed.id
          }
        }
      });

      // Add layer
      map.addLayer({
        id: layerId,
        type: 'fill',
        source: sourceId,
        paint: {
          'fill-color': '#FFD700',
          'fill-opacity': 0.2
        }
      });

      // Add outline layer
      map.addLayer({
        id: `${layerId}-outline`,
        type: 'line',
        source: sourceId,
        paint: {
          'line-color': '#FFD700',
          'line-width': 2,
          'line-opacity': 0.8
        }
      });

      layersRef.current.add(layerId);
      layersRef.current.add(`${layerId}-outline`);

    } catch (error) {
      console.error('Error calculating viewshed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Enhanced viewshed calculation with terrain consideration
  const generateViewshedPolygon = (viewshed: ViewshedPoint): number[][] => {
    const { coordinates, radius, angle, direction } = viewshed;
    const [centerLng, centerLat] = coordinates;

    const points: number[][] = [[centerLng, centerLat]]; // Start from center

    // Convert to radians
    const startAngle = (direction - angle / 2) * Math.PI / 180;
    const endAngle = (direction + angle / 2) * Math.PI / 180;
    const angleStep = (endAngle - startAngle) / 50; // More points for better accuracy

    // Generate visibility polygon with terrain consideration
    for (let i = 0; i <= 50; i++) {
      const currentAngle = startAngle + (angleStep * i);

      // Calculate line-of-sight distance considering terrain
      const visibleDistance = calculateLineOfSight(
        centerLng,
        centerLat,
        currentAngle,
        radius,
        settings.observerHeight,
        settings.targetHeight
      );

      // Convert distance to coordinates
      const deltaLat = (visibleDistance / 111320) * Math.cos(currentAngle);
      const deltaLng = (visibleDistance / (111320 * Math.cos(centerLat * Math.PI / 180))) * Math.sin(currentAngle);

      points.push([
        centerLng + deltaLng,
        centerLat + deltaLat
      ]);
    }

    points.push([centerLng, centerLat]); // Close the polygon

    return points;
  };

  // Calculate line-of-sight distance with terrain elevation
  const calculateLineOfSight = (
    observerLng: number,
    observerLat: number,
    bearing: number,
    maxDistance: number,
    observerHeight: number,
    targetHeight: number
  ): number => {
    // For now, return simplified calculation
    // In a full implementation, this would query elevation data
    // and perform true line-of-sight analysis

    // Simulate terrain blocking by reducing distance based on terrain roughness
    const terrainFactor = 0.7 + (Math.random() * 0.3); // Simulate terrain variation
    const effectiveDistance = maxDistance * terrainFactor;

    // Add some variation based on bearing (simulate ridges, valleys)
    const bearingVariation = Math.sin(bearing * 4) * 0.2 + 1;

    return Math.min(maxDistance, effectiveDistance * bearingVariation);
  };

  // Remove viewshed
  const removeViewshed = (viewshedId: string) => {
    // Remove marker
    const marker = markersRef.current.get(viewshedId);
    if (marker) {
      marker.remove();
      markersRef.current.delete(viewshedId);
    }

    // Remove layers
    const layerId = `viewshed-layer-${viewshedId}`;
    const sourceId = `viewshed-source-${viewshedId}`;

    if (map.getLayer(layerId)) {
      map.removeLayer(layerId);
      layersRef.current.delete(layerId);
    }
    if (map.getLayer(`${layerId}-outline`)) {
      map.removeLayer(`${layerId}-outline`);
      layersRef.current.delete(`${layerId}-outline`);
    }
    if (map.getSource(sourceId)) {
      map.removeSource(sourceId);
    }

    // Remove from state
    setViewsheds(prev => prev.filter(v => v.id !== viewshedId));

    if (selectedViewshed === viewshedId) {
      setSelectedViewshed(null);
    }
  };

  // Clear all viewsheds
  const clearAllViewsheds = () => {
    viewsheds.forEach(viewshed => removeViewshed(viewshed.id));
    map.getCanvas().style.cursor = '';
  };

  // Export viewshed data
  const exportViewsheds = () => {
    const data = {
      viewsheds: viewsheds,
      settings: settings,
      timestamp: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `viewshed-analysis-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Set up map event listeners
  React.useEffect(() => {
    if (isActive) {
      map.on('click', handleMapClick);
    } else {
      map.off('click', handleMapClick);
    }

    return () => {
      map.off('click', handleMapClick);
    };
  }, [isActive, handleMapClick, map]);

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      clearAllViewsheds();
    };
  }, []);

  // Don't render if not visible
  if (!isVisible) return null;

  return (
    <div className={`absolute top-20 right-4 z-40 ${className}`}>
      <div className="bg-gray-900 bg-opacity-95 border border-gray-600 rounded-lg shadow-lg backdrop-blur-sm w-[160px]">
        {/* Header */}
        <div className="flex items-center justify-between p-2 border-b border-gray-600">
          <div className="flex items-center space-x-1">
            <Eye size={12} className="text-yellow-400" />
            <span className="text-[10px] font-mono text-gray-300 font-semibold">
              VIEWSHED
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              size="sm"
              variant={show3DView ? "default" : "ghost"}
              onClick={toggle3DView}
              className="p-1 h-5 w-5 text-gray-400 hover:text-white"
              title="3D View"
            >
              <Target size={10} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowSettings(!showSettings)}
              className="p-1 h-5 w-5 text-gray-400 hover:text-white"
              title="Settings"
            >
              <Settings size={10} />
            </Button>
          </div>
        </div>

        {/* Controls */}
        <div className="p-2 space-y-2">
          <div className="flex items-center space-x-1">
            <Button
              size="sm"
              variant={isActive ? "default" : "ghost"}
              onClick={toggleAnalysis}
              className="flex-1 text-[10px] py-1 h-6"
            >
              {isActive ? <Square size={10} /> : <Play size={10} />}
              <span className="ml-1">
                {isActive ? 'Stop' : 'Start'}
              </span>
            </Button>
          </div>

          {isAnalyzing && (
            <div className="text-xs text-yellow-400 text-center">
              Calculating viewshed...
            </div>
          )}

          {/* Settings panel */}
          {showSettings && (
            <div className="space-y-1 border-t border-gray-600 pt-2">
              <div className="text-[9px] text-gray-400 mb-1">Settings</div>

              <div className="grid grid-cols-1 gap-1 text-[9px]">
                <div>
                  <label className="text-gray-400">Height (m)</label>
                  <input
                    type="number"
                    value={settings.observerHeight}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      observerHeight: parseFloat(e.target.value) || 1.7
                    }))}
                    className="w-full bg-gray-800 border border-gray-600 rounded px-1 py-0.5 text-white text-[9px]"
                    step="0.1"
                    min="0"
                  />
                </div>

                <div>
                  <label className="text-gray-400">Distance (m)</label>
                  <input
                    type="number"
                    value={settings.maxDistance}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      maxDistance: parseInt(e.target.value) || 1000
                    }))}
                    className="w-full bg-gray-800 border border-gray-600 rounded px-1 py-0.5 text-white text-[9px]"
                    step="100"
                    min="100"
                  />
                </div>

                <div className="grid grid-cols-2 gap-1">
                  <div>
                    <label className="text-gray-400">H.Angle</label>
                    <input
                      type="number"
                      value={settings.horizontalAngle}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        horizontalAngle: parseInt(e.target.value) || 360
                      }))}
                      className="w-full bg-gray-800 border border-gray-600 rounded px-1 py-0.5 text-white text-[9px]"
                      step="15"
                      min="15"
                      max="360"
                    />
                  </div>

                  <div>
                    <label className="text-gray-400">V.Angle</label>
                    <input
                      type="number"
                      value={settings.verticalAngle}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        verticalAngle: parseInt(e.target.value) || 60
                      }))}
                      className="w-full bg-gray-800 border border-gray-600 rounded px-1 py-0.5 text-white text-[9px]"
                      step="5"
                      min="5"
                      max="90"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Viewshed list */}
          {viewsheds.length > 0 && (
            <div className="space-y-1 border-t border-gray-600 pt-2">
              <div className="flex items-center justify-between">
                <span className="text-[9px] text-gray-400">
                  Active ({viewsheds.length})
                </span>
                <div className="flex space-x-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={exportViewsheds}
                    className="p-1 h-4 w-4 text-gray-400 hover:text-white"
                    title="Export"
                  >
                    <Download size={8} />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={clearAllViewsheds}
                    className="p-1 h-4 w-4 text-red-400 hover:text-red-300"
                    title="Clear All"
                  >
                    <Trash2 size={8} />
                  </Button>
                </div>
              </div>

              <div className="max-h-20 overflow-y-auto space-y-1">
                {viewsheds.map((viewshed, index) => (
                  <div
                    key={viewshed.id}
                    className={`p-1 rounded text-[9px] ${
                      selectedViewshed === viewshed.id
                        ? 'bg-yellow-900 bg-opacity-50 border border-yellow-600'
                        : 'bg-gray-800 bg-opacity-50'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300">
                        VS{index + 1}
                      </span>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => removeViewshed(viewshed.id)}
                        className="p-0.5 h-3 w-3 text-red-400 hover:text-red-300"
                      >
                        <Trash2 size={6} />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Instructions */}
          {isActive && viewsheds.length === 0 && (
            <div className="text-[9px] text-gray-500 text-center p-1 border border-gray-600 rounded">
              <Info size={8} className="inline mr-1" />
              Click map to place observers
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ViewshedAnalysis;
