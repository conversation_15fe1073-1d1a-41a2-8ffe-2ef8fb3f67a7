import { create } from 'zustand';
import { db } from '@/services/db/database';
import {
  Incident,
  IncidentFilter,
  IncidentStatistics,
  GeoStatistics
} from '@/types/incident';
import {
  getFilteredIncidents,
  generateIncidentStatistics,
  generateGeoStatistics
} from '@/services/analysis/incidentAnalytics';

// Custom filter function type
export type CustomFilterFn = (incident: Incident) => boolean;

// Extended filter interface with custom filter function
export interface ExtendedIncidentFilter extends IncidentFilter {
  customFilter?: CustomFilterFn;
}

interface IncidentState {
  incidents: Incident[];
  filteredIncidents: Incident[];
  selectedIncident: Incident | null;
  filter: ExtendedIncidentFilter;
  statistics: IncidentStatistics | null;
  geoStatistics: GeoStatistics | null;
  loading: boolean;
  showingForm: boolean;
  formLocation: { latitude: number; longitude: number } | null;
  loadIncidents: () => Promise<void>;
  setFilter: (filter: Partial<ExtendedIncidentFilter>) => void;
  resetFilter: () => void;
  selectIncident: (id: string | null) => Promise<void>;
  addIncident: (incident: Omit<Incident, 'id'>) => Promise<void>;
  updateIncident: (id: string, updates: Partial<Incident>) => Promise<void>;
  deleteIncident: (id: string) => Promise<void>;
  importIncidents: (csvData: string) => Promise<void>;
  showIncidentForm: (location?: { latitude: number; longitude: number }) => void;
  hideIncidentForm: () => void;
}

const defaultFilter: ExtendedIncidentFilter = {
  types: undefined,
  severities: undefined,
  statuses: undefined,
  actions: undefined,
  startDate: undefined,
  endDate: undefined,
  searchTerm: undefined,
  tags: undefined,
  customFilter: undefined
};

export const useIncidentStore = create<IncidentState>((set, get) => ({
  incidents: [],
  filteredIncidents: [],
  selectedIncident: null,
  filter: defaultFilter,
  statistics: null,
  geoStatistics: null,
  loading: false,
  showingForm: false,
  formLocation: null,

  loadIncidents: async () => {
    set({ loading: true });
    try {
      const incidents = await db.incidents.toArray();
      set({ incidents });

      // Apply current filter
      const filter = get().filter;
      const filtered = await getFilteredIncidents(filter);
      const stats = await generateIncidentStatistics(filtered);
      const geoStats = await generateGeoStatistics(filtered);

      set({
        filteredIncidents: filtered,
        statistics: stats,
        geoStatistics: geoStats
      });
    } catch (error) {
      console.error('Failed to load incidents:', error);
    } finally {
      set({ loading: false });
    }
  },

  setFilter: async (filterUpdate) => {
    set({ loading: true });
    try {
      const newFilter = { ...get().filter, ...filterUpdate };
      const filtered = await getFilteredIncidents(newFilter);
      const stats = await generateIncidentStatistics(filtered);
      const geoStats = await generateGeoStatistics(filtered);

      set({
        filter: newFilter,
        filteredIncidents: filtered,
        statistics: stats,
        geoStatistics: geoStats
      });
    } catch (error) {
      console.error('Failed to apply filter:', error);
    } finally {
      set({ loading: false });
    }
  },

  resetFilter: async () => {
    set({ loading: true });
    try {
      const incidents = get().incidents;
      const stats = await generateIncidentStatistics(incidents);
      const geoStats = await generateGeoStatistics(incidents);

      set({
        filter: defaultFilter,
        filteredIncidents: incidents,
        statistics: stats,
        geoStatistics: geoStats
      });
    } catch (error) {
      console.error('Failed to reset filter:', error);
    } finally {
      set({ loading: false });
    }
  },

  selectIncident: async (id) => {
    if (!id) {
      set({ selectedIncident: null });
      return;
    }

    try {
      const incident = await db.incidents.get(id);
      set({ selectedIncident: incident || null });
    } catch (error) {
      console.error('Failed to select incident:', error);
      set({ selectedIncident: null });
    }
  },

  addIncident: async (incidentData) => {
    set({ loading: true });
    try {
      const id = `INC-${Date.now()}`;
      const newIncident = { ...incidentData, id };

      await db.incidents.add(newIncident);
      await get().loadIncidents();
    } catch (error) {
      console.error('Failed to add incident:', error);
    } finally {
      set({ loading: false });
    }
  },

  updateIncident: async (id, updates) => {
    set({ loading: true });
    try {
      await db.incidents.update(id, updates);
      await get().loadIncidents();

      // If the updated incident is the selected one, refresh it
      if (get().selectedIncident?.id === id) {
        get().selectIncident(id);
      }
    } catch (error) {
      console.error('Failed to update incident:', error);
    } finally {
      set({ loading: false });
    }
  },

  deleteIncident: async (id) => {
    set({ loading: true });
    try {
      await db.incidents.delete(id);
      await get().loadIncidents();

      // If the deleted incident is the selected one, clear selection
      if (get().selectedIncident?.id === id) {
        set({ selectedIncident: null });
      }
    } catch (error) {
      console.error('Failed to delete incident:', error);
    } finally {
      set({ loading: false });
    }
  },

  importIncidents: async (csvData: string) => {
    set({ loading: true });
    try {
      const rows = csvData.split('\n');
      const headers = rows[0].split(',');

      const incidents: Omit<Incident, 'id'>[] = rows.slice(1).map(row => {
        const values = row.split(',');
        const incident: any = {};

        headers.forEach((header, index) => {
          const value = values[index]?.trim();
          if (value) {
            if (header === 'location') {
              const [lat, lng] = value.split(';').map(Number);
              incident.location = { latitude: lat, longitude: lng };
            } else if (header === 'tags') {
              incident.tags = value.split(';');
            } else if (header === 'reportedAt' || header === 'resolvedAt') {
              incident[header] = new Date(value);
            } else {
              incident[header] = value;
            }
          }
        });

        return incident;
      });

      // Add incidents in batches
      const batchSize = 50;
      for (let i = 0; i < incidents.length; i += batchSize) {
        const batch = incidents.slice(i, i + batchSize);
        await Promise.all(batch.map(incident => get().addIncident(incident)));
      }

      await get().loadIncidents();
    } catch (error) {
      console.error('Failed to import incidents:', error);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Show the incident form with optional location
  showIncidentForm: (location) => {
    set({
      showingForm: true,
      formLocation: location || null
    });
  },

  // Hide the incident form
  hideIncidentForm: () => {
    set({
      showingForm: false,
      formLocation: null
    });
  }
}));