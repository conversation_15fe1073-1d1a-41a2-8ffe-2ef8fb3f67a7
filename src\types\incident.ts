export interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface Incident {
  id: string;
  title: string;
  description: string;
  type: IncidentType;
  action?: ActionType;
  severity: IncidentSeverity;
  status: IncidentStatus;
  location: Coordinates;
  address: string;
  reportedAt: Date;
  resolvedAt?: Date;
  reportedBy: string;
  assignedTo?: string;
  attachments?: string[];
  tags?: string[];
}

export enum IncidentType {
  // Original types
  PHYSICAL_RAID = 'PHYSICAL_RAID',
  FIRE_RAID = 'FIRE_RAID',
  AMBUSH = 'AMBUSH',
  SNIPING = 'SNIPING',
  POST_OVERRUN = 'POST_OVERRUN',
  POST_FIRE = 'POST_FIRE',
  DEMONSTRATION = 'DEMONSTRATION',
  TARGET_KILLING = 'TARGET_KILLING',
  ARSON = 'ARSON',

  // TS (Tactical Situation) types used in other components
  TS_ACTIVITY = 'TS_ACTIVITY',
  TS_INFIL = 'TS_INFIL',
  TS_PRESENCE = 'TS_PRESENCE',
  TS_MOV = 'TS_MOV',
  TS_TASKEEL = 'TS_TASKEEL',
  TS_SB = 'TS_SB',
  TS_EXTORTION = 'TS_EXTORTION',
  TS_SUSPECT = 'TS_SUSPECT',
  TS_MEETING = 'TS_MEETING',
  TS_SEEN = 'TS_SEEN',
  TS_TGT_KILLING = 'TS_TGT_KILLING',
  TS_JIRGA = 'TS_JIRGA',

  OTHER = 'OTHER'
}

export enum ActionType {
  ADO = 'ADO', // Area Dominating Ops
  ASO = 'ASO', // Area Sanitization Ops
  IBO = 'IBO', // Int Based Ops
  SEARCH_OPS = 'SEARCH_OPS', // Search Ops
  SEARCH_AND_CLEARANCE = 'SEARCH_AND_CLEARANCE', // Search and Clearance Ops
  COMPOUND_SEARCH = 'COMPOUND_SEARCH', // Compound Search Ops
  CARDON_AND_SEARCH = 'CARDON_AND_SEARCH', // Cardon and Search Ops
  ROUTE_CLEARANCE = 'ROUTE_CLEARANCE', // Route Clearance
  ROUTE_PICQUETTING = 'ROUTE_PICQUETTING', // Route Picquetting
  ROUTE_PATROLLING = 'ROUTE_PATROLLING', // Route Patrolling
  ROUTE_RECCE = 'ROUTE_RECCE', // Route Recce
  IO_CAMPAIGN = 'IO_CAMPAIGN', // Information Ops Campaign
  CIMIC = 'CIMIC', // Civil Military Coord Ops
  QIPS = 'QIPS', // Quick Impact Projects
  AIR_OPS = 'AIR_OPS', // Air Ops
  DRONE_STRIKES = 'DRONE_STRIKES', // Drone Strikes
  ISR_MISSIONS = 'ISR_MISSIONS', // ISR Missions
  RESCUE_OPS = 'RESCUE_OPS', // Rescue Ops
  HOSTAGE_RESCUE = 'HOSTAGE_RESCUE', // Hostage Rescue Ops
  ROUTE_BD = 'ROUTE_BD', // Route BD
  LVL1_SE_CLEARANCE = 'LVL1_SE_CLEARANCE', // Lvl 1 S&E Clearance
  LVL2_SE_CLEARANCE = 'LVL2_SE_CLEARANCE', // Lvl 2 S&E Clearance
  LVL3_SE_CLEARANCE = 'LVL3_SE_CLEARANCE', // Lvl 3 S&E Clearance
  TECH_SWEEP = 'TECH_SWEEP', // Tech Sweep Ops
  NONE = 'NONE'
}

export enum IncidentSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export enum IncidentStatus {
  REPORTED = 'REPORTED',
  IN_PROGRESS = 'IN_PROGRESS',
  RESOLVED = 'RESOLVED',
  CLOSED = 'CLOSED'
}

export interface IncidentFilter {
  types?: IncidentType[];
  actions?: ActionType[];
  severities?: IncidentSeverity[];
  statuses?: IncidentStatus[];
  startDate?: Date;
  endDate?: Date;
  searchTerm?: string;
  tags?: string[];
}

export interface IncidentStatistics {
  total: number;
  byType: Record<string, number>;
  byAction: Record<ActionType, number>;
  bySeverity: Record<IncidentSeverity, number>;
  byStatus: Record<IncidentStatus, number>;
  byDay: Array<{ date: string; count: number }>;
  byMonth: Array<{ month: string; count: number }>;
  avgResolutionTime: number;
}

export interface GeoStatistics {
  hotspots: Array<{ location: Coordinates; weight: number }>;
  clustersByType: Record<string, Array<{ location: Coordinates; count: number }>>;
}

export interface Response {
  id: string;
  title: string;
  description: string;
  type: ActionType;
  status: ResponseStatus;
  location: Coordinates;
  address: string;
  startDate: Date;
  endDate?: Date;
  commander: string;
  units: string[];
  relatedIncidents?: string[]; // IDs of related incidents
  attachments?: string[];
  tags?: string[];
  // Additional fields for enhanced response form
  notes?: string;
  equipmentUsed?: string[];
  personnelCount?: number;
  objectives?: string[];
  outcome?: string;
  casualties?: number;
  successRate?: number;
  afterActionReport?: string;
}

export enum ResponseStatus {
  PLANNED = 'PLANNED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export interface ResponseFilter {
  types?: ActionType[];
  statuses?: ResponseStatus[];
  startDate?: Date;
  endDate?: Date;
  searchTerm?: string;
  tags?: string[];
}

export interface ResponseStatistics {
  total: number;
  byType: Record<ActionType, number>;
  byStatus: Record<ResponseStatus, number>;
  byDay: Array<{ date: string; count: number }>;
  byMonth: Array<{ month: string; count: number }>;
  avgDuration: number;
}