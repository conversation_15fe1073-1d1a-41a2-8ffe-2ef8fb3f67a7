import * as XLSX from 'xlsx';
import { Incident, Response } from '@/types/incident';
import {
  detectCoordinateFormat,
  CoordinateFormat,
  parseCoordinate,
  isValidCoordinate
} from './coordinateUtils';

export interface ValidationIssue {
  type: 'error' | 'warning';
  field?: string;
  message: string;
  rowIndex?: number;
  suggestion?: string;
}

export interface ValidationResult {
  isValid: boolean;
  issues: ValidationIssue[];
  stats: {
    totalRows: number;
    validRows: number;
    invalidRows: number;
    missingRequiredFields: string[];
    hasLocationData: boolean;
    coordinateFormat?: CoordinateFormat;
    needsCoordinateConversion?: boolean;
  };
  fileType: 'csv' | 'excel' | 'unknown';
}

/**
 * Analyzes a file to determine if it can be imported as incidents
 * @param file The file to analyze
 * @returns A promise that resolves to a validation result
 */
export const analyzeIncidentFile = async (file: File): Promise<ValidationResult> => {
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  const result: ValidationResult = {
    isValid: false,
    issues: [],
    stats: {
      totalRows: 0,
      validRows: 0,
      invalidRows: 0,
      missingRequiredFields: [],
      hasLocationData: false,
      coordinateFormat: CoordinateFormat.UNKNOWN,
      needsCoordinateConversion: false
    },
    fileType: 'unknown'
  };

  try {
    if (fileExtension === 'csv') {
      result.fileType = 'csv';
      const text = await readFileAsText(file);
      return analyzeIncidentCSV(text, result);
    } else if (fileExtension === 'xlsx' || fileExtension === 'xls') {
      result.fileType = 'excel';
      const arrayBuffer = await readFileAsArrayBuffer(file);
      return analyzeIncidentExcel(arrayBuffer, result);
    } else {
      result.issues.push({
        type: 'error',
        message: `Unsupported file type: ${fileExtension}. Only CSV and Excel files are supported.`,
        suggestion: 'Please convert your file to CSV or Excel format.'
      });
      return result;
    }
  } catch (error) {
    result.issues.push({
      type: 'error',
      message: `Failed to analyze file: ${error instanceof Error ? error.message : String(error)}`,
      suggestion: 'Please check if the file is corrupted or try a different file.'
    });
    return result;
  }
};

/**
 * Analyzes a file to determine if it can be imported as responses
 * @param file The file to analyze
 * @returns A promise that resolves to a validation result
 */
export const analyzeResponseFile = async (file: File): Promise<ValidationResult> => {
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  const result: ValidationResult = {
    isValid: false,
    issues: [],
    stats: {
      totalRows: 0,
      validRows: 0,
      invalidRows: 0,
      missingRequiredFields: [],
      hasLocationData: false,
      coordinateFormat: CoordinateFormat.UNKNOWN,
      needsCoordinateConversion: false
    },
    fileType: 'unknown'
  };

  try {
    if (fileExtension === 'csv') {
      result.fileType = 'csv';
      const text = await readFileAsText(file);
      return analyzeResponseCSV(text, result);
    } else if (fileExtension === 'xlsx' || fileExtension === 'xls') {
      result.fileType = 'excel';
      const arrayBuffer = await readFileAsArrayBuffer(file);
      return analyzeResponseExcel(arrayBuffer, result);
    } else {
      result.issues.push({
        type: 'error',
        message: `Unsupported file type: ${fileExtension}. Only CSV and Excel files are supported.`,
        suggestion: 'Please convert your file to CSV or Excel format.'
      });
      return result;
    }
  } catch (error) {
    result.issues.push({
      type: 'error',
      message: `Failed to analyze file: ${error instanceof Error ? error.message : String(error)}`,
      suggestion: 'Please check if the file is corrupted or try a different file.'
    });
    return result;
  }
};

/**
 * Analyzes a CSV file for incident import compatibility
 * @param csvData The CSV data as a string
 * @param result The validation result to update
 * @returns The updated validation result
 */
const analyzeIncidentCSV = (csvData: string, result: ValidationResult): ValidationResult => {
  // Required fields for incidents
  const requiredFields = ['title', 'description', 'type', 'severity', 'status', 'address'];

  // Parse CSV
  const rows = csvData.split('\n').filter(row => row.trim());
  if (rows.length < 2) {
    result.issues.push({
      type: 'error',
      message: 'CSV file must contain at least a header row and one data row',
      suggestion: 'Please add data to your file or use a different file.'
    });
    return result;
  }

  // Parse headers
  const headers = rows[0].split(',').map(header => header.trim().replace(/^"(.*)"$/, '$1'));

  // Check for required fields
  const missingFields = requiredFields.filter(field => !headers.includes(field));
  if (missingFields.length > 0) {
    result.issues.push({
      type: 'error',
      message: `Missing required fields: ${missingFields.join(', ')}`,
      suggestion: 'Add these columns to your CSV file or use the template provided.'
    });
    result.stats.missingRequiredFields = missingFields;
  }

  // Check for location data
  result.stats.hasLocationData = headers.includes('latitude') && headers.includes('longitude');
  if (!result.stats.hasLocationData) {
    result.issues.push({
      type: 'warning',
      message: 'Missing location data (latitude/longitude)',
      suggestion: 'Add latitude and longitude columns for proper map display.'
    });
  }

  // Analyze each row
  result.stats.totalRows = rows.length - 1; // Exclude header row
  let validRows = 0;
  let invalidRows = 0;

  for (let i = 1; i < rows.length; i++) {
    if (!rows[i].trim()) continue;

    // Check for potential issues in this row
    const rowIssues = analyzeCSVRow(rows[i], headers, i, requiredFields);

    if (rowIssues.length > 0) {
      result.issues.push(...rowIssues);
      invalidRows++;
    } else {
      validRows++;
    }
  }

  result.stats.validRows = validRows;
  result.stats.invalidRows = invalidRows;
  result.isValid = missingFields.length === 0 && validRows > 0;

  return result;
};

/**
 * Analyzes a CSV file for response import compatibility
 * @param csvData The CSV data as a string
 * @param result The validation result to update
 * @returns The updated validation result
 */
const analyzeResponseCSV = (csvData: string, result: ValidationResult): ValidationResult => {
  // Required fields for responses
  const requiredFields = ['title', 'description', 'type', 'status', 'address', 'commander'];

  // Parse CSV
  const rows = csvData.split('\n').filter(row => row.trim());
  if (rows.length < 2) {
    result.issues.push({
      type: 'error',
      message: 'CSV file must contain at least a header row and one data row',
      suggestion: 'Please add data to your file or use a different file.'
    });
    return result;
  }

  // Parse headers
  const headers = rows[0].split(',').map(header => header.trim().replace(/^"(.*)"$/, '$1'));

  // Check for required fields
  const missingFields = requiredFields.filter(field => !headers.includes(field));
  if (missingFields.length > 0) {
    result.issues.push({
      type: 'error',
      message: `Missing required fields: ${missingFields.join(', ')}`,
      suggestion: 'Add these columns to your CSV file or use the template provided.'
    });
    result.stats.missingRequiredFields = missingFields;
  }

  // Check for location data
  result.stats.hasLocationData = headers.includes('latitude') && headers.includes('longitude');
  if (!result.stats.hasLocationData) {
    result.issues.push({
      type: 'warning',
      message: 'Missing location data (latitude/longitude)',
      suggestion: 'Add latitude and longitude columns for proper map display.'
    });
  }

  // Analyze each row
  result.stats.totalRows = rows.length - 1; // Exclude header row
  let validRows = 0;
  let invalidRows = 0;

  for (let i = 1; i < rows.length; i++) {
    if (!rows[i].trim()) continue;

    // Check for potential issues in this row
    const rowIssues = analyzeCSVRow(rows[i], headers, i, requiredFields);

    if (rowIssues.length > 0) {
      result.issues.push(...rowIssues);
      invalidRows++;
    } else {
      validRows++;
    }
  }

  result.stats.validRows = validRows;
  result.stats.invalidRows = invalidRows;
  result.isValid = missingFields.length === 0 && validRows > 0;

  return result;
};

/**
 * Analyzes an Excel file for incident import compatibility
 * @param data The Excel file data as an ArrayBuffer
 * @param result The validation result to update
 * @returns The updated validation result
 */
const analyzeIncidentExcel = (data: ArrayBuffer, result: ValidationResult): ValidationResult => {
  // Required fields for incidents
  const requiredFields = ['title', 'description', 'type', 'severity', 'status', 'address'];

  // Read the Excel file
  const workbook = XLSX.read(data, { type: 'array' });

  // Check if there are any sheets
  if (workbook.SheetNames.length === 0) {
    result.issues.push({
      type: 'error',
      message: 'Excel file does not contain any sheets',
      suggestion: 'Please add data to your Excel file or use a different file.'
    });
    return result;
  }

  // Get the first worksheet
  const worksheet = workbook.Sheets[workbook.SheetNames[0]];

  // Convert worksheet to JSON
  const jsonData = XLSX.utils.sheet_to_json(worksheet);

  // Check if there is any data
  if (jsonData.length === 0) {
    result.issues.push({
      type: 'error',
      message: 'Excel file does not contain any data',
      suggestion: 'Please add data to your Excel file or use a different file.'
    });
    return result;
  }

  // Check for required fields
  const firstRow = jsonData[0] as Record<string, any>;
  const headers = Object.keys(firstRow);
  const missingFields = requiredFields.filter(field => !headers.includes(field));

  if (missingFields.length > 0) {
    result.issues.push({
      type: 'error',
      message: `Missing required fields: ${missingFields.join(', ')}`,
      suggestion: 'Add these columns to your Excel file or use the template provided.'
    });
    result.stats.missingRequiredFields = missingFields;
  }

  // Check for location data
  result.stats.hasLocationData = headers.includes('latitude') && headers.includes('longitude');
  if (!result.stats.hasLocationData) {
    result.issues.push({
      type: 'warning',
      message: 'Missing location data (latitude/longitude)',
      suggestion: 'Add latitude and longitude columns for proper map display.'
    });
  }

  // Analyze each row
  result.stats.totalRows = jsonData.length;
  let validRows = 0;
  let invalidRows = 0;

  jsonData.forEach((row, index) => {
    // Check for potential issues in this row
    const rowIssues = analyzeExcelRow(row as Record<string, any>, requiredFields, index + 1);

    if (rowIssues.length > 0) {
      result.issues.push(...rowIssues);
      invalidRows++;
    } else {
      validRows++;
    }
  });

  result.stats.validRows = validRows;
  result.stats.invalidRows = invalidRows;
  result.isValid = missingFields.length === 0 && validRows > 0;

  return result;
};

/**
 * Analyzes an Excel file for response import compatibility
 * @param data The Excel file data as an ArrayBuffer
 * @param result The validation result to update
 * @returns The updated validation result
 */
const analyzeResponseExcel = (data: ArrayBuffer, result: ValidationResult): ValidationResult => {
  // Required fields for responses
  const requiredFields = ['title', 'description', 'type', 'status', 'address', 'commander'];

  // Read the Excel file
  const workbook = XLSX.read(data, { type: 'array' });

  // Check if there are any sheets
  if (workbook.SheetNames.length === 0) {
    result.issues.push({
      type: 'error',
      message: 'Excel file does not contain any sheets',
      suggestion: 'Please add data to your Excel file or use a different file.'
    });
    return result;
  }

  // Get the first worksheet
  const worksheet = workbook.Sheets[workbook.SheetNames[0]];

  // Convert worksheet to JSON
  const jsonData = XLSX.utils.sheet_to_json(worksheet);

  // Check if there is any data
  if (jsonData.length === 0) {
    result.issues.push({
      type: 'error',
      message: 'Excel file does not contain any data',
      suggestion: 'Please add data to your Excel file or use a different file.'
    });
    return result;
  }

  // Check for required fields
  const firstRow = jsonData[0] as Record<string, any>;
  const headers = Object.keys(firstRow);
  const missingFields = requiredFields.filter(field => !headers.includes(field));

  if (missingFields.length > 0) {
    result.issues.push({
      type: 'error',
      message: `Missing required fields: ${missingFields.join(', ')}`,
      suggestion: 'Add these columns to your Excel file or use the template provided.'
    });
    result.stats.missingRequiredFields = missingFields;
  }

  // Check for location data
  result.stats.hasLocationData = headers.includes('latitude') && headers.includes('longitude');
  if (!result.stats.hasLocationData) {
    result.issues.push({
      type: 'warning',
      message: 'Missing location data (latitude/longitude)',
      suggestion: 'Add latitude and longitude columns for proper map display.'
    });
  }

  // Analyze each row
  result.stats.totalRows = jsonData.length;
  let validRows = 0;
  let invalidRows = 0;

  jsonData.forEach((row, index) => {
    // Check for potential issues in this row
    const rowIssues = analyzeExcelRow(row as Record<string, any>, requiredFields, index + 1);

    if (rowIssues.length > 0) {
      result.issues.push(...rowIssues);
      invalidRows++;
    } else {
      validRows++;
    }
  });

  result.stats.validRows = validRows;
  result.stats.invalidRows = invalidRows;
  result.isValid = missingFields.length === 0 && validRows > 0;

  return result;
};

/**
 * Analyzes a CSV row for potential issues
 * @param row The CSV row as a string
 * @param headers The CSV headers
 * @param rowIndex The row index (1-based)
 * @param requiredFields The required fields
 * @returns An array of validation issues
 */
const analyzeCSVRow = (row: string, headers: string[], rowIndex: number, requiredFields: string[]): ValidationIssue[] => {
  const issues: ValidationIssue[] = [];

  // Split the row by comma, but handle quoted values properly
  let values: string[] = [];
  let inQuotes = false;
  let currentValue = '';

  for (let j = 0; j < row.length; j++) {
    const char = row[j];

    if (char === '"' && (j === 0 || row[j-1] !== '\\')) {
      inQuotes = !inQuotes;
    } else if (char === ',' && !inQuotes) {
      values.push(currentValue);
      currentValue = '';
    } else {
      currentValue += char;
    }
  }

  // Add the last value
  values.push(currentValue);

  // Check if we have the right number of values
  if (values.length !== headers.length) {
    issues.push({
      type: 'error',
      message: `Row ${rowIndex} has ${values.length} values, expected ${headers.length}`,
      rowIndex,
      suggestion: 'Check for missing or extra commas in this row.'
    });
    return issues;
  }

  // Check for empty required fields
  headers.forEach((header, index) => {
    if (requiredFields.includes(header) && (!values[index] || values[index].trim() === '')) {
      issues.push({
        type: 'error',
        field: header,
        message: `Row ${rowIndex} is missing required field: ${header}`,
        rowIndex,
        suggestion: `Add a value for ${header} in this row.`
      });
    }
  });

  // Check for valid latitude/longitude if present
  const latIndex = headers.indexOf('latitude');
  const lngIndex = headers.indexOf('longitude');

  if (latIndex !== -1 && lngIndex !== -1) {
    const latValue = values[latIndex].trim();
    const lngValue = values[lngIndex].trim();

    // Detect coordinate format
    const latFormat = detectCoordinateFormat(latValue);
    const lngFormat = detectCoordinateFormat(lngValue);

    // Set coordinate format in the result if not already set
    if (!result.stats.coordinateFormat || result.stats.coordinateFormat === CoordinateFormat.UNKNOWN) {
      if (latFormat !== CoordinateFormat.UNKNOWN) {
        result.stats.coordinateFormat = latFormat;
      } else if (lngFormat !== CoordinateFormat.UNKNOWN) {
        result.stats.coordinateFormat = lngFormat;
      }
    }

    // Check if conversion is needed
    if (latFormat === CoordinateFormat.DMS || lngFormat === CoordinateFormat.DMS) {
      result.stats.needsCoordinateConversion = true;
    }

    // Parse coordinates
    let lat: number | null = null;
    let lng: number | null = null;

    if (latFormat === CoordinateFormat.DECIMAL_DEGREES) {
      lat = parseFloat(latValue);
    } else if (latFormat === CoordinateFormat.DMS) {
      lat = parseCoordinate(latValue);
      if (lat !== null) {
        issues.push({
          type: 'warning',
          field: 'latitude',
          message: `Row ${rowIndex} has DMS latitude: ${latValue} (converts to ${lat.toFixed(6)})`,
          rowIndex,
          suggestion: 'DMS coordinates will be automatically converted to decimal degrees during import.'
        });
      }
    } else {
      // Try to parse as decimal
      lat = parseFloat(latValue);
    }

    if (lngFormat === CoordinateFormat.DECIMAL_DEGREES) {
      lng = parseFloat(lngValue);
    } else if (lngFormat === CoordinateFormat.DMS) {
      lng = parseCoordinate(lngValue);
      if (lng !== null) {
        issues.push({
          type: 'warning',
          field: 'longitude',
          message: `Row ${rowIndex} has DMS longitude: ${lngValue} (converts to ${lng.toFixed(6)})`,
          rowIndex,
          suggestion: 'DMS coordinates will be automatically converted to decimal degrees during import.'
        });
      }
    } else {
      // Try to parse as decimal
      lng = parseFloat(lngValue);
    }

    // Validate coordinates
    if (lat === null || !isValidCoordinate(lat, true)) {
      issues.push({
        type: 'error',
        field: 'latitude',
        message: `Row ${rowIndex} has invalid latitude: ${latValue}`,
        rowIndex,
        suggestion: 'Latitude must be a number between -90 and 90 or a valid DMS coordinate (e.g., 40°26\'46"N).'
      });
    }

    if (lng === null || !isValidCoordinate(lng, false)) {
      issues.push({
        type: 'error',
        field: 'longitude',
        message: `Row ${rowIndex} has invalid longitude: ${lngValue}`,
        rowIndex,
        suggestion: 'Longitude must be a number between -180 and 180 or a valid DMS coordinate (e.g., 79°58\'56"W).'
      });
    }
  }

  return issues;
};

/**
 * Analyzes an Excel row for potential issues
 * @param row The Excel row as an object
 * @param requiredFields The required fields
 * @param rowIndex The row index (1-based)
 * @returns An array of validation issues
 */
const analyzeExcelRow = (row: Record<string, any>, requiredFields: string[], rowIndex: number): ValidationIssue[] => {
  const issues: ValidationIssue[] = [];

  // Check for empty required fields
  requiredFields.forEach(field => {
    if (row[field] === undefined || row[field] === null || row[field] === '') {
      issues.push({
        type: 'error',
        field,
        message: `Row ${rowIndex} is missing required field: ${field}`,
        rowIndex,
        suggestion: `Add a value for ${field} in this row.`
      });
    }
  });

  // Check for valid latitude/longitude if present
  if ('latitude' in row && 'longitude' in row) {
    const latValue = String(row.latitude).trim();
    const lngValue = String(row.longitude).trim();

    // Detect coordinate format
    const latFormat = detectCoordinateFormat(latValue);
    const lngFormat = detectCoordinateFormat(lngValue);

    // Set coordinate format in the result if not already set
    if (!result.stats.coordinateFormat || result.stats.coordinateFormat === CoordinateFormat.UNKNOWN) {
      if (latFormat !== CoordinateFormat.UNKNOWN) {
        result.stats.coordinateFormat = latFormat;
      } else if (lngFormat !== CoordinateFormat.UNKNOWN) {
        result.stats.coordinateFormat = lngFormat;
      }
    }

    // Check if conversion is needed
    if (latFormat === CoordinateFormat.DMS || lngFormat === CoordinateFormat.DMS) {
      result.stats.needsCoordinateConversion = true;
    }

    // Parse coordinates
    let lat: number | null = null;
    let lng: number | null = null;

    if (latFormat === CoordinateFormat.DECIMAL_DEGREES) {
      lat = parseFloat(latValue);
    } else if (latFormat === CoordinateFormat.DMS) {
      lat = parseCoordinate(latValue);
      if (lat !== null) {
        issues.push({
          type: 'warning',
          field: 'latitude',
          message: `Row ${rowIndex} has DMS latitude: ${latValue} (converts to ${lat.toFixed(6)})`,
          rowIndex,
          suggestion: 'DMS coordinates will be automatically converted to decimal degrees during import.'
        });
      }
    } else {
      // Try to parse as decimal
      lat = parseFloat(latValue);
    }

    if (lngFormat === CoordinateFormat.DECIMAL_DEGREES) {
      lng = parseFloat(lngValue);
    } else if (lngFormat === CoordinateFormat.DMS) {
      lng = parseCoordinate(lngValue);
      if (lng !== null) {
        issues.push({
          type: 'warning',
          field: 'longitude',
          message: `Row ${rowIndex} has DMS longitude: ${lngValue} (converts to ${lng.toFixed(6)})`,
          rowIndex,
          suggestion: 'DMS coordinates will be automatically converted to decimal degrees during import.'
        });
      }
    } else {
      // Try to parse as decimal
      lng = parseFloat(lngValue);
    }

    // Validate coordinates
    if (lat === null || !isValidCoordinate(lat, true)) {
      issues.push({
        type: 'error',
        field: 'latitude',
        message: `Row ${rowIndex} has invalid latitude: ${latValue}`,
        rowIndex,
        suggestion: 'Latitude must be a number between -90 and 90 or a valid DMS coordinate (e.g., 40°26\'46"N).'
      });
    }

    if (lng === null || !isValidCoordinate(lng, false)) {
      issues.push({
        type: 'error',
        field: 'longitude',
        message: `Row ${rowIndex} has invalid longitude: ${lngValue}`,
        rowIndex,
        suggestion: 'Longitude must be a number between -180 and 180 or a valid DMS coordinate (e.g., 79°58\'56"W).'
      });
    }
  }

  return issues;
};

/**
 * Reads a file as text
 * @param file The file to read
 * @returns A promise that resolves to the file contents as text
 */
const readFileAsText = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = e => {
      if (e.target?.result) {
        resolve(e.target.result as string);
      } else {
        reject(new Error('Failed to read file'));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsText(file);
  });
};

/**
 * Reads a file as an ArrayBuffer
 * @param file The file to read
 * @returns A promise that resolves to the file contents as an ArrayBuffer
 */
const readFileAsArrayBuffer = (file: File): Promise<ArrayBuffer> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = e => {
      if (e.target?.result) {
        resolve(e.target.result as ArrayBuffer);
      } else {
        reject(new Error('Failed to read file'));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsArrayBuffer(file);
  });
};
