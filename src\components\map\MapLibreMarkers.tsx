import React, { useEffect, useRef, useCallback } from 'react';
import maplibregl from 'maplibre-gl';
import { Incident, Response, IncidentType, ActionType } from '@/types/incident';
import { PerformanceConfig, MarkerPool, filterByViewport } from '@/utils/mapPerformance';
import { getSafeCoordinates } from '@/utils/mapUtils';
import { getIncidentSymbol, getResponseSymbol, createTacticalIcon, createResponseIcon } from './TacticalSymbols';

interface MapLibreMarkersProps {
  map: maplibregl.Map;
  incidents: Incident[];
  responses: Response[];
  onIncidentSelect: (incidentId: string) => void;
  onResponseSelect: (responseId: string) => void;
  markerPool: MarkerPool;
  performanceConfig: PerformanceConfig;
}

interface MarkerData {
  id: string;
  type: 'incident' | 'response';
  coordinates: [number, number];
  data: Incident | Response;
}

const MapLibreMarkers: React.FC<MapLibreMarkersProps> = ({
  map,
  incidents,
  responses,
  onIncidentSelect,
  onResponseSelect,
  markerPool,
  performanceConfig
}) => {
  const markersRef = useRef<Map<string, maplibregl.Marker>>(new Map());
  const popupRef = useRef<maplibregl.Popup | null>(null);
  const clusterSourceRef = useRef<boolean>(false);

  // Create enhanced tactical symbol for incidents
  const createIncidentSymbol = useCallback((incident: Incident): HTMLElement => {
    const size = Math.max(16, 16); // Minimum 16px size for visibility
    const symbolConfig = getIncidentSymbol(incident.type);

    // Create container with proper MapLibre-compatible styling
    const container = document.createElement('div');
    container.className = 'incident-marker tactical-symbol-stable';
    container.style.cssText = `
      width: ${size}px;
      height: ${size}px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.6));
      pointer-events: auto;
    `;

    // Create the enhanced tactical symbol using SVG
    const svgIcon = createTacticalIcon(incident.type, size);
    const svgElement = document.createElement('div');
    svgElement.innerHTML = svgIcon;
    svgElement.style.cssText = `
      width: ${size}px;
      height: ${size}px;
      display: flex;
      align-items: center;
      justify-content: center;
    `;

    container.appendChild(svgElement);

    // Add enhanced severity indicator with military styling
    const severityIndicator = document.createElement('div');
    severityIndicator.style.cssText = `
      position: absolute;
      top: -3px;
      right: -3px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: ${getSeverityColor(incident.severity)};
      border: 2px solid #000000;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
      z-index: 10;
    `;
    container.appendChild(severityIndicator);

    // Add hover effect
    container.addEventListener('mouseenter', () => {
      container.style.transform = 'scale(1.15)';
      container.style.zIndex = '1000';
    });

    container.addEventListener('mouseleave', () => {
      container.style.transform = 'scale(1)';
      container.style.zIndex = '10';
    });

    return container;
  }, []);

  // Create enhanced symbol for responses
  const createResponseSymbol = useCallback((response: Response): HTMLElement => {
    const size = Math.max(14, 14); // Minimum 14px size for responses, slightly smaller than incidents

    const container = document.createElement('div');
    container.className = 'response-marker tactical-symbol-stable';
    container.style.cssText = `
      width: ${size}px;
      height: ${size}px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.6));
      pointer-events: auto;
    `;

    // Create the enhanced response symbol using SVG
    const actionType = response.type || 'other' as ActionType;
    const svgIcon = createResponseIcon(actionType, size);
    const svgElement = document.createElement('div');
    svgElement.innerHTML = svgIcon;
    svgElement.style.cssText = `
      width: ${size}px;
      height: ${size}px;
      display: flex;
      align-items: center;
      justify-content: center;
    `;

    container.appendChild(svgElement);

    // Add enhanced status indicator with military styling
    const statusIndicator = document.createElement('div');
    statusIndicator.style.cssText = `
      position: absolute;
      bottom: -3px;
      left: -3px;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: ${getResponseStatusColor(response.status)};
      border: 2px solid #000000;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
      z-index: 10;
    `;
    container.appendChild(statusIndicator);

    // Add hover effect
    container.addEventListener('mouseenter', () => {
      container.style.transform = 'scale(1.15)';
      container.style.zIndex = '1000';
    });

    container.addEventListener('mouseleave', () => {
      container.style.transform = 'scale(1)';
      container.style.zIndex = '10';
    });

    return container;
  }, []);

  // Get severity color
  const getSeverityColor = (severity: string): string => {
    switch (severity) {
      case 'CRITICAL': return '#FF0000';
      case 'HIGH': return '#FF8800';
      case 'MEDIUM': return '#FFFF00';
      case 'LOW': return '#00FF00';
      default: return '#888888';
    }
  };

  // Get response status color
  const getResponseStatusColor = (status: string): string => {
    switch (status) {
      case 'ACTIVE': return '#00FF00';
      case 'COMPLETED': return '#0088FF';
      case 'CANCELLED': return '#FF0000';
      default: return '#888888';
    }
  };

  // Create popup content for incidents
  const createIncidentPopup = (incident: Incident): string => {
    const safeLocation = getSafeCoordinates(incident.location);
    return `
      <div class="tactical-popup">
        <div class="popup-header">
          <h3 class="text-sm font-bold text-white">${incident.type.replace('_', ' ').toUpperCase()}</h3>
          <span class="severity-badge severity-${incident.severity.toLowerCase()}">${incident.severity}</span>
        </div>
        <div class="popup-content">
          <div class="grid-info">
            <div><strong>GRID:</strong> ${safeLocation.latitude.toFixed(5)}, ${safeLocation.longitude.toFixed(5)}</div>
            <div><strong>DATE:</strong> ${new Date(incident.reportedAt).toLocaleDateString()}</div>
            <div><strong>STATUS:</strong> ${incident.status}</div>
          </div>
          <div class="description">
            <strong>DESCRIPTION:</strong><br>
            ${incident.description || 'No description available'}
          </div>
        </div>
        <button class="popup-button" onclick="window.selectIncident('${incident.id}')">
          VIEW DETAILS
        </button>
      </div>
    `;
  };

  // Create popup content for responses
  const createResponsePopup = (response: Response): string => {
    const safeLocation = getSafeCoordinates(response.location);
    return `
      <div class="tactical-popup">
        <div class="popup-header">
          <h3 class="text-sm font-bold text-white">RESPONSE OPERATION</h3>
          <span class="status-badge status-${response.status.toLowerCase()}">${response.status}</span>
        </div>
        <div class="popup-content">
          <div class="grid-info">
            <div><strong>GRID:</strong> ${safeLocation.latitude.toFixed(5)}, ${safeLocation.longitude.toFixed(5)}</div>
            <div><strong>COMMANDER:</strong> ${response.commander}</div>
            <div><strong>UNITS:</strong> ${response.unitsInvolved ? response.unitsInvolved.join(', ') : 'Not specified'}</div>
          </div>
          <div class="description">
            <strong>OBJECTIVE:</strong><br>
            ${response.objective || 'No objective specified'}
          </div>
        </div>
        <button class="popup-button" onclick="window.selectResponse('${response.id}')">
          VIEW DETAILS
        </button>
      </div>
    `;
  };

  // Update markers based on current data and viewport
  const updateMarkers = useCallback(() => {
    if (!map) return;

    console.log('MapLibreMarkers: updateMarkers called');
    console.log('Incidents count:', incidents.length);
    console.log('Responses count:', responses.length);

    // Debug: Log first few incidents to see their structure
    if (incidents.length > 0) {
      console.log('First incident:', incidents[0]);
    }
    if (responses.length > 0) {
      console.log('First response:', responses[0]);
    }

    // Combine incidents and responses into marker data
    const allMarkerData: MarkerData[] = [
      ...incidents.map(incident => {
        const originalCoords = incident.location;
        const safeCoords = getSafeCoordinates(incident.location);
        console.log(`Incident ${incident.id}:`, {
          original: originalCoords,
          safe: safeCoords,
          isValid: originalCoords && typeof originalCoords.latitude === 'number' && typeof originalCoords.longitude === 'number'
        });
        return {
          id: `incident-${incident.id}`,
          type: 'incident' as const,
          coordinates: [safeCoords.longitude, safeCoords.latitude] as [number, number],
          data: incident
        };
      }),
      ...responses.map(response => {
        const originalCoords = response.location;
        const safeCoords = getSafeCoordinates(response.location);
        console.log(`Response ${response.id}:`, {
          original: originalCoords,
          safe: safeCoords,
          isValid: originalCoords && typeof originalCoords.latitude === 'number' && typeof originalCoords.longitude === 'number'
        });
        return {
          id: `response-${response.id}`,
          type: 'response' as const,
          coordinates: [safeCoords.longitude, safeCoords.latitude] as [number, number],
          data: response
        };
      })
    ];

    console.log('Total marker data:', allMarkerData.length);

    // Always show all markers - disable viewport filtering to prevent movement issues
    // This ensures both incidents and responses are always displayed
    const visibleMarkerData = allMarkerData.map(m => m.data);

    console.log('Visible marker data (all markers):', visibleMarkerData.length);

    const visibleIds = new Set(allMarkerData.map(markerData => markerData.id));

    console.log('Visible IDs:', Array.from(visibleIds));

    // Remove markers that are no longer needed
    markersRef.current.forEach((marker, id) => {
      if (!visibleIds.has(id)) {
        marker.remove();
        markersRef.current.delete(id);
        // Skip marker pool to prevent reuse issues
      }
    });

    // Add or update visible markers
    let markersCreated = 0;
    allMarkerData.forEach(markerData => {
      if (!visibleIds.has(markerData.id)) {
        console.log(`Skipping marker ${markerData.id} - not in visible set`);
        return;
      }

      let marker = markersRef.current.get(markerData.id);

      if (!marker) {
        console.log(`Creating new marker for ${markerData.id} at [${markerData.coordinates}]`);

        // Create new marker
        const element = markerData.type === 'incident'
          ? createIncidentSymbol(markerData.data as Incident)
          : createResponseSymbol(markerData.data as Response);

        console.log('Created element:', element);

        marker = new maplibregl.Marker({
          element,
          anchor: 'bottom'  // Bottom anchor for precise coordinate positioning
        })
          .setLngLat(markerData.coordinates)
          .addTo(map);

        console.log('Added marker to map:', marker);
        markersCreated++;

        // Add click handler - only show popup, don't navigate directly
        element.addEventListener('click', (e) => {
          e.stopPropagation();

          // Show popup
          if (popupRef.current) {
            popupRef.current.remove();
          }

          const popupContent = markerData.type === 'incident'
            ? createIncidentPopup(markerData.data as Incident)
            : createResponsePopup(markerData.data as Response);

          popupRef.current = new maplibregl.Popup({
            closeButton: true,
            closeOnClick: false,
            className: 'tactical-popup-container'
          })
            .setLngLat(markerData.coordinates)
            .setHTML(popupContent)
            .addTo(map);
        });

        markersRef.current.set(markerData.id, marker);
      } else {
        // Only update position if coordinates have actually changed to prevent unnecessary movement
        const currentLngLat = marker.getLngLat();
        const [newLng, newLat] = markerData.coordinates;

        if (Math.abs(currentLngLat.lng - newLng) > 0.00001 || Math.abs(currentLngLat.lat - newLat) > 0.00001) {
          marker.setLngLat(markerData.coordinates);
        }
      }
    });

    console.log(`Markers created: ${markersCreated}, Total active markers: ${markersRef.current.size}`);
  }, [map, incidents, responses, onIncidentSelect, onResponseSelect, performanceConfig, createIncidentSymbol, createResponseSymbol]);

  // Set up global functions for popup buttons
  useEffect(() => {
    (window as any).selectIncident = onIncidentSelect;
    (window as any).selectResponse = onResponseSelect;

    return () => {
      delete (window as any).selectIncident;
      delete (window as any).selectResponse;
    };
  }, [onIncidentSelect, onResponseSelect]);

  // Update markers when data changes
  useEffect(() => {
    updateMarkers();
  }, [updateMarkers]);

  // Update markers on map move for performance optimization (debounced)
  useEffect(() => {
    if (!map) return;

    let timeoutId: NodeJS.Timeout;

    const handleMapMove = () => {
      // Debounce the update to prevent excessive re-rendering
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        updateMarkers();
      }, 200);
    };

    map.on('moveend', handleMapMove);
    map.on('zoomend', handleMapMove);

    return () => {
      clearTimeout(timeoutId);
      map.off('moveend', handleMapMove);
      map.off('zoomend', handleMapMove);
    };
  }, [map, updateMarkers]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      markersRef.current.forEach(marker => marker.remove());
      markersRef.current.clear();

      if (popupRef.current) {
        popupRef.current.remove();
      }
    };
  }, []);

  return null; // This component doesn't render anything directly
};

export default MapLibreMarkers;
