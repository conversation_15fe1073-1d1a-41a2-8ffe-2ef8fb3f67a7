import { Incident, IncidentSeverity, IncidentStatus, IncidentType, ActionType } from '@/types/incident';
import { format, subDays, addDays, addHours } from 'date-fns';

// Generate a set of mock incidents for demonstration
const generateMockIncidents = (): Incident[] => {
  const incidents: Incident[] = [];
  const now = new Date();

  // Pakistan cities for random locations
  const cities = [
    { name: 'North Waziristan', lat: 32.9889, lng: 69.9592 },
    { name: 'South Waziristan', lat: 32.3502, lng: 69.7128 },
    { name: '<PERSON><PERSON><PERSON>', lat: 34.1058, lng: 71.0857 },
    { name: 'Ku<PERSON><PERSON>', lat: 33.7946, lng: 70.1009 },
    { name: 'Orakzai', lat: 33.6707, lng: 70.9033 },
    { name: 'Mo<PERSON>d', lat: 34.4726, lng: 71.3103 },
    { name: '<PERSON><PERSON>', lat: 34.7717, lng: 71.5449 }
  ];

  // Generate 100 random incidents
  for (let i = 0; i < 100; i++) {
    const cityIndex = Math.floor(Math.random() * cities.length);
    const city = cities[cityIndex];

    // Random coordinates near the city center
    const latOffset = (Math.random() - 0.5) * 0.5;
    const lngOffset = (Math.random() - 0.5) * 0.5;

    const randomType = Object.values(IncidentType)[
      Math.floor(Math.random() * (Object.values(IncidentType).length - 1))
    ];

    const randomAction = Math.random() > 0.3 ? Object.values(ActionType)[
      Math.floor(Math.random() * (Object.values(ActionType).length - 1))
    ] : ActionType.NONE;

    const randomSeverity = Object.values(IncidentSeverity)[
      Math.floor(Math.random() * Object.values(IncidentSeverity).length)
    ];

    const randomStatus = Object.values(IncidentStatus)[
      Math.floor(Math.random() * Object.values(IncidentStatus).length)
    ];

    // Random date within the last 30 days
    const reportedAt = subDays(now, Math.floor(Math.random() * 30));

    // 70% chance of having a resolved date if status is RESOLVED or CLOSED
    let resolvedAt = undefined;
    if ((randomStatus === IncidentStatus.RESOLVED ||
         randomStatus === IncidentStatus.CLOSED) &&
        Math.random() < 0.7) {
      resolvedAt = addHours(reportedAt, Math.floor(Math.random() * 72) + 1);
    }

    incidents.push({
      id: `INC-${1000 + i}`,
      title: `${randomType.replace('_', ' ')} incident in ${city.name}`,
      description: `This is a ${randomSeverity.toLowerCase()} severity incident reported in ${city.name}.`,
      type: randomType,
      action: randomAction,
      severity: randomSeverity,
      status: randomStatus,
      location: {
        latitude: city.lat + latOffset,
        longitude: city.lng + lngOffset
      },
      address: `${city.name} District, Pakistan`,
      reportedAt: reportedAt,
      resolvedAt: resolvedAt,
      reportedBy: `User-${Math.floor(Math.random() * 10) + 1}`,
      assignedTo: Math.random() < 0.8 ? `Agent-${Math.floor(Math.random() * 5) + 1}` : undefined,
      tags: generateRandomTags(randomType),
      attachments: Math.random() < 0.4 ? [`report-${i}.pdf`] : undefined
    });
  }

  return incidents;
};

// Generate random tags based on incident type
const generateRandomTags = (type: IncidentType): string[] => {
  const tagsByType: Record<IncidentType, string[]> = {
    [IncidentType.PHYSICAL_RAID]: ['armed', 'unarmed', 'coordinated', 'surprise'],
    [IncidentType.FIRE_RAID]: ['mortar', 'rocket', 'small-arms', 'heavy-weapons'],
    [IncidentType.AMBUSH]: ['roadside', 'mountain', 'valley', 'coordinated'],
    [IncidentType.SNIPING]: ['long-range', 'urban', 'mountain', 'precision'],
    [IncidentType.POST_OVERRUN]: ['complete', 'partial', 'coordinated', 'night'],
    [IncidentType.POST_FIRE]: ['sustained', 'brief', 'harassing', 'covering'],
    [IncidentType.DEMONSTRATION]: ['violent', 'peaceful', 'organized', 'spontaneous'],
    [IncidentType.TARGET_KILLING]: ['civilian', 'military', 'political', 'tribal'],
    [IncidentType.ARSON]: ['building', 'vehicle', 'infrastructure', 'symbolic'],
    [IncidentType.OTHER]: ['misc', 'unclassified', 'pending', 'unusual']
  };

  // Default tags if the incident type is not found in the map
  const defaultTags = ['unclassified', 'misc', 'pending'];

  // Get available tags for this type, or use default tags if not found
  const availableTags = tagsByType[type] || defaultTags;
  const numTags = Math.floor(Math.random() * 3) + 1; // 1-3 tags

  const selectedTags: string[] = [];
  for (let i = 0; i < numTags; i++) {
    const randomTag = availableTags[Math.floor(Math.random() * availableTags.length)];
    if (!selectedTags.includes(randomTag)) {
      selectedTags.push(randomTag);
    }
  }

  return selectedTags;
};

export const mockIncidents = generateMockIncidents();