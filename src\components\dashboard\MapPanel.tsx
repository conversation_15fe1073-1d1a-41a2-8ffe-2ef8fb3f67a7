import React, { useEffect, useState, useRef } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ile<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  useMap,
  ScaleControl,
  useMapEvents
} from 'react-leaflet';
import {
  Icon,
  LatLng
} from 'leaflet';
import { isValidCoordinates, getSafeCoordinates } from '@/utils/mapUtils';
import {
  Layers,
  MapPin,
  Circle as CircleIcon,
  Trash2,
  Save,
  Download,
  Upload,
  Shield
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { useIncidentStore } from '@/store/incidentStore';
import { useResponseStore } from '@/store/responseStore';
import { useFilterStore } from '@/store/filterStore';
import { Incident, IncidentType, Response, ActionType } from '@/types/incident';
import { createGeoJsonFromIncidents } from '@/services/map/mapService';
import { checkLeafletPlugins } from '@/services/map/leafletPlugins';

// Import custom map components
import MarkerCluster from '@/components/map/MarkerCluster';
import LayerManager from '@/components/map/LayerManager';
import ExportTools from '@/components/map/ExportTools';
import MilitaryToolbar from '@/components/map/MilitaryToolbar';
import ControlContainer from '@/components/map/ControlContainer';
import { createTacticalIcon, createResponseIcon } from '@/components/map/TacticalSymbols';
import MilitaryTools from '@/components/map/MilitaryTools';
import TacticalLegend from '@/components/map/TacticalLegend';
import MapContextMenu from '@/components/map/MapContextMenu';

// Import Leaflet CSS
import 'leaflet/dist/leaflet.css';
// Import Leaflet plugins CSS
import 'leaflet-draw/dist/leaflet.draw.css';
import 'leaflet.fullscreen/Control.FullScreen.css';
import 'leaflet-measure/dist/leaflet-measure.css';
import 'leaflet.locatecontrol/dist/L.Control.Locate.css';

// Leaflet controls positioning CSS
const mapControlsStyle = `
  /* Fix for Leaflet controls positioning */
  .leaflet-top.leaflet-right {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-end !important;
  }

  .leaflet-top.leaflet-left {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
  }
`;

// Add style element to head
if (typeof document !== 'undefined') {
  const styleEl = document.createElement('style');
  styleEl.appendChild(document.createTextNode(mapControlsStyle));
  document.head.appendChild(styleEl);
}

// Import Leaflet plugins
import 'leaflet-draw';
import 'leaflet.fullscreen';
import 'leaflet-measure';
import 'leaflet.locatecontrol';
import 'leaflet.markercluster';
import 'leaflet-routing-machine';
import 'leaflet.heat';

// Fix for default marker icons
import markerIcon2x from 'leaflet/dist/images/marker-icon-2x.png';
import markerIcon from 'leaflet/dist/images/marker-icon.png';
import markerShadow from 'leaflet/dist/images/marker-shadow.png';

// Fix for default marker icons
delete (Icon.Default.prototype as any)._getIconUrl;
Icon.Default.mergeOptions({
  iconUrl: markerIcon,
  iconRetinaUrl: markerIcon2x,
  shadowUrl: markerShadow,
});

// Import Leaflet
import L from 'leaflet';

// Check Leaflet plugins
// Uncomment to debug plugin availability
// checkLeafletPlugins();

interface MapViewProps {
  incidents: Incident[];
  onSelectIncident: (id: string) => void;
}

// Component to handle map bounds
const MapBounds: React.FC<{ incidents: Incident[] }> = ({ incidents }) => {
  const map = useMap();

  // Add a flag to track if user interaction was recent
  const userInteractionRef = useRef(false);
  // Track last incidents length to detect changes
  const lastIncidentsLengthRef = useRef(incidents.length);
  // Track if this is the first render
  const isFirstRenderRef = useRef(true);

  useEffect(() => {
    // Listen for the global event that indicates user interaction
    const handleUserInteraction = () => {
      console.log("User interaction detected in MapBounds");
      userInteractionRef.current = true;

      // Reset the flag after a delay to allow normal bounds fitting later
      if (typeof window !== 'undefined') {
        // Use type assertion to access the custom property
        clearTimeout((window as any).mapBoundsTimer);
        (window as any).mapBoundsTimer = setTimeout(() => {
          userInteractionRef.current = false;
          console.log("User interaction timeout expired, bounds fitting enabled");
        }, 10000); // 10 second delay before allowing auto-bounds again
      }
    };

    // Add the event listener to window
    if (typeof window !== 'undefined') {
      window.addEventListener('home-button-clicked', handleUserInteraction);

      // Also listen for map movement events
      if (map) {
        map.on('movestart', () => {
          console.log("Map movement detected");
          handleUserInteraction();
        });

        map.on('zoomstart', () => {
          console.log("Map zoom detected");
          handleUserInteraction();
        });

        map.on('dragstart', () => {
          console.log("Map drag detected");
          handleUserInteraction();
        });
      }
    }

    return () => {
      // Clean up the event listeners
      if (typeof window !== 'undefined') {
        window.removeEventListener('home-button-clicked', handleUserInteraction);
        clearTimeout((window as any).mapBoundsTimer);
      }

      if (map) {
        map.off('movestart');
        map.off('zoomstart');
        map.off('dragstart');
      }
    };
  }, [map]);

  useEffect(() => {
    // Skip bounds fitting if user interaction was recent
    if (userInteractionRef.current) {
      console.log("Skipping bounds fitting because of recent user interaction");
      return;
    }

    // Skip bounds fitting if this is not the first render and incidents length hasn't changed
    if (!isFirstRenderRef.current && incidents.length === lastIncidentsLengthRef.current) {
      console.log("Skipping bounds fitting because incidents haven't changed");
      return;
    }

    // Update the last incidents length
    lastIncidentsLengthRef.current = incidents.length;

    // Mark first render as complete
    isFirstRenderRef.current = false;

    if (incidents.length > 0) {
      console.log("Fitting bounds to incidents");

      // Filter out incidents with invalid coordinates
      const validIncidents = incidents.filter(incident => isValidCoordinates(incident.location));

      if (validIncidents.length > 0) {
        const bounds = validIncidents.reduce(
          (bounds, incident) => {
            const safeLocation = getSafeCoordinates(incident.location);
            return bounds.extend([
              safeLocation.latitude,
              safeLocation.longitude
            ]);
          },
          map.getBounds()
        );
        map.fitBounds(bounds, { padding: [50, 50] });
      }
    }
  }, [incidents, map]);

  return null;
};

// Component to render heatmap
const HeatmapLayer: React.FC<{ incidents: Incident[] }> = ({ incidents }) => {
  const map = useMap();
  const heatLayerRef = useRef<any>(null);

  useEffect(() => {
    if (!map || incidents.length === 0) return;

    // Clean up function
    const cleanup = () => {
      if (heatLayerRef.current) {
        try {
          map.removeLayer(heatLayerRef.current);
          heatLayerRef.current = null;
        } catch (error) {
          console.warn('Error removing heat layer:', error);
        }
      }
    };

    // Remove existing heat layer
    cleanup();

    try {
      // Check if heatLayer is available
      // @ts-ignore
      if (typeof L.heatLayer !== 'function') {
        console.warn('Heat layer plugin not available');
        return cleanup;
      }

      // Convert incidents to heatmap points, filtering out invalid coordinates
      const heatPoints = incidents
        .filter(incident => isValidCoordinates(incident.location))
        .map(incident => {
          // Weight based on severity
          let weight = 1;
          switch (incident.severity) {
            case 'LOW': weight = 1; break;
            case 'MEDIUM': weight = 2; break;
            case 'HIGH': weight = 3; break;
            case 'CRITICAL': weight = 4; break;
          }

          // Get safe coordinates
          const safeLocation = getSafeCoordinates(incident.location);

          return [
            safeLocation.latitude,
            safeLocation.longitude,
            weight
          ] as [number, number, number];
        });

      // Create and add heatmap layer
      // @ts-ignore
      heatLayerRef.current = L.heatLayer(heatPoints, {
        radius: 25,
        blur: 15,
        maxZoom: 17,
        gradient: {
          0.4: 'blue',
          0.6: 'lime',
          0.8: 'yellow',
          1.0: 'red'
        }
      }).addTo(map);
    } catch (error) {
      console.warn('Error creating heat layer:', error);
    }

    return cleanup;
  }, [map, incidents]);

  return null;
};

// Component to handle map plugins and controls
// This component is used within the MapView component
const MapControls: React.FC<{
  onDrawCreated?: (layer: any) => void;
  drawnItems?: L.FeatureGroup;
}> = ({ onDrawCreated, drawnItems }) => {
  const map = useMap();

  // Initialize plugins after map is ready
  useEffect(() => {
    if (!map) return;

    // Add fullscreen control - only if available
    try {
      // @ts-ignore
      if (typeof L.control.fullscreen === 'function') {
        // @ts-ignore
        L.control.fullscreen({
          position: 'topleft',
          title: 'Show me the fullscreen!',
          titleCancel: 'Exit fullscreen mode',
          content: null,
          forceSeparateButton: true,
          forcePseudoFullscreen: true,
          fullscreenElement: false
        }).addTo(map);
      }
    } catch (error) {
      console.warn('Fullscreen control not available:', error);
    }

    // Add locate control - only if available
    try {
      // @ts-ignore
      if (typeof L.control.locate === 'function') {
        // @ts-ignore
        L.control.locate({
          position: 'topleft',
          strings: {
            title: 'Show me where I am'
          },
          locateOptions: {
            maxZoom: 15
          }
        }).addTo(map);
      }
    } catch (error) {
      console.warn('Locate control not available:', error);
    }

    // Add measurement control - only if available
    try {
      // @ts-ignore
      if (L.Control.Measure) {
        // @ts-ignore
        const measureControl = new L.Control.Measure({
          position: 'topleft',
          primaryLengthUnit: 'kilometers',
          secondaryLengthUnit: 'miles',
          primaryAreaUnit: 'sqkilometers',
          secondaryAreaUnit: 'acres'
        });
        measureControl.addTo(map);
      }
    } catch (error) {
      console.warn('Measure control not available:', error);
    }

    // We no longer add the draw control here
    // It's now handled in the MilitaryTools component

    // Make sure any Leaflet Draw controls are hidden
    try {
      // Hide any visible Leaflet Draw controls
      setTimeout(() => {
        const existingDrawControls = document.querySelectorAll('.leaflet-draw');
        existingDrawControls.forEach(control => {
          (control as HTMLElement).style.display = 'none';
        });
      }, 100); // Small delay to ensure controls are in the DOM
    } catch (error) {
      console.warn('Error hiding draw controls:', error);
    }

    // Home button is now handled in the MilitaryToolbar component

    // Add coordinates display
    const coordControl = L.Control.extend({
      options: {
        position: 'bottomleft'
      },
      onAdd: function() {
        const container = L.DomUtil.create('div', 'leaflet-control leaflet-control-coordinates bg-white px-2 py-1 rounded shadow text-xs');
        container.innerHTML = 'Coordinates: --.-----, --.-----';

        map.on('mousemove', function(e: L.LeafletMouseEvent) {
          container.innerHTML = `Coordinates: ${e.latlng.lat.toFixed(5)}, ${e.latlng.lng.toFixed(5)}`;
        });

        return container;
      }
    });
    map.addControl(new coordControl());

    // Handle draw events for callback purposes
    if (drawnItems && onDrawCreated) {
      map.on('draw:created', (e: any) => {
        try {
          const layer = e.layer;
          if (onDrawCreated) {
            onDrawCreated(layer);
          }
        } catch (error) {
          console.warn('Error handling draw created event:', error);
        }
      });
    }

    return () => {
      // Clean up event listeners when component unmounts
      try {
        map.off('draw:created');
        map.off('mousemove');
      } catch (error) {
        console.warn('Error cleaning up event listeners:', error);
      }
    };
  }, [map, drawnItems, onDrawCreated]);

  return null;
};

const MapView: React.FC<MapViewProps> = ({ incidents, onSelectIncident }) => {
  // Default center to Pakistan if no incidents
  const defaultCenter = { lat: 30.3753, lng: 69.3451 };
  const defaultZoom = 5;

  const [mapMode, setMapMode] = useState<'markers' | 'heatmap' | 'cluster'>('markers');
  const [activeBaseLayer, setActiveBaseLayer] = useState<string>('satellite');
  const [showResponses, setShowResponses] = useState<boolean>(true);

  // Context menu state
  const [contextMenuPosition, setContextMenuPosition] = useState<{ x: number; y: number } | null>(null);
  const [contextMenuMapPosition, setContextMenuMapPosition] = useState<LatLng | null>(null);

  // Get responses from the store
  const { filteredResponses } = useResponseStore();
  const { showIncidentForm } = useIncidentStore();
  const { showResponseForm } = useResponseStore();

  // Reference to store the map instance
  const mapRef = useRef<L.Map | null>(null);

  // State to store the current map view (zoom and center)
  const [mapView, setMapView] = useState({
    center: defaultCenter,
    zoom: defaultZoom
  });

  // Reference to the drawn items layer
  const drawnItemsRef = useRef<L.FeatureGroup | null>(null);
  const [drawnFeatures, setDrawnFeatures] = useState<any[]>([]);

  // Use the filter store for filtered incidents
  const { applyFilters } = useFilterStore();

  // Initialize drawn items layer when component mounts
  useEffect(() => {
    drawnItemsRef.current = L.featureGroup();
    return () => {
      drawnItemsRef.current = null;
    };
  }, []);

  // Handle initial map view capture - run only once
  const initialMapCaptureRef = useRef(false);
  useEffect(() => {
    if (mapRef.current && !initialMapCaptureRef.current) {
      // Capture the initial map view once the map is ready
      captureMapView();
      initialMapCaptureRef.current = true;
    }
  }, []);

  // Function to capture the current map view
  const captureMapView = () => {
    if (mapRef.current) {
      const center = mapRef.current.getCenter();
      const zoom = mapRef.current.getZoom();
      setMapView({
        center: { lat: center.lat, lng: center.lng },
        zoom: zoom
      });
    }
  };

  // Save map view when map is moved or zoomed
  useEffect(() => {
    if (!mapRef.current) return;

    const map = mapRef.current;

    const handleMoveEnd = () => {
      captureMapView();
    };

    map.on('moveend', handleMoveEnd);
    map.on('zoomend', handleMoveEnd);

    return () => {
      map.off('moveend', handleMoveEnd);
      map.off('zoomend', handleMoveEnd);
    };
  }, []); // Empty dependency array since we're using ref

  // Apply filters to incidents
  const filteredIncidents = applyFilters(incidents);

  const createMarkerIcon = (type: IncidentType) => {
    // Use tactical military symbols instead of default markers
    return createTacticalIcon(type, 32);
  };

  const createResponseMarkerIcon = (type: ActionType) => {
    // Use blue NATO-style symbols for responses
    return createResponseIcon(type, 32);
  };

  const handleDrawCreated = (layer: any) => {
    // Save the drawn feature
    const geoJSON = layer.toGeoJSON();
    setDrawnFeatures(prev => [...prev, geoJSON]);
  };

  const handleClearDrawings = () => {
    if (drawnItemsRef.current) {
      drawnItemsRef.current.clearLayers();
      setDrawnFeatures([]);
    }
  };

  const handleSaveDrawings = () => {
    if (drawnFeatures.length > 0) {
      const geoJSON = {
        type: 'FeatureCollection',
        features: drawnFeatures
      };

      // Save to localStorage for demo purposes
      localStorage.setItem('savedDrawings', JSON.stringify(geoJSON));
      alert('Drawings saved successfully!');
    } else {
      alert('No drawings to save!');
    }
  };

  const handleLoadDrawings = () => {
    const savedDrawings = localStorage.getItem('savedDrawings');
    if (savedDrawings && drawnItemsRef.current) {
      try {
        const geoJSON = JSON.parse(savedDrawings);

        // Clear existing drawings
        drawnItemsRef.current.clearLayers();

        // Add saved drawings
        L.geoJSON(geoJSON, {
          onEachFeature: (_, layer) => {
            drawnItemsRef.current?.addLayer(layer);
          }
        });

        setDrawnFeatures(geoJSON.features);
        alert('Drawings loaded successfully!');
      } catch (error) {
        console.error('Error loading drawings:', error);
        alert('Error loading drawings!');
      }
    } else {
      alert('No saved drawings found!');
    }
  };

  // Handle adding an incident from the context menu
  const handleAddIncident = () => {
    if (contextMenuMapPosition) {
      // Set the location in the form
      const location = {
        latitude: contextMenuMapPosition.lat,
        longitude: contextMenuMapPosition.lng
      };

      // Show the incident form with the location
      showIncidentForm(location);

      // Close the context menu
      setContextMenuPosition(null);
    }
  };

  // Handle adding a response from the context menu
  const handleAddResponse = () => {
    if (contextMenuMapPosition) {
      // Set the location in the form
      const location = {
        latitude: contextMenuMapPosition.lat,
        longitude: contextMenuMapPosition.lng
      };

      // Show the response form with the location
      showResponseForm(location);

      // Close the context menu
      setContextMenuPosition(null);
    }
  };

  // We'll use setFilteredIncidents directly where needed

  // Function to add drawn items layer to map
  const DrawItemsLayer = () => {
    const map = useMap();

    useEffect(() => {
      if (drawnItemsRef.current) {
        drawnItemsRef.current.addTo(map);
      }
    }, [map]);

    return null;
  };

  // Component to handle map events like right-click
  const MapEvents = () => {
    // Using _ to indicate we're not using the map reference
    useMapEvents({
      contextmenu: (e) => {
        // Show context menu at click position
        setContextMenuPosition({ x: e.originalEvent.clientX, y: e.originalEvent.clientY });
        setContextMenuMapPosition(e.latlng);

        // Prevent default context menu
        e.originalEvent.preventDefault();
      },
      click: () => {
        // Close context menu on regular click
        setContextMenuPosition(null);
      }
    });

    return null;
  };

  // Component to fix zoom issues
  const ZoomFixer = () => {
    const map = useMap();

    useEffect(() => {
      if (map) {
        // Override the default zoom behavior
        const originalZoomIn = map.zoomIn;
        const originalZoomOut = map.zoomOut;

        // Replace with custom implementations
        map.zoomIn = function(delta?: number, options?: L.ZoomOptions) {
          console.log('Custom zoomIn called');
          try {
            // Stop any ongoing animations
            this.stop();

            // Get current zoom
            const currentZoom = this.getZoom();
            const zoomDelta = delta || 1;

            // Set new zoom directly
            this.setZoom(currentZoom + zoomDelta, { animate: false });

            // Force redraw
            this.invalidateSize(true);
          } catch (error) {
            console.warn('Error in custom zoomIn:', error);
            // Fall back to original method
            originalZoomIn.call(this, delta, options);
          }
          return this;
        };

        map.zoomOut = function(delta?: number, options?: L.ZoomOptions) {
          console.log('Custom zoomOut called');
          try {
            // Stop any ongoing animations
            this.stop();

            // Get current zoom
            const currentZoom = this.getZoom();
            const zoomDelta = delta || 1;

            // Set new zoom directly
            this.setZoom(currentZoom - zoomDelta, { animate: false });

            // Force redraw
            this.invalidateSize(true);
          } catch (error) {
            console.warn('Error in custom zoomOut:', error);
            // Fall back to original method
            originalZoomOut.call(this, delta, options);
          }
          return this;
        };

        // Add a custom method for centering on Pakistan
        // Use type assertion to add the method
        (map as any).centerOnPakistan = function() {
          console.log('Centering on Pakistan');
          try {
            // Stop any ongoing animations
            this.stop();

            // Set zoom directly first
            this.setZoom(5, { animate: false });

            // Then set center
            this.panTo([30.3753, 69.3451], { animate: false });

            // Force redraw
            this.invalidateSize(true);
          } catch (error) {
            console.warn('Error centering on Pakistan:', error);
          }
          return this;
        };

        // Make the custom method available globally
        if (typeof window !== 'undefined') {
          (window as any).centerMapOnPakistan = () => {
            // Use type assertion to call the method
            (map as any).centerOnPakistan();
          };
        }
      }
    }, [map]);

    return null;
  };

  return (
    <Card
      title="TACTICAL MAP"
      className="h-full military-card"
      headerAction={
        <div className="flex flex-wrap gap-1">
          <div className="flex space-x-1">
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${mapMode === 'markers' ? 'active' : ''}`}
              onClick={() => setMapMode('markers')}
              title="Show individual markers"
            >
              <MapPin size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${mapMode === 'cluster' ? 'active' : ''}`}
              onClick={() => setMapMode('cluster')}
              title="Show clustered markers"
            >
              <Layers size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${mapMode === 'heatmap' ? 'active' : ''}`}
              onClick={() => setMapMode('heatmap')}
              title="Show heatmap"
            >
              <CircleIcon size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${showResponses ? 'active' : ''}`}
              onClick={() => setShowResponses(!showResponses)}
              title={showResponses ? "Hide Responses" : "Show Responses"}
            >
              <Shield size={14} style={{ color: '#0074D9' }} />
            </Button>
          </div>
          <div className="flex space-x-1">
            <Button
              size="sm"
              variant="ghost"
              className="military-button"
              onClick={handleClearDrawings}
              title="Clear all drawings"
            >
              <Trash2 size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="military-button"
              onClick={handleSaveDrawings}
              title="Save drawings"
            >
              <Save size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="military-button"
              onClick={handleLoadDrawings}
              title="Load drawings"
            >
              <Upload size={14} />
            </Button>
          </div>
        </div>
      }
    >
      <div className="h-full rounded-md overflow-hidden relative"
           onClick={(e) => {
             // Prevent click events from propagating to the Card component
             e.stopPropagation();
           }}
      >
        {/* Context menu */}
        {contextMenuPosition && contextMenuMapPosition && (
          <MapContextMenu
            position={contextMenuPosition}
            mapPosition={contextMenuMapPosition}
            onAddIncident={handleAddIncident}
            onAddResponse={handleAddResponse}
            onClose={() => setContextMenuPosition(null)}
          />
        )}

        <MapContainer
          center={[mapView.center.lat, mapView.center.lng]}
          zoom={mapView.zoom}
          style={{ height: '100%', width: '100%' }}
          zoomControl={false} // We'll add custom zoom control
          ref={(map) => {
            if (map && !mapRef.current) {
              mapRef.current = map;
              // We'll handle the initial view capture in a useEffect
            }
          }}
        >
          {/* Base layers - Controlled by MilitaryToolbar */}
          {activeBaseLayer === 'satellite' && (
            <TileLayer
              url="https://{s}.google.com/vt/lyrs=s,h&x={x}&y={y}&z={z}"
              subdomains={['mt0','mt1','mt2','mt3']}
              attribution="&copy; Google Maps"
            />
          )}

          {activeBaseLayer === 'terrain' && (
            <TileLayer
              url="https://{s}.google.com/vt/lyrs=p&x={x}&y={y}&z={z}"
              subdomains={['mt0','mt1','mt2','mt3']}
              attribution="&copy; Google Maps"
            />
          )}

          {activeBaseLayer === 'osm' && (
            <TileLayer
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
              attribution="&copy; OpenStreetMap contributors"
            />
          )}

          {activeBaseLayer === 'dark' && (
            <TileLayer
              url="https://cartodb-basemaps-{s}.global.ssl.fastly.net/dark_all/{z}/{x}/{y}.png"
              attribution="&copy; CartoDB"
            />
          )}

          {activeBaseLayer === 'topo' && (
            <TileLayer
              url="https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png"
              attribution="&copy; OpenTopoMap"
            />
          )}

          {/* Add scale control */}
          <ScaleControl position="bottomright" imperial={false} />

          {/* Add map bounds handler */}
          <MapBounds incidents={filteredIncidents} />

          {/* Add drawn items layer */}
          <DrawItemsLayer />

          {/* Military-styled toolbar with all controls */}
          <MilitaryToolbar
            drawnItems={drawnItemsRef.current}
            onDrawCreated={handleDrawCreated}
            onBaseLayerChange={setActiveBaseLayer}
            activeBaseLayer={activeBaseLayer}
          />

          {/* Coordinate display is now part of MilitaryToolbar */}

          {/* All Tactical Tools in a collapsible container at top left */}
          <ControlContainer
            title="TACTICAL TOOLS"
            icon={<Shield size={16} />}
            position="topleft"
            initialCollapsed={true}
            className="text-left"
          >
            <div className="space-y-3">
              {/* Military drawing tools */}
              <div>
                <div className="text-xs font-bold mb-1 text-gray-300">TACTICAL TOOLS</div>
                <MilitaryTools drawnItems={drawnItemsRef.current} />
              </div>

              {/* Export tools */}
              <div>
                <div className="text-xs font-bold mb-1 text-gray-300">EXPORT</div>
                <div className="grid grid-cols-2 gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    className="military-button"
                    title="Export Incidents as GeoJSON"
                    onClick={() => {
                      try {
                        const geoJson = createGeoJsonFromIncidents(filteredIncidents);
                        const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(geoJson));
                        const downloadAnchorNode = document.createElement('a');
                        downloadAnchorNode.setAttribute("href", dataStr);
                        downloadAnchorNode.setAttribute("download", "incidents.geojson");
                        document.body.appendChild(downloadAnchorNode);
                        downloadAnchorNode.click();
                        downloadAnchorNode.remove();
                      } catch (error) {
                        console.log('Export failed', error);
                        alert('Export failed: ' + error);
                      }
                    }}
                  >
                    <Download size={14} />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="military-button"
                    title="Save Current View"
                    onClick={() => {
                      try {
                        // Get the map instance using useMap hook
                        const mapContainer = document.querySelector('.leaflet-container');
                        // @ts-ignore - Leaflet adds this property to the DOM element
                        const mapInstance = mapContainer ? mapContainer['_leaflet_map'] : null;
                        if (!mapInstance) {
                          throw new Error('Map instance not found');
                        }

                        const mapState = {
                          center: mapInstance.getCenter(),
                          zoom: mapInstance.getZoom(),
                          baseLayer: activeBaseLayer
                        };
                        localStorage.setItem('mapState', JSON.stringify(mapState));
                        alert('Map view saved successfully');
                      } catch (error) {
                        console.log('Save failed', error);
                        alert('Save failed: ' + error);
                      }
                    }}
                  >
                    <Save size={14} />
                  </Button>
                </div>
              </div>
            </div>
          </ControlContainer>

          {/* Layer management in a collapsible container parallel to TACTICAL TOOLS */}
          <ControlContainer
            title="LAYERS"
            icon={<Layers size={16} />}
            position="topright"
            initialCollapsed={true}
            className="right-[75px]" // Position parallel to TACTICAL TOOLS
          >
            <LayerManager drawnItems={drawnItemsRef.current} />
          </ControlContainer>

          {/* Enhanced spatial filter removed as requested */}

          {/* Tactical symbols legend moved to bottom left */}
          <TacticalLegend position="bottomleft" />

          {/* Map view preserver removed */}

          {/* Map view reset button removed as requested */}

          {/* Map events handler for context menu */}
          <MapEvents />

          {/* Add the zoom fixer component */}
          <ZoomFixer />

          {/* Render incident markers */}
          {mapMode === 'markers' && filteredIncidents
            .filter(incident => isValidCoordinates(incident.location))
            .map((incident: Incident) => {
              // Get safe coordinates
              const safeLocation = getSafeCoordinates(incident.location);

              return (
                <Marker
                  key={incident.id}
                  position={[safeLocation.latitude, safeLocation.longitude]}
                  icon={createMarkerIcon(incident.type)}
                  // dblclick event handler removed as requested
                >
                  <Popup className="military-popup">
                    <div className="p-2">
                      <h3 className="font-medium text-white">{incident.title}</h3>
                      <div className="flex flex-wrap gap-1 mt-2">
                        <Badge label={incident.type.replace('_', ' ')} color="primary" />
                        <Badge
                          label={incident.severity}
                          color={
                            incident.severity === 'CRITICAL' ? 'danger' :
                            incident.severity === 'HIGH' ? 'warning' :
                            incident.severity === 'MEDIUM' ? 'info' : 'default'
                          }
                        />
                      </div>
                      <div className="text-xs text-gray-300 mt-2 font-mono">
                        <div>GRID: {safeLocation.latitude.toFixed(5)}, {safeLocation.longitude.toFixed(5)}</div>
                        <div>LOC: {incident.address}</div>
                        <div>REPORTED: {new Date(incident.reportedAt).toLocaleString()}</div>
                      </div>
                      <Button
                        size="sm"
                        className="mt-2 w-full military-button"
                        onClick={() => onSelectIncident(incident.id)}
                      >
                        View Details
                      </Button>
                    </div>
                  </Popup>
                </Marker>
              );
            })}

          {/* Render response markers */}
          {mapMode === 'markers' && showResponses && filteredResponses
            .filter(response => isValidCoordinates(response.location))
            .map((response: Response) => {
              // Get safe coordinates
              const safeLocation = getSafeCoordinates(response.location);

              return (
                <Marker
                  key={response.id}
                  position={[safeLocation.latitude, safeLocation.longitude]}
                  icon={createResponseMarkerIcon(response.type)}
                  // dblclick event handler removed as requested
                >
                  <Popup className="military-popup">
                    <div className="p-2">
                      <h3 className="font-medium text-white">{response.title}</h3>
                      <div className="flex flex-wrap gap-1 mt-2">
                        <Badge label={response.type.replace('_', ' ')} color="primary" className="bg-military-blue" />
                        <Badge
                          label={response.status}
                          color={
                            response.status === 'PLANNED' ? 'warning' :
                            response.status === 'IN_PROGRESS' ? 'info' :
                            response.status === 'COMPLETED' ? 'success' : 'danger'
                          }
                        />
                      </div>
                      <div className="text-xs text-gray-300 mt-2 font-mono">
                        <div>GRID: {safeLocation.latitude.toFixed(5)}, {safeLocation.longitude.toFixed(5)}</div>
                        <div>LOC: {response.address}</div>
                        <div>COMMANDER: {response.commander}</div>
                        <div>START: {new Date(response.startDate).toLocaleString()}</div>
                      </div>
                      <Button
                        size="sm"
                        className="mt-2 w-full military-button"
                        onClick={() => useResponseStore.getState().selectResponse(response.id)}
                      >
                        View Details
                      </Button>
                    </div>
                  </Popup>
                </Marker>
              );
            })}

          {/* Render marker clusters */}
          {mapMode === 'cluster' && (
            <MarkerCluster
              incidents={filteredIncidents}
              onSelectIncident={onSelectIncident}
            />
          )}

          {/* Render heatmap */}
          {mapMode === 'heatmap' && (
            <HeatmapLayer incidents={filteredIncidents} />
          )}
        </MapContainer>
      </div>
    </Card>
  );
};

const MapPanel: React.FC = () => {
  const { filteredIncidents, selectIncident } = useIncidentStore();

  const handleSelectIncident = (id: string) => {
    selectIncident(id);
  };

  return (
    <MapView
      incidents={filteredIncidents}
      onSelectIncident={handleSelectIncident}
    />
  );
};

export default MapPanel;