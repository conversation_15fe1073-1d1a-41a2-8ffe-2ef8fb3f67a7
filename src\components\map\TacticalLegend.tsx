import React, { useState, useCallback } from 'react';
import { Incident, Response, IncidentType } from '@/types/incident';
import {
  Eye,
  EyeOff,
  Edit3,
  Save,
  X,
  Plus,
  Trash2,
  ChevronDown,
  ChevronUp,
  Settings
} from 'lucide-react';
import Button from '@/components/ui/Button';
import { getIncidentSymbol, getResponseSymbol } from './TacticalSymbols';

interface TacticalLegendProps {
  position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  incidents: Incident[];
  responses: Response[];
  isVisible?: boolean;
  className?: string;
}

interface LegendItem {
  id: string;
  type: 'incident' | 'response';
  label: string;
  color: string;
  symbol: string;
  visible: boolean;
  count: number;
}

interface CustomSymbol {
  id: string;
  name: string;
  symbol: string;
  color: string;
  category: string;
}

const TacticalLegend: React.FC<TacticalLegendProps> = ({
  position,
  incidents,
  responses,
  isVisible = true,
  className = ''
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [showCustomSymbols, setShowCustomSymbols] = useState(false);

  // Custom symbols state
  const [customSymbols, setCustomSymbols] = useState<CustomSymbol[]>([
    { id: '1', name: 'Command Post', symbol: '⌘', color: '#FFD700', category: 'Command' },
    { id: '2', name: 'Observation Post', symbol: '👁', color: '#00FF00', category: 'Intelligence' },
    { id: '3', name: 'Supply Depot', symbol: '📦', color: '#8B4513', category: 'Logistics' },
    { id: '4', name: 'Medical Station', symbol: '🏥', color: '#FF0000', category: 'Medical' }
  ]);

  // Visibility state for legend items
  const [itemVisibility, setItemVisibility] = useState<Record<string, boolean>>({});



  // Generate legend items from current data
  const generateLegendItems = useCallback((): LegendItem[] => {
    const items: LegendItem[] = [];

    // Group incidents by type
    const incidentGroups = incidents.reduce((acc, incident) => {
      acc[incident.type] = (acc[incident.type] || 0) + 1;
      return acc;
    }, {} as Record<IncidentType, number>);

    // Add incident legend items
    Object.entries(incidentGroups).forEach(([type, count]) => {
      const incidentType = type as IncidentType;
      const symbolConfig = getIncidentSymbol(incidentType);
      items.push({
        id: `incident-${type}`,
        type: 'incident',
        label: type.replace('_', ' ').toUpperCase(),
        color: symbolConfig.color,
        symbol: symbolConfig.symbol,
        visible: itemVisibility[`incident-${type}`] !== false,
        count
      });
    });

    // Add response legend item
    if (responses.length > 0) {
      items.push({
        id: 'response-all',
        type: 'response',
        label: 'RESPONSE OPERATIONS',
        color: '#00AA00',
        symbol: '🚁',
        visible: itemVisibility['response-all'] !== false,
        count: responses.length
      });
    }

    return items;
  }, [incidents, responses, itemVisibility]);

  const legendItems = generateLegendItems();

  // Toggle item visibility
  const toggleItemVisibility = (itemId: string) => {
    setItemVisibility(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };

  // Handle editing
  const startEditing = (itemId: string) => {
    setEditingItem(itemId);
  };

  const stopEditing = () => {
    setEditingItem(null);
  };

  // Add custom symbol
  const addCustomSymbol = () => {
    const newSymbol: CustomSymbol = {
      id: Date.now().toString(),
      name: 'New Symbol',
      symbol: '⭐',
      color: '#FFFFFF',
      category: 'Custom'
    };
    setCustomSymbols(prev => [...prev, newSymbol]);
  };

  // Remove custom symbol
  const removeCustomSymbol = (symbolId: string) => {
    setCustomSymbols(prev => prev.filter(s => s.id !== symbolId));
  };

  // Get position classes
  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      default:
        return 'bottom-4 left-4';
    }
  };

  // Don't render if not visible
  if (!isVisible) return null;

  return (
    <div className={`absolute ${getPositionClasses()} z-40 ${className}`}>
      <div className="bg-gray-900 bg-opacity-95 border border-gray-600 rounded-lg shadow-lg backdrop-blur-sm w-[160px]">
        {/* Header */}
        <div className="flex items-center justify-between p-2 border-b border-gray-600">
          <div className="flex items-center space-x-1">
            <span className="text-[10px] font-mono text-gray-300 font-semibold">
              LEGEND
            </span>
            <span className="text-[9px] text-gray-500">
              ({legendItems.length})
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsEditing(!isEditing)}
              className="p-1 h-5 w-5 text-gray-400 hover:text-white"
              title={isEditing ? "Stop Editing" : "Edit Legend"}
            >
              {isEditing ? <X size={10} /> : <Edit3 size={10} />}
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-1 h-5 w-5 text-gray-400 hover:text-white"
            >
              {isCollapsed ? <ChevronUp size={10} /> : <ChevronDown size={10} />}
            </Button>
          </div>
        </div>

        {/* Content */}
        {!isCollapsed && (
          <div className="p-2 space-y-1 max-h-[200px] overflow-y-auto">
            {/* Legend items */}
            {legendItems.map(item => (
              <div
                key={item.id}
                className="flex items-center justify-between p-1 rounded bg-gray-800 bg-opacity-50"
              >
                <div className="flex items-center space-x-2 flex-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => toggleItemVisibility(item.id)}
                    className="p-0.5 h-4 w-4 text-gray-400 hover:text-white"
                  >
                    {item.visible ? <Eye size={8} /> : <EyeOff size={8} />}
                  </Button>

                  <div
                    className="w-3 h-3 rounded flex items-center justify-center text-[8px]"
                    style={{ backgroundColor: item.color }}
                  >
                    {item.symbol}
                  </div>

                  <div className="flex-1">
                    <div className="text-[9px] text-gray-300 font-medium truncate">
                      {item.label.split(' ')[0]}
                    </div>
                    <div className="text-[8px] text-gray-500">
                      {item.count}
                    </div>
                  </div>
                </div>

                {isEditing && (
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => startEditing(item.id)}
                    className="p-0.5 h-4 w-4 text-gray-400 hover:text-white"
                  >
                    <Edit3 size={8} />
                  </Button>
                )}
              </div>
            ))}

            {/* Empty state */}
            {legendItems.length === 0 && (
              <div className="text-center py-2">
                <div className="text-gray-500 text-[9px]">
                  No data
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TacticalLegend;
