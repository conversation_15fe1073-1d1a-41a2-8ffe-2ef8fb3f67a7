import React, { useState } from 'react';
import { 
  Filter, X, Calendar, Search, Tag, Map, Pie<PERSON>hart, 
  AlertTriangle, CheckCircle, Clock, Activity, 
  ChevronDown, ChevronUp, RefreshCw
} from 'lucide-react';
import { format } from 'date-fns';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { useFilterStore } from '@/store/filterStore';
import { 
  IncidentType, 
  IncidentSeverity, 
  IncidentStatus, 
  ActionType 
} from '@/types/incident';

// Helper function to get human-readable filter names
const getFilterLabel = (filterType: string): string => {
  switch (filterType) {
    case 'type': return 'Type';
    case 'severity': return 'Severity';
    case 'status': return 'Status';
    case 'action': return 'Action';
    case 'dateRange': return 'Date Range';
    case 'searchTerm': return 'Search';
    case 'tags': return 'Tags';
    case 'spatial': return 'Map Area';
    case 'chart': return 'Chart Selection';
    default: return filterType;
  }
};

// Helper function to get filter icon
const getFilterIcon = (filterType: string) => {
  switch (filterType) {
    case 'type': return <Activity size={14} />;
    case 'severity': return <AlertTriangle size={14} />;
    case 'status': return <CheckCircle size={14} />;
    case 'action': return <Activity size={14} />;
    case 'dateRange': return <Calendar size={14} />;
    case 'searchTerm': return <Search size={14} />;
    case 'tags': return <Tag size={14} />;
    case 'spatial': return <Map size={14} />;
    case 'chart': return <PieChart size={14} />;
    default: return <Filter size={14} />;
  }
};

// Helper function to get filter value label
const getValueLabel = (filterType: string, value: any): string => {
  if (value instanceof Date) {
    return format(value, 'yyyy-MM-dd');
  }
  
  if (typeof value === 'string') {
    // For enum values, make them more readable
    if (Object.values(IncidentType).includes(value as IncidentType)) {
      return value.replace('TS_', '');
    }
    
    if (Object.values(IncidentSeverity).includes(value as IncidentSeverity)) {
      return value;
    }
    
    if (Object.values(IncidentStatus).includes(value as IncidentStatus)) {
      return value.replace('_', ' ');
    }
    
    if (Object.values(ActionType).includes(value as ActionType)) {
      return value;
    }
    
    return value;
  }
  
  return String(value);
};

// Helper function to get filter color
const getFilterColor = (filterType: string, value?: any): string => {
  switch (filterType) {
    case 'type': return 'bg-military-navy';
    case 'severity': 
      if (value === 'CRITICAL') return 'bg-military-red';
      if (value === 'HIGH') return 'bg-military-amber';
      if (value === 'MEDIUM') return 'bg-military-darkgreen';
      return 'bg-military-navy';
    case 'status': return 'bg-military-darkgreen';
    case 'action': return 'bg-military-navy';
    case 'dateRange': return 'bg-military-navy';
    case 'searchTerm': return 'bg-military-navy';
    case 'tags': return 'bg-military-navy';
    case 'spatial': return 'bg-military-amber';
    case 'chart': return 'bg-military-darkgreen';
    default: return 'bg-military-navy';
  }
};

const FilterPanel: React.FC = () => {
  const [expanded, setExpanded] = useState(true);
  const { 
    activeFilters, 
    types, severities, statuses, actions,
    dateRange, searchTerm, tags, spatial, chartFilters,
    removeFilter, resetAllFilters
  } = useFilterStore();
  
  // No filters applied
  if (activeFilters.length === 0) {
    return null;
  }
  
  return (
    <Card 
      title={
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <Filter size={16} className="mr-2" />
            <span>ACTIVE FILTERS</span>
            <span className="ml-2 text-xs bg-military-darkgreen px-2 py-0.5 rounded-full">
              {activeFilters.length}
            </span>
          </div>
          <Button
            size="xs"
            variant="ghost"
            className="military-button"
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
          </Button>
        </div>
      }
      variant="military"
      className="mb-4"
    >
      {expanded && (
        <div className="space-y-3">
          <div className="flex flex-wrap gap-2">
            {/* Type filters */}
            {types.length > 0 && (
              <>
                {types.map(type => (
                  <div 
                    key={`type-${type}`}
                    className={`flex items-center px-2 py-1 rounded text-xs text-white ${getFilterColor('type')}`}
                  >
                    <Activity size={12} className="mr-1" />
                    <span>{getValueLabel('type', type)}</span>
                    <Button
                      size="xs"
                      variant="ghost"
                      className="ml-1 p-0 h-4 w-4 hover:bg-military-panel"
                      onClick={() => removeFilter('type', type)}
                    >
                      <X size={10} />
                    </Button>
                  </div>
                ))}
                <Button
                  size="xs"
                  variant="ghost"
                  className="military-button-sm"
                  onClick={() => removeFilter('type')}
                >
                  <X size={10} className="mr-1" />
                  Clear Types
                </Button>
              </>
            )}
            
            {/* Severity filters */}
            {severities.length > 0 && (
              <>
                {severities.map(severity => (
                  <div 
                    key={`severity-${severity}`}
                    className={`flex items-center px-2 py-1 rounded text-xs text-white ${getFilterColor('severity', severity)}`}
                  >
                    <AlertTriangle size={12} className="mr-1" />
                    <span>{getValueLabel('severity', severity)}</span>
                    <Button
                      size="xs"
                      variant="ghost"
                      className="ml-1 p-0 h-4 w-4 hover:bg-military-panel"
                      onClick={() => removeFilter('severity', severity)}
                    >
                      <X size={10} />
                    </Button>
                  </div>
                ))}
                <Button
                  size="xs"
                  variant="ghost"
                  className="military-button-sm"
                  onClick={() => removeFilter('severity')}
                >
                  <X size={10} className="mr-1" />
                  Clear Severities
                </Button>
              </>
            )}
            
            {/* Status filters */}
            {statuses.length > 0 && (
              <>
                {statuses.map(status => (
                  <div 
                    key={`status-${status}`}
                    className={`flex items-center px-2 py-1 rounded text-xs text-white ${getFilterColor('status')}`}
                  >
                    <CheckCircle size={12} className="mr-1" />
                    <span>{getValueLabel('status', status)}</span>
                    <Button
                      size="xs"
                      variant="ghost"
                      className="ml-1 p-0 h-4 w-4 hover:bg-military-panel"
                      onClick={() => removeFilter('status', status)}
                    >
                      <X size={10} />
                    </Button>
                  </div>
                ))}
                <Button
                  size="xs"
                  variant="ghost"
                  className="military-button-sm"
                  onClick={() => removeFilter('status')}
                >
                  <X size={10} className="mr-1" />
                  Clear Statuses
                </Button>
              </>
            )}
            
            {/* Action filters */}
            {actions.length > 0 && (
              <>
                {actions.map(action => (
                  <div 
                    key={`action-${action}`}
                    className={`flex items-center px-2 py-1 rounded text-xs text-white ${getFilterColor('action')}`}
                  >
                    <Activity size={12} className="mr-1" />
                    <span>{getValueLabel('action', action)}</span>
                    <Button
                      size="xs"
                      variant="ghost"
                      className="ml-1 p-0 h-4 w-4 hover:bg-military-panel"
                      onClick={() => removeFilter('action', action)}
                    >
                      <X size={10} />
                    </Button>
                  </div>
                ))}
                <Button
                  size="xs"
                  variant="ghost"
                  className="military-button-sm"
                  onClick={() => removeFilter('action')}
                >
                  <X size={10} className="mr-1" />
                  Clear Actions
                </Button>
              </>
            )}
            
            {/* Date range filter */}
            {(dateRange.startDate || dateRange.endDate) && (
              <div 
                className={`flex items-center px-2 py-1 rounded text-xs text-white ${getFilterColor('dateRange')}`}
              >
                <Calendar size={12} className="mr-1" />
                <span>
                  {dateRange.startDate ? format(dateRange.startDate, 'yyyy-MM-dd') : 'Any'} to {' '}
                  {dateRange.endDate ? format(dateRange.endDate, 'yyyy-MM-dd') : 'Any'}
                </span>
                <Button
                  size="xs"
                  variant="ghost"
                  className="ml-1 p-0 h-4 w-4 hover:bg-military-panel"
                  onClick={() => removeFilter('dateRange')}
                >
                  <X size={10} />
                </Button>
              </div>
            )}
            
            {/* Search term filter */}
            {searchTerm && (
              <div 
                className={`flex items-center px-2 py-1 rounded text-xs text-white ${getFilterColor('searchTerm')}`}
              >
                <Search size={12} className="mr-1" />
                <span>"{searchTerm}"</span>
                <Button
                  size="xs"
                  variant="ghost"
                  className="ml-1 p-0 h-4 w-4 hover:bg-military-panel"
                  onClick={() => removeFilter('searchTerm')}
                >
                  <X size={10} />
                </Button>
              </div>
            )}
            
            {/* Tags filters */}
            {tags.length > 0 && (
              <>
                {tags.map(tag => (
                  <div 
                    key={`tag-${tag}`}
                    className={`flex items-center px-2 py-1 rounded text-xs text-white ${getFilterColor('tags')}`}
                  >
                    <Tag size={12} className="mr-1" />
                    <span>{tag}</span>
                    <Button
                      size="xs"
                      variant="ghost"
                      className="ml-1 p-0 h-4 w-4 hover:bg-military-panel"
                      onClick={() => removeFilter('tags', tag)}
                    >
                      <X size={10} />
                    </Button>
                  </div>
                ))}
                <Button
                  size="xs"
                  variant="ghost"
                  className="military-button-sm"
                  onClick={() => removeFilter('tags')}
                >
                  <X size={10} className="mr-1" />
                  Clear Tags
                </Button>
              </>
            )}
            
            {/* Spatial filter */}
            {spatial.active && (
              <div 
                className={`flex items-center px-2 py-1 rounded text-xs text-white ${getFilterColor('spatial')}`}
              >
                <Map size={12} className="mr-1" />
                <span>Map Selection</span>
                <Button
                  size="xs"
                  variant="ghost"
                  className="ml-1 p-0 h-4 w-4 hover:bg-military-panel"
                  onClick={() => removeFilter('spatial')}
                >
                  <X size={10} />
                </Button>
              </div>
            )}
            
            {/* Chart filters */}
            {chartFilters.length > 0 && (
              <>
                {chartFilters.map((filter, index) => (
                  <div 
                    key={`chart-${index}`}
                    className={`flex items-center px-2 py-1 rounded text-xs text-white ${getFilterColor('chart')}`}
                  >
                    <PieChart size={12} className="mr-1" />
                    <span>{filter.label}</span>
                    <Button
                      size="xs"
                      variant="ghost"
                      className="ml-1 p-0 h-4 w-4 hover:bg-military-panel"
                      onClick={() => removeFilter('chart', filter.value)}
                    >
                      <X size={10} />
                    </Button>
                  </div>
                ))}
                <Button
                  size="xs"
                  variant="ghost"
                  className="military-button-sm"
                  onClick={() => removeFilter('chart')}
                >
                  <X size={10} className="mr-1" />
                  Clear Chart Filters
                </Button>
              </>
            )}
          </div>
          
          {/* Reset all filters button */}
          <div className="flex justify-end">
            <Button
              size="sm"
              variant="military"
              className="military-button"
              onClick={resetAllFilters}
            >
              <RefreshCw size={14} className="mr-2" />
              Reset All Filters
            </Button>
          </div>
        </div>
      )}
    </Card>
  );
};

export default FilterPanel;
