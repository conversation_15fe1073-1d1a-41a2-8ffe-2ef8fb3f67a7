import pkg from 'xlsx';
const { readFile, utils, writeFile } = pkg;
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';

// Path to the Excel file
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const inputFilePath = resolve(__dirname, 'RESPONSE SCW.xlsx');
const outputFilePath = resolve(__dirname, 'RESPONSE SCW - Fixed.xlsx');

/**
 * Converts a date string from MM/DD/YY format to YYYY-MM-DD format
 * @param {string} dateStr - The date string to convert
 * @returns {string} - The converted date string
 */
function convertDateFormat(dateStr) {
  if (!dateStr || typeof dateStr !== 'string') {
    return dateStr;
  }

  // Check if it matches the MM/DD/YY format
  const match = dateStr.match(/^(\d{1,2})\/(\d{1,2})\/(\d{2})$/);
  if (!match) {
    return dateStr;
  }

  const month = match[1].padStart(2, '0');
  const day = match[2].padStart(2, '0');
  let year = match[3];
  
  // Assume 20xx for years less than 50, 19xx for years 50 and above
  if (parseInt(year) < 50) {
    year = `20${year}`;
  } else {
    year = `19${year}`;
  }

  return `${year}-${month}-${day}`;
}

/**
 * Fixes the date formats in the Excel file
 */
function fixExcelDateFormats() {
  try {
    console.log(`Reading Excel file: ${inputFilePath}`);
    
    // Read the Excel file
    const workbook = readFile(inputFilePath);
    
    // Get the first worksheet
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    // Convert worksheet to JSON
    const jsonData = utils.sheet_to_json(worksheet);
    
    console.log(`Total rows: ${jsonData.length}`);
    
    // Fix date formats
    const fixedData = jsonData.map(row => {
      const fixedRow = { ...row };
      
      // Fix startDate if it exists
      if (fixedRow.startDate) {
        const originalDate = fixedRow.startDate;
        fixedRow.startDate = convertDateFormat(String(originalDate));
        console.log(`Row: ${fixedRow.title} - startDate: ${originalDate} -> ${fixedRow.startDate}`);
      }
      
      // Fix endDate if it exists
      if (fixedRow.endDate) {
        const originalDate = fixedRow.endDate;
        fixedRow.endDate = convertDateFormat(String(originalDate));
        console.log(`Row: ${fixedRow.title} - endDate: ${originalDate} -> ${fixedRow.endDate}`);
      }
      
      return fixedRow;
    });
    
    // Convert back to worksheet
    const newWorksheet = utils.json_to_sheet(fixedData);
    
    // Create a new workbook
    const newWorkbook = utils.book_new();
    utils.book_append_sheet(newWorkbook, newWorksheet, sheetName);
    
    // Write to a new file
    writeFile(newWorkbook, outputFilePath);
    
    console.log(`\nFixed Excel file saved to: ${outputFilePath}`);
    
    return fixedData;
  } catch (error) {
    console.error('Error fixing Excel file:', error);
    return null;
  }
}

// Run the function
fixExcelDateFormats();
