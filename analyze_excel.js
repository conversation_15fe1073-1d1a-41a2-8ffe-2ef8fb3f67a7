import pkg from 'xlsx';
const { readFile, utils } = pkg;
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';

// Path to the Excel file
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const filePath = resolve(__dirname, 'RESPONSE SCW.xlsx');

// Function to analyze the Excel file
function analyzeExcelFile(filePath) {
  try {
    console.log(`Analyzing Excel file: ${filePath}`);

    // Read the Excel file
    const workbook = readFile(filePath);

    // Get the first worksheet
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    // Convert worksheet to JSON
    const jsonData = utils.sheet_to_json(worksheet, { raw: false, dateNF: 'yyyy-mm-dd' });

    console.log(`\nTotal rows: ${jsonData.length}`);

    if (jsonData.length > 0) {
      // Display column headers
      console.log('\nColumns found:');
      const headers = Object.keys(jsonData[0]);
      headers.forEach(header => {
        console.log(`- ${header}`);
      });

      // Check for date columns
      const dateColumns = ['startDate', 'endDate', 'Start Date', 'End Date', 'START DATE', 'END DATE'];
      const foundDateColumns = headers.filter(header =>
        dateColumns.some(dateCol => header.toLowerCase().includes(dateCol.toLowerCase()))
      );

      if (foundDateColumns.length > 0) {
        console.log('\nPotential date columns found:');
        foundDateColumns.forEach(column => {
          console.log(`\nColumn: ${column}`);

          // Display sample values from the date column
          console.log('Sample values:');
          for (let i = 0; i < Math.min(5, jsonData.length); i++) {
            const value = jsonData[i][column];
            console.log(`Row ${i+1}: ${value} (Type: ${typeof value})`);
          }
        });

        // Analyze date formats
        console.log('\nDate format analysis:');
        foundDateColumns.forEach(column => {
          const uniqueFormats = new Set();
          const samples = {};

          jsonData.forEach((row, index) => {
            const value = row[column];
            if (value) {
              // Try to identify the format
              let format = 'unknown';

              if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(value)) {
                format = 'MM/DD/YYYY or DD/MM/YYYY';
              } else if (/^\d{4}-\d{1,2}-\d{1,2}$/.test(value)) {
                format = 'YYYY-MM-DD';
              } else if (/^\d{1,2}-\d{1,2}-\d{4}$/.test(value)) {
                format = 'DD-MM-YYYY or MM-DD-YYYY';
              } else if (/^\d{1,2}\.\d{1,2}\.\d{4}$/.test(value)) {
                format = 'DD.MM.YYYY or MM.DD.YYYY';
              } else if (/^\d{1,2} [A-Za-z]+ \d{4}$/.test(value)) {
                format = 'DD Month YYYY';
              }

              uniqueFormats.add(format);
              if (!samples[format]) {
                samples[format] = value;
              }
            }
          });

          console.log(`\nColumn: ${column}`);
          console.log('Detected formats:');
          uniqueFormats.forEach(format => {
            console.log(`- ${format} (Example: ${samples[format]})`);
          });
        });
      } else {
        console.log('\nNo date columns found.');
      }

      // Display a sample row
      console.log('\nSample row (first row):');
      console.log(JSON.stringify(jsonData[0], null, 2));
    }

    return jsonData;
  } catch (error) {
    console.error('Error analyzing Excel file:', error);
    return null;
  }
}

// Run the analysis
analyzeExcelFile(filePath);
