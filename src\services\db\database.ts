import Dexie, { Table } from 'dexie';
import { Incident, Response } from '@/types/incident';
import { mockIncidents } from './mockData';
import { mockResponses } from './mockResponses';

class IncidentDatabase extends Dexie {
  incidents!: Table<Incident, string>;
  responses!: Table<Response, string>;

  constructor() {
    super('IncidentsDB');
    this.version(4).stores({
      incidents: 'id, type, action, severity, status, reportedAt, resolvedAt',
      responses: 'id, type, status, startDate, endDate, commander'
    });
  }

  async initializeDemoData() {
    try {
      const incidentCount = await this.incidents.count();
      const responseCount = await this.responses.count();

      // Initialize incidents if needed
      if (incidentCount === 0) {
        console.log('Initializing incident demo data...');
        await this.incidents.clear(); // Clear any existing data

        // Add data in smaller batches to prevent memory issues
        const batchSize = 20;
        for (let i = 0; i < mockIncidents.length; i += batchSize) {
          const batch = mockIncidents.slice(i, i + batchSize);
          await this.incidents.bulkAdd(batch);
        }
        console.log('Incident demo data initialized successfully');
      } else {
        console.log('Incident database already contains data, skipping initialization');
      }

      // Initialize responses if needed
      if (responseCount === 0) {
        console.log('Initializing response demo data...');
        await this.responses.clear(); // Clear any existing data

        // Add response data in batches
        const batchSize = 20;
        for (let i = 0; i < mockResponses.length; i += batchSize) {
          const batch = mockResponses.slice(i, i + batchSize);
          await this.responses.bulkAdd(batch);
        }
        console.log('Response demo data initialized successfully');
      } else {
        console.log('Response database already contains data, skipping initialization');
      }
    } catch (error) {
      console.error('Failed to initialize demo data:', error);
      throw error;
    }
  }

  async resetDatabase() {
    try {
      await this.incidents.clear();
      await this.responses.clear();
      await this.initializeDemoData();
      return true;
    } catch (error) {
      console.error('Failed to reset database:', error);
      return false;
    }
  }
}

export const db = new IncidentDatabase();

// Initialize the database
export const initDatabase = async () => {
  try {
    // Check if IndexedDB is available
    if (!window.indexedDB) {
      throw new Error('IndexedDB is not supported in this browser');
    }

    // Request storage persistence
    if (navigator.storage && navigator.storage.persist) {
      try {
        const isPersisted = await navigator.storage.persist();
        console.log(`Storage persistence: ${isPersisted ? 'granted' : 'denied'}`);

        // Emit a custom event if persistence is denied
        if (!isPersisted) {
          const storageEvent = new CustomEvent('storage-error', {
            detail: {
              type: 'persistence-denied',
              message: 'Storage persistence denied by browser'
            }
          });
          window.dispatchEvent(storageEvent);
        }
      } catch (error) {
        console.error('Error requesting storage persistence:', error);
        // Emit a custom event for storage error
        const storageEvent = new CustomEvent('storage-error', {
          detail: {
            type: 'persistence-error',
            message: 'Error requesting storage persistence',
            error
          }
        });
        window.dispatchEvent(storageEvent);
      }
    }

    // Open database with retry mechanism
    let retries = 3;
    while (retries > 0) {
      try {
        await db.open();
        console.log('Database opened successfully');
        await db.initializeDemoData();
        return true;
      } catch (error) {
        retries--;
        if (retries === 0) throw error;
        console.log(`Database open failed, retrying... (${retries} attempts left)`);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return false;
  } catch (error) {
    console.error('Failed to initialize database:', error);
    return false;
  }
};

export default db;