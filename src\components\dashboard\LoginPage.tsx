import React, { useState } from 'react';
import { Lock, Mail } from 'lucide-react';
import Button from '@/components/ui/Button';

interface LoginPageProps {
  onLoginSuccess: () => void;
}

// Hardcoded credentials (for development only!)
const VALID_CREDENTIALS = [
  { email: '<EMAIL>', password: 'admin123' },
  { email: '<EMAIL>', password: 'user123' }
];

const LoginPage: React.FC<LoginPageProps> = ({ onLoginSuccess }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Check against hardcoded credentials
      const isValid = VALID_CREDENTIALS.some(
        cred => cred.email === email && cred.password === password
      );

      if (isValid) {
        onLoginSuccess();
      } else {
        setError('Invalid email or password');
      }
    } catch (err) {
      setError('Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center military-theme p-4">
      <div className="w-full max-w-md military-container bg-military-panel border border-military-border p-8"
           style={{ clipPath: 'polygon(0 0, calc(100% - 12px) 0, 100% 12px, 100% 100%, 12px 100%, 0 calc(100% - 12px))' }}>
        <div className="text-center mb-8">
          <div className="mx-auto flex flex-col items-center justify-center mb-4">
            <img src="/Resources/logo.png" alt="Logo" className="h-20 mb-4" />
            <h2 className="text-2xl font-military-heading text-military-white uppercase tracking-wider">
              FARHAD SCW DECISION SUPPORT SYSTEM
            </h2>
          </div>
          <div className="mx-auto flex items-center justify-center h-12 w-12 military-container bg-military-navy mb-4">
            <Lock className="h-6 w-6 text-military-white" />
          </div>
          <h3 className="text-xl font-military-heading text-military-white uppercase tracking-wider">
            SECURE ACCESS PORTAL
          </h3>
          <p className="mt-2 text-sm font-military-body text-military-white opacity-80">
            MONITOR • ANALYZE • RESPOND
          </p>
        </div>

        {error && (
          <div className="mb-4 p-3 military-alert text-military-white text-sm"
               style={{ clipPath: 'polygon(0 0, calc(100% - 8px) 0, 100% 8px, 100% 100%, 8px 100%, 0 calc(100% - 8px))' }}>
            {error}
          </div>
        )}

        <form className="space-y-6" onSubmit={handleSubmit}>
          <div>
            <label htmlFor="email" className="block text-sm font-military-body text-military-white uppercase tracking-wider mb-1">
              AUTHENTICATION ID
            </label>
            <div className="relative military-container">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Mail className="h-5 w-5 text-military-accent" />
              </div>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border-0 bg-military-navy text-military-white placeholder-military-white placeholder-opacity-50 focus:outline-none focus:ring-1 focus:ring-military-accent sm:text-sm font-military-body"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-military-body text-military-white uppercase tracking-wider mb-1">
              SECURITY CODE
            </label>
            <div className="relative military-container">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Lock className="h-5 w-5 text-military-accent" />
              </div>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border-0 bg-military-navy text-military-white placeholder-military-white placeholder-opacity-50 focus:outline-none focus:ring-1 focus:ring-military-accent sm:text-sm font-military-body"
                placeholder="••••••••"
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                className="h-4 w-4 bg-military-navy border-military-border text-military-accent focus:ring-military-accent"
              />
              <label htmlFor="remember-me" className="ml-2 block text-sm font-military-body text-military-white uppercase">
                REMEMBER CREDENTIALS
              </label>
            </div>

            <div className="text-sm">
              <a href="#" className="font-military-body text-military-accent hover:text-military-white uppercase">
                RESET ACCESS
              </a>
            </div>
          </div>

          <div>
            <Button
              type="submit"
              variant="military"
              className="w-full flex justify-center military-btn bg-military-darkgreen"
              loading={loading}
              disabled={loading}
            >
              AUTHENTICATE
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginPage;