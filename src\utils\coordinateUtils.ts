/**
 * Utility functions for handling geographic coordinates
 */

/**
 * Coordinate format types
 */
export enum CoordinateFormat {
  DECIMAL_DEGREES = 'DECIMAL_DEGREES',
  DMS = 'DMS',
  WASHUK_FORMAT = 'WASHUK_FORMAT',
  UNKNOWN = 'UNKNOWN'
}

/**
 * Detects the format of a coordinate string
 * @param value The coordinate string to check
 * @returns The detected coordinate format
 */
export const detectCoordinateFormat = (value: string): CoordinateFormat => {
  if (!value || typeof value !== 'string') {
    return CoordinateFormat.UNKNOWN;
  }

  // Clean the input
  const cleanValue = value.trim();

  // Check if it's a simple decimal number
  if (/^-?\d+(\.\d+)?$/.test(cleanValue)) {
    return CoordinateFormat.DECIMAL_DEGREES;
  }

  // Check for DMS format with symbols (°, ', ")
  if (/^\s*-?\d+\s*[°˚º⁰o]\s*\d+\s*['′ʹ]\s*\d+(\.\d+)?\s*["″ʺ]\s*[NSEW]?\s*$/.test(cleanValue)) {
    return CoordinateFormat.DMS;
  }

  // Check for DMS format with letters (N, S, E, W) and spaces or symbols
  if (/^\s*(\d+)\s*[ °˚º⁰o]*\s*(\d+)\s*[ '′ʹ]*\s*(\d+(\.\d+)?)\s*[ "″ʺ]*\s*([NSEW])\s*$/.test(cleanValue)) {
    return CoordinateFormat.DMS;
  }

  // Check for DMS format with numbers and spaces (e.g., "40 26 46.302 N")
  if (/^\s*\d+\s+\d+\s+\d+(\.\d+)?\s*[NSEW]?\s*$/.test(cleanValue)) {
    return CoordinateFormat.DMS;
  }

  // Check for DMS format with numbers and colons (e.g., "40:26:46.302N")
  if (/^\s*\d+\s*:\s*\d+\s*:\s*\d+(\.\d+)?\s*[NSEW]?\s*$/.test(cleanValue)) {
    return CoordinateFormat.DMS;
  }

  // Check for Washuk district format with double quotes (e.g., " 27°28'10.76""N")
  if (/^\s*"?\s*\d+\s*°\s*\d+\s*'\s*\d+(\.\d+)?\s*"{1,2}[NSEW]\s*"{0,1}\s*$/.test(cleanValue)) {
    return CoordinateFormat.WASHUK_FORMAT;
  }

  return CoordinateFormat.UNKNOWN;
};

/**
 * Converts a DMS (Degrees, Minutes, Seconds) coordinate to decimal degrees
 * @param dmsStr The DMS coordinate string (e.g., "40°26'46.302\"N" or "40 26 46.302 N")
 * @returns The coordinate in decimal degrees or null if conversion fails
 */
export const dmsToDecimal = (dmsStr: string): number | null => {
  if (!dmsStr || typeof dmsStr !== 'string') {
    return null;
  }

  try {
    // Clean the input
    const cleanStr = dmsStr.trim();

    // Handle different DMS formats

    // Format with symbols (°, ', ")
    let match = cleanStr.match(/^\s*(-?\d+)\s*[°˚º⁰o]\s*(\d+)\s*['′ʹ]\s*(\d+(\.\d+)?)\s*["″ʺ]\s*([NSEW])?\s*$/);
    if (match) {
      const degrees = parseInt(match[1], 10);
      const minutes = parseInt(match[2], 10);
      const seconds = parseFloat(match[3]);
      const direction = match[5] || '';

      return calculateDecimalDegrees(degrees, minutes, seconds, direction);
    }

    // Format with letters (N, S, E, W) and spaces or symbols
    match = cleanStr.match(/^\s*(\d+)\s*[ °˚º⁰o]*\s*(\d+)\s*[ '′ʹ]*\s*(\d+(\.\d+)?)\s*[ "″ʺ]*\s*([NSEW])\s*$/);
    if (match) {
      const degrees = parseInt(match[1], 10);
      const minutes = parseInt(match[2], 10);
      const seconds = parseFloat(match[3]);
      const direction = match[5];

      return calculateDecimalDegrees(degrees, minutes, seconds, direction);
    }

    // Format with numbers and spaces (e.g., "40 26 46.302 N")
    match = cleanStr.match(/^\s*(\d+)\s+(\d+)\s+(\d+(\.\d+)?)\s*([NSEW])?\s*$/);
    if (match) {
      const degrees = parseInt(match[1], 10);
      const minutes = parseInt(match[2], 10);
      const seconds = parseFloat(match[3]);
      const direction = match[5] || '';

      return calculateDecimalDegrees(degrees, minutes, seconds, direction);
    }

    // Format with numbers and colons (e.g., "40:26:46.302N")
    match = cleanStr.match(/^\s*(\d+)\s*:\s*(\d+)\s*:\s*(\d+(\.\d+)?)\s*([NSEW])?\s*$/);
    if (match) {
      const degrees = parseInt(match[1], 10);
      const minutes = parseInt(match[2], 10);
      const seconds = parseFloat(match[3]);
      const direction = match[5] || '';

      return calculateDecimalDegrees(degrees, minutes, seconds, direction);
    }

    // Washuk district format with double quotes (e.g., " 27°28'10.76""N")
    // First, clean up the string by removing extra quotes and spaces
    const washukStr = cleanStr.replace(/"{2}/g, '"').trim();
    match = washukStr.match(/^\s*(\d+)\s*°\s*(\d+)\s*'\s*(\d+(\.\d+)?)\s*"?([NSEW])"?\s*$/);
    if (match) {
      const degrees = parseInt(match[1], 10);
      const minutes = parseInt(match[2], 10);
      const seconds = parseFloat(match[3]);
      const direction = match[5];

      return calculateDecimalDegrees(degrees, minutes, seconds, direction);
    }

    // Try a more general pattern for DMS format
    // This will attempt to extract degrees, minutes, seconds, and direction from various formats
    const generalPattern = /^\s*"?\s*(\d+)\s*[°˚º⁰o\s]\s*(\d+)\s*['′ʹ\s]\s*(\d+(?:\.\d+)?)\s*["″ʺ]?\s*([NSEW])\s*"?\s*$/i;
    match = cleanStr.replace(/"{2}/g, '"').match(generalPattern);
    if (match) {
      const degrees = parseInt(match[1], 10);
      const minutes = parseInt(match[2], 10);
      const seconds = parseFloat(match[3]);
      const direction = match[4].toUpperCase();

      return calculateDecimalDegrees(degrees, minutes, seconds, direction);
    }

    // Last resort: try to extract numbers and direction from any string that looks like DMS
    const lastResortPattern = /(\d+)[^\d]+(\d+)[^\d]+(\d+(?:\.\d+)?)[^\d]*([NSEW])/i;
    match = cleanStr.replace(/"{2}/g, '"').match(lastResortPattern);
    if (match) {
      const degrees = parseInt(match[1], 10);
      const minutes = parseInt(match[2], 10);
      const seconds = parseFloat(match[3]);
      const direction = match[4].toUpperCase();

      // Validate the extracted values before returning
      if (degrees >= 0 && degrees <= 180 && minutes >= 0 && minutes < 60 && seconds >= 0 && seconds < 60) {
        console.log(`Last resort DMS parsing: ${cleanStr} -> ${degrees}° ${minutes}' ${seconds}" ${direction}`);
        return calculateDecimalDegrees(degrees, minutes, seconds, direction);
      }
    }

    return null;
  } catch (error) {
    console.error('Error converting DMS to decimal:', error);
    return null;
  }
};

/**
 * Calculates decimal degrees from DMS components
 * @param degrees Degrees component
 * @param minutes Minutes component
 * @param seconds Seconds component
 * @param direction Direction (N, S, E, W)
 * @returns The coordinate in decimal degrees
 */
const calculateDecimalDegrees = (
  degrees: number,
  minutes: number,
  seconds: number,
  direction: string
): number => {
  // Validate inputs
  if (minutes < 0 || minutes >= 60 || seconds < 0 || seconds >= 60) {
    throw new Error('Invalid DMS values: minutes and seconds must be between 0 and 60');
  }

  // Calculate decimal degrees
  let decimal = degrees + minutes / 60 + seconds / 3600;

  // Apply direction
  if (direction === 'S' || direction === 's' || direction === 'W' || direction === 'w') {
    decimal = -decimal;
  }

  return decimal;
};

/**
 * Parses coordinates in the Washuk district format
 * @param value The coordinate string in Washuk format (e.g., " 27°28'10.76""N")
 * @returns The coordinate in decimal degrees or null if parsing fails
 */
export const parseWashukFormat = (value: string): number | null => {
  if (!value) return null;

  try {
    // Clean up the string
    let cleanStr = value.trim();

    // Remove surrounding quotes if present
    cleanStr = cleanStr.replace(/^["']|["']$/g, '').trim();

    // Replace double quotes with single quotes
    cleanStr = cleanStr.replace(/"{2}/g, '"');

    // Try to match the pattern
    const pattern = /\s*(\d+)\s*°\s*(\d+)\s*'\s*(\d+(?:\.\d+)?)\s*"?([NSEW])"?\s*/i;
    const match = cleanStr.match(pattern);

    if (match) {
      const degrees = parseInt(match[1], 10);
      const minutes = parseInt(match[2], 10);
      const seconds = parseFloat(match[3]);
      const direction = match[4].toUpperCase();

      console.log(`Parsed Washuk format: ${cleanStr} -> ${degrees}° ${minutes}' ${seconds}" ${direction}`);
      return calculateDecimalDegrees(degrees, minutes, seconds, direction);
    }

    return null;
  } catch (error) {
    console.error('Error parsing Washuk format:', error);
    return null;
  }
};

/**
 * Preprocesses a coordinate string to clean it up for parsing
 * @param value The raw coordinate string
 * @returns The cleaned coordinate string
 */
export const preprocessCoordinate = (value: string): string => {
  if (!value) return '';

  // Remove any surrounding quotes
  let cleaned = value.replace(/^["']|["']$/g, '').trim();

  // Replace double quotes with single quotes
  cleaned = cleaned.replace(/"{2}/g, '"');

  // Remove any extra spaces
  cleaned = cleaned.replace(/\s+/g, ' ').trim();

  // Handle the specific Washuk district format
  if (cleaned.includes('°') && cleaned.includes("'") && cleaned.includes('"')) {
    // Extract the components
    const match = cleaned.match(/(\d+)\s*°\s*(\d+)\s*'\s*(\d+(?:\.\d+)?)\s*"?\s*([NSEW])/i);
    if (match) {
      const [_, degrees, minutes, seconds, direction] = match;
      // Reformat to a standard format
      return `${degrees}° ${minutes}' ${seconds}" ${direction}`;
    }
  }

  return cleaned;
};

/**
 * Attempts to parse a coordinate string to decimal degrees
 * @param value The coordinate string to parse
 * @returns The coordinate in decimal degrees or null if parsing fails
 */
export const parseCoordinate = (value: string): number | null => {
  if (!value) return null;

  // Preprocess the coordinate string
  const cleanedValue = preprocessCoordinate(value);

  // Detect the format
  const format = detectCoordinateFormat(cleanedValue);

  // Handle based on format
  if (format === CoordinateFormat.DECIMAL_DEGREES) {
    return parseFloat(cleanedValue);
  } else if (format === CoordinateFormat.DMS) {
    return dmsToDecimal(cleanedValue);
  } else if (format === CoordinateFormat.WASHUK_FORMAT) {
    return parseWashukFormat(cleanedValue);
  }

  // Try direct decimal parsing as fallback
  const directDecimal = parseFloat(cleanedValue);
  if (!isNaN(directDecimal) && /^-?\d+(\.\d+)?$/.test(cleanedValue.trim())) {
    return directDecimal;
  }

  // Try DMS parsing as fallback
  const dmsResult = dmsToDecimal(cleanedValue);
  if (dmsResult !== null) {
    return dmsResult;
  }

  // If all else fails, try the last resort pattern directly
  const lastResortPattern = /(\d+)[^\d]+(\d+)[^\d]+(\d+(?:\.\d+)?)[^\d]*([NSEW])/i;
  const match = cleanedValue.match(lastResortPattern);
  if (match) {
    const degrees = parseInt(match[1], 10);
    const minutes = parseInt(match[2], 10);
    const seconds = parseFloat(match[3]);
    const direction = match[4].toUpperCase();

    // Validate the extracted values before returning
    if (degrees >= 0 && degrees <= 180 && minutes >= 0 && minutes < 60 && seconds >= 0 && seconds < 60) {
      console.log(`Last resort DMS parsing: ${cleanedValue} -> ${degrees}° ${minutes}' ${seconds}" ${direction}`);
      return calculateDecimalDegrees(degrees, minutes, seconds, direction);
    }
  }

  return null;
};

/**
 * Checks if a coordinate is valid (within range)
 * @param value The coordinate value
 * @param isLatitude Whether the coordinate is a latitude
 * @returns True if the coordinate is valid, false otherwise
 */
export const isValidCoordinate = (value: number, isLatitude: boolean): boolean => {
  if (value === null || value === undefined || isNaN(value)) {
    return false;
  }

  if (isLatitude) {
    return value >= -90 && value <= 90;
  } else {
    return value >= -180 && value <= 180;
  }
};

/**
 * Formats a decimal degree coordinate as a string
 * @param value The decimal degree value
 * @param isLatitude Whether the coordinate is a latitude
 * @param precision The number of decimal places to include
 * @returns The formatted coordinate string
 */
export const formatDecimalDegrees = (
  value: number,
  isLatitude: boolean = true,
  precision: number = 6
): string => {
  if (value === null || value === undefined || isNaN(value)) {
    return '';
  }

  const direction = isLatitude
    ? (value >= 0 ? 'N' : 'S')
    : (value >= 0 ? 'E' : 'W');

  return `${Math.abs(value).toFixed(precision)}° ${direction}`;
};
