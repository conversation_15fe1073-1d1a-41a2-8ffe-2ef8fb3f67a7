import React, { useState, ReactNode } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';
import Button from '@/components/ui/Button';

interface ControlContainerProps {
  title: string;
  icon?: ReactNode;
  position?: 'topleft' | 'topright' | 'bottomleft' | 'bottomright';
  initialCollapsed?: boolean;
  className?: string;
  children: ReactNode;
}

const ControlContainer: React.FC<ControlContainerProps> = ({
  title,
  icon,
  position = 'topright',
  initialCollapsed = false,
  className = '',
  children
}) => {
  const [isCollapsed, setIsCollapsed] = useState(initialCollapsed);

  const getPositionClasses = () => {
    switch (position) {
      case 'topleft':
        return 'top-2 left-2';
      case 'topright':
        return 'top-2 right-2';
      case 'bottomleft':
        return 'bottom-2 left-2';
      case 'bottomright':
        return 'bottom-2 right-2';
      default:
        return 'top-2 right-2';
    }
  };

  return (
    <div className={`absolute z-1000 bg-gray-900 bg-opacity-90 rounded ${getPositionClasses()} ${className}`}>
      <div className="flex items-center justify-between p-2 border-b border-gray-700">
        <div className="flex items-center space-x-2">
          {icon && <span className="text-white">{icon}</span>}
          <span className="text-white text-xs font-bold">{title}</span>
        </div>
        <Button
          size="sm"
          variant="ghost"
          className="p-1 text-white"
          onClick={() => setIsCollapsed(!isCollapsed)}
        >
          {isCollapsed ? <ChevronDown size={14} /> : <ChevronUp size={14} />}
        </Button>
      </div>
      {!isCollapsed && (
        <div className="p-2">
          {children}
        </div>
      )}
    </div>
  );
};

export default ControlContainer;
