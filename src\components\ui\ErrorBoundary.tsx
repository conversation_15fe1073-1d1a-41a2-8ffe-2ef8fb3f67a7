import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON>riangle, RefreshCw } from 'lucide-react';
import { errorService, ErrorCategory } from '@/services/logging/errorService';
import Button from './Button';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null
  };

  public static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error, errorInfo: null };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to our error service
    errorService.logError(
      error,
      'Component error caught by ErrorBoundary',
      ErrorCategory.UI,
      { componentStack: errorInfo.componentStack }
    );
    
    // Update state with error details
    this.setState({ errorInfo });
  }

  private handleReload = (): void => {
    window.location.reload();
  };

  private handleReset = (): void => {
    this.setState({ hasError: false, error: null, errorInfo: null });
  };

  public render(): ReactNode {
    if (this.state.hasError) {
      // If a custom fallback is provided, use it
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Otherwise, use our default military-themed error UI
      return (
        <div className="min-h-screen military-theme flex items-center justify-center p-4">
          <div className="military-container bg-military-black max-w-2xl w-full p-6"
               style={{ clipPath: 'polygon(0 0, calc(100% - 12px) 0, 100% 12px, 100% 100%, 12px 100%, 0 calc(100% - 12px))' }}>
            <div className="bg-military-red p-4 mb-6 flex items-start"
                 style={{ clipPath: 'polygon(0 0, calc(100% - 8px) 0, 100% 8px, 100% 100%, 8px 100%, 0 calc(100% - 8px))' }}>
              <AlertTriangle size={24} className="text-military-white mr-3 mt-1 flex-shrink-0" />
              <div>
                <h2 className="text-lg font-military-heading text-military-white uppercase tracking-wider mb-2">
                  SYSTEM MALFUNCTION
                </h2>
                <p className="text-military-white font-military-body">
                  A critical error has occurred in the application. Technical details have been logged.
                </p>
              </div>
            </div>
            
            {/* Error details */}
            <div className="mb-6">
              <h3 className="text-sm font-military-heading text-military-white uppercase mb-2">ERROR DETAILS:</h3>
              <div className="bg-military-navy border border-military-border p-3 font-mono text-sm text-military-white overflow-x-auto"
                   style={{ clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' }}>
                <p>{this.state.error?.toString()}</p>
              </div>
            </div>
            
            {/* Component stack */}
            {this.state.errorInfo && (
              <div className="mb-6">
                <h3 className="text-sm font-military-heading text-military-white uppercase mb-2">COMPONENT STACK:</h3>
                <div className="bg-military-navy border border-military-border p-3 font-mono text-xs text-military-white overflow-x-auto max-h-40"
                     style={{ clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' }}>
                  <pre>{this.state.errorInfo.componentStack}</pre>
                </div>
              </div>
            )}
            
            {/* Action buttons */}
            <div className="flex justify-end space-x-3">
              <Button 
                variant="military" 
                onClick={this.handleReset}
                className="military-btn"
              >
                RETRY
              </Button>
              <Button 
                variant="military" 
                onClick={this.handleReload}
                className="military-btn bg-military-darkgreen"
                leftIcon={<RefreshCw size={16} />}
              >
                RELOAD APPLICATION
              </Button>
            </div>
          </div>
        </div>
      );
    }

    // If there's no error, render children normally
    return this.props.children;
  }
}

export default ErrorBoundary;
