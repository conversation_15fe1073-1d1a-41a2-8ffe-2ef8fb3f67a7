import React, { useState, useEffect } from 'react';
import { X, MapPin } from 'lucide-react';
import Button from '@/components/ui/Button';
import { useIncidentStore } from '@/store/incidentStore';
import { useResponseStore } from '@/store/responseStore';
import {
  Incident,
  IncidentType,
  ActionType,
  IncidentSeverity,
  IncidentStatus,
  Response,
  ResponseStatus
} from '@/types/incident';

interface IncidentFormProps {
  incident?: Incident;
  response?: Response;
  onClose: () => void;
  mapCoordinates?: { latitude: number; longitude: number } | null;
  isResponse?: boolean;
}

const IncidentForm: React.FC<IncidentFormProps> = ({
  incident,
  response,
  onClose,
  mapCoordinates,
  isResponse = false
}) => {
  const { addIncident, updateIncident } = useIncidentStore();
  const { addResponse, updateResponse } = useResponseStore();

  // Initialize form data based on whether it's an incident or response
  const [formData, setFormData] = useState(
    isResponse ? {
      // Response form data
      title: response?.title || '',
      description: response?.description || '',
      type: response?.type || ActionType.NONE,
      status: response?.status || ResponseStatus.PLANNED,
      location: response?.location || mapCoordinates || { latitude: 34.0151, longitude: 71.5249 },
      address: response?.address || '',
      commander: response?.commander || '',
      units: response?.units?.join(', ') || '',
      startDate: response?.startDate ? new Date(response.startDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      endDate: response?.endDate ? new Date(response.endDate).toISOString().split('T')[0] : '',
      relatedIncidents: response?.relatedIncidents?.join(', ') || '',
      tags: response?.tags?.join(', ') || ''
    } : {
      // Incident form data
      title: incident?.title || '',
      description: incident?.description || '',
      type: incident?.type || IncidentType.TS_ACTIVITY,
      action: incident?.action || ActionType.NONE,
      severity: incident?.severity || IncidentSeverity.MEDIUM,
      status: incident?.status || IncidentStatus.REPORTED,
      location: incident?.location || mapCoordinates || { latitude: 34.0151, longitude: 71.5249 },
      address: incident?.address || '',
      reportedBy: incident?.reportedBy || '',
      assignedTo: incident?.assignedTo || '',
      tags: incident?.tags?.join(', ') || ''
    }
  );

  // Update coordinates if mapCoordinates changes
  useEffect(() => {
    if (mapCoordinates && !incident && !response) {
      setFormData(prev => ({
        ...prev,
        location: mapCoordinates
      }));
    }
  }, [mapCoordinates, incident, response]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (isResponse) {
        // Handle response form submission
        const responseData = {
          ...formData,
          tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
          units: (formData as any).units.split(',').map((unit: string) => unit.trim()).filter(Boolean),
          relatedIncidents: (formData as any).relatedIncidents.split(',').map((id: string) => id.trim()).filter(Boolean),
          startDate: new Date((formData as any).startDate),
          endDate: (formData as any).endDate ? new Date((formData as any).endDate) : undefined
        };

        if (response) {
          await updateResponse(response.id, responseData);
        } else {
          await addResponse(responseData);
        }
      } else {
        // Handle incident form submission
        const incidentData = {
          ...formData,
          tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
          reportedAt: incident?.reportedAt || new Date(),
          resolvedAt: incident?.resolvedAt
        };

        if (incident) {
          await updateIncident(incident.id, incidentData);
        } else {
          await addIncident(incidentData);
        }
      }

      onClose();
    } catch (error) {
      console.error(`Failed to save ${isResponse ? 'response' : 'incident'}:`, error);
    }
  };

  return (
    <div className="fixed inset-0 bg-military-black bg-opacity-80 flex items-center justify-center z-[1000] p-4">
      <div className="bg-military-panel border border-military-border max-w-2xl w-full max-h-[90vh] flex flex-col"
           style={{ clipPath: 'polygon(0 0, calc(100% - 12px) 0, 100% 12px, 100% 100%, 12px 100%, 0 calc(100% - 12px))' }}>
        <div className="flex items-center justify-between px-6 py-4 bg-military-navy border-b border-military-border">
          <h2 className="text-lg font-military-heading text-military-white uppercase tracking-wider">
            {isResponse
              ? (response ? 'EDIT TACTICAL RESPONSE' : 'NEW TACTICAL RESPONSE')
              : (incident ? 'EDIT TACTICAL INCIDENT' : 'NEW TACTICAL INCIDENT')
            }
          </h2>
          <Button
            variant="military"
            size="sm"
            onClick={onClose}
            className="military-btn"
          >
            <X size={18} />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="flex-grow overflow-y-auto p-6 bg-military-black">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-military-body text-military-white uppercase">Title</label>
              <input
                type="text"
                required
                className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              />
            </div>

            <div>
              <label className="block text-sm font-military-body text-military-white uppercase">Description</label>
              <textarea
                required
                rows={3}
                className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-military-body text-military-white uppercase">Type</label>
                <select
                  required
                  className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                  style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: isResponse ? e.target.value as ActionType : e.target.value as IncidentType })}
                >
                  {isResponse ? (
                    // Show action types for response form
                    Object.values(ActionType).map(type => (
                      <option key={type} value={type}>
                        {type.replace('_', ' ')}
                      </option>
                    ))
                  ) : (
                    // Show incident types for incident form
                    Object.values(IncidentType).map(type => (
                      <option key={type} value={type}>
                        {type.replace('TS_', '')}
                      </option>
                    ))
                  )}
                </select>
              </div>

              {!isResponse && (
                <div>
                  <label className="block text-sm font-military-body text-military-white uppercase">Action</label>
                  <select
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={formData.action}
                    onChange={(e) => setFormData({ ...formData, action: e.target.value as ActionType })}
                  >
                    {Object.values(ActionType).map(action => (
                      <option key={action} value={action}>
                        {action.replace('_', ' ')}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {!isResponse && (
                <div>
                  <label className="block text-sm font-military-body text-military-white uppercase">Severity</label>
                  <select
                    required
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={formData.severity}
                    onChange={(e) => setFormData({ ...formData, severity: e.target.value as IncidentSeverity })}
                  >
                    {Object.values(IncidentSeverity).map(severity => (
                      <option key={severity} value={severity}>{severity}</option>
                    ))}
                  </select>
                </div>
              )}

              <div>
                <label className="block text-sm font-military-body text-military-white uppercase">Status</label>
                <select
                  required
                  className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                  style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: isResponse ? e.target.value as ResponseStatus : e.target.value as IncidentStatus })}
                >
                  {isResponse ? (
                    // Show response statuses for response form
                    Object.values(ResponseStatus).map(status => (
                      <option key={status} value={status}>{status.replace('_', ' ')}</option>
                    ))
                  ) : (
                    // Show incident statuses for incident form
                    Object.values(IncidentStatus).map(status => (
                      <option key={status} value={status}>{status.replace('_', ' ')}</option>
                    ))
                  )}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-military-body text-military-white uppercase">Latitude</label>
                <div className="relative">
                  <input
                    type="number"
                    step="0.000001"
                    required
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={formData.location.latitude}
                    onChange={(e) => setFormData({
                      ...formData,
                      location: { ...formData.location, latitude: parseFloat(e.target.value) }
                    })}
                  />
                  <MapPin size={16} className="absolute right-3 top-1/2 transform -translate-y-1/2 text-military-accent" />
                </div>
              </div>

              <div>
                <label className="block text-sm font-military-body text-military-white uppercase">Longitude</label>
                <div className="relative">
                  <input
                    type="number"
                    step="0.000001"
                    required
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={formData.location.longitude}
                    onChange={(e) => setFormData({
                      ...formData,
                      location: { ...formData.location, longitude: parseFloat(e.target.value) }
                    })}
                  />
                  <MapPin size={16} className="absolute right-3 top-1/2 transform -translate-y-1/2 text-military-accent" />
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-military-body text-military-white uppercase">Address</label>
              <input
                type="text"
                required
                className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                value={formData.address}
                onChange={(e) => setFormData({ ...formData, address: e.target.value })}
              />
            </div>

            {!isResponse && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-military-body text-military-white uppercase">Reported By</label>
                  <input
                    type="text"
                    required
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={formData.reportedBy}
                    onChange={(e) => setFormData({ ...formData, reportedBy: e.target.value })}
                  />
                </div>

                <div>
                  <label className="block text-sm font-military-body text-military-white uppercase">Assigned To</label>
                  <input
                    type="text"
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={formData.assignedTo}
                    onChange={(e) => setFormData({ ...formData, assignedTo: e.target.value })}
                  />
                </div>
              </div>
            )}

            <div>
              <label className="block text-sm font-military-body text-military-white uppercase">Tags (comma-separated)</label>
              <input
                type="text"
                className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                value={formData.tags}
                onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
                placeholder="e.g., urgent, follow-up, investigation"
              />
            </div>
          </div>

          {/* Response-specific fields */}
          {isResponse && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-military-body text-military-white uppercase">Commander</label>
                  <input
                    type="text"
                    required
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={(formData as any).commander || ''}
                    onChange={(e) => setFormData({ ...formData, commander: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-military-body text-military-white uppercase">Units (comma-separated)</label>
                  <input
                    type="text"
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={(formData as any).units || ''}
                    onChange={(e) => setFormData({ ...formData, units: e.target.value })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-military-body text-military-white uppercase">Start Date</label>
                  <input
                    type="date"
                    required
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={(formData as any).startDate || ''}
                    onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-military-body text-military-white uppercase">End Date</label>
                  <input
                    type="date"
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={(formData as any).endDate || ''}
                    onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-military-body text-military-white uppercase">Related Incidents (comma-separated IDs)</label>
                <input
                  type="text"
                  className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                  style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                  value={(formData as any).relatedIncidents || ''}
                  onChange={(e) => setFormData({ ...formData, relatedIncidents: e.target.value })}
                  placeholder="e.g., INC-123456789, INC-987654321"
                />
              </div>
            </>
          )}

          <div className="mt-6 flex justify-end space-x-3">
            <Button variant="military" onClick={onClose} className="military-btn">
              CANCEL
            </Button>
            <Button type="submit" variant="military" className="military-btn bg-military-darkgreen">
              {isResponse
                ? (response ? 'UPDATE RESPONSE' : 'CREATE RESPONSE')
                : (incident ? 'UPDATE INCIDENT' : 'CREATE INCIDENT')
              }
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default IncidentForm;