import React, { useState, useCallback, useEffect, useMemo } from 'react';
import {
  X,
  Search,
  Plus,
  Download,
  Upload,
  Shield,
  Target,
  MapPin,
  Layers,
  Filter,
  Grid,
  List,
  Star,
  Copy,
  Trash2,
  Edit3,
  <PERSON>,
  EyeOff,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import Button from '@/components/ui/Button';
import {
  tacticalSymbols,
  responseSymbols,
  enhancedMilitarySymbols,
  allTacticalSymbols,
  SymbolConfig,
  getIncidentSymbol,
  getResponseSymbol
} from './TacticalSymbols';
import { IncidentType, ActionType } from '@/types/incident';

export interface MilitarySymbol extends SymbolConfig {
  id: string;
  name: string;
  category: string;
  subcategory?: string;
  natoCode?: string;
  isCustom: boolean;
  tags: string[];
  size?: number;
  opacity?: number;
  incidentType?: IncidentType;
  responseType?: ActionType;
}

export interface SymbolCategory {
  id: string;
  name: string;
  description: string;
  symbols: MilitarySymbol[];
  expanded: boolean;
}

export interface SymbolManagerProps {
  isOpen: boolean;
  onClose: () => void;
  onSymbolSelect?: (symbol: MilitarySymbol) => void;
  onPlaceOnMap?: (symbol: MilitarySymbol, coordinates?: [number, number]) => void;
  className?: string;
}

// Convert existing tactical symbols to MilitarySymbol format
const convertTacticalSymbolsToMilitary = (): SymbolCategory[] => {
  const categories: SymbolCategory[] = [];

  // Incident symbols category
  const incidentSymbols: MilitarySymbol[] = Object.entries(tacticalSymbols).map(([key, config]) => ({
    id: `incident-${key}`,
    name: config.description,
    category: 'incidents',
    subcategory: 'tactical',
    isCustom: false,
    tags: ['incident', 'tactical', key.replace('_', ' ')],
    size: 32,
    opacity: 0.9,
    incidentType: key as IncidentType,
    ...config
  }));

  categories.push({
    id: 'incidents',
    name: 'Incident Types',
    description: 'Tactical incident symbols for operational planning',
    expanded: true,
    symbols: incidentSymbols
  });

  // Response symbols category
  const responseSymbols_: MilitarySymbol[] = Object.entries(responseSymbols).map(([key, config]) => ({
    id: `response-${key}`,
    name: config.description,
    category: 'responses',
    subcategory: 'operational',
    isCustom: false,
    tags: ['response', 'operational', key.replace('_', ' ')],
    size: 28,
    opacity: 0.9,
    responseType: key as ActionType,
    ...config
  }));

  categories.push({
    id: 'responses',
    name: 'Response Actions',
    description: 'Response and action symbols for operational coordination',
    expanded: false,
    symbols: responseSymbols_
  });

  // Enhanced military symbols category
  const militarySymbols: MilitarySymbol[] = Object.entries(enhancedMilitarySymbols).map(([key, config]) => ({
    id: `military-${key}`,
    name: config.description,
    category: 'military',
    subcategory: 'strategic',
    isCustom: false,
    tags: ['military', 'strategic', key.replace('_', ' ')],
    size: 30,
    opacity: 0.9,
    ...config
  }));

  categories.push({
    id: 'military',
    name: 'Military Assets',
    description: 'Command, control, logistics, and support facilities',
    expanded: false,
    symbols: militarySymbols
  });

  return categories;
};

// Initialize symbol categories from existing tactical symbols
const SYMBOL_CATEGORIES: SymbolCategory[] = convertTacticalSymbolsToMilitary();

const SymbolManager: React.FC<SymbolManagerProps> = ({
  isOpen,
  onClose,
  onSymbolSelect,
  onPlaceOnMap,
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [categories, setCategories] = useState<SymbolCategory[]>(SYMBOL_CATEGORIES);
  const [selectedSymbol, setSelectedSymbol] = useState<MilitarySymbol | null>(null);
  const [customSymbols, setCustomSymbols] = useState<MilitarySymbol[]>([]);

  // Filter symbols based on search and category
  const filteredSymbols = useMemo(() => {
    let allSymbols: MilitarySymbol[] = [];

    // Collect all symbols from categories
    categories.forEach(category => {
      allSymbols = [...allSymbols, ...category.symbols];
    });

    // Add custom symbols
    allSymbols = [...allSymbols, ...customSymbols];

    // Apply filters
    return allSymbols.filter(symbol => {
      const matchesSearch = searchTerm === '' ||
        symbol.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        symbol.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        symbol.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesCategory = selectedCategory === 'all' || symbol.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  }, [categories, customSymbols, searchTerm, selectedCategory]);

  // Toggle category expansion
  const toggleCategory = useCallback((categoryId: string) => {
    setCategories(prev => prev.map(cat =>
      cat.id === categoryId ? { ...cat, expanded: !cat.expanded } : cat
    ));
  }, []);

  // Handle symbol selection
  const handleSymbolSelect = useCallback((symbol: MilitarySymbol) => {
    setSelectedSymbol(symbol);
    onSymbolSelect?.(symbol);
  }, [onSymbolSelect]);

  // Handle placing symbol on map
  const handlePlaceOnMap = useCallback((symbol: MilitarySymbol) => {
    onPlaceOnMap?.(symbol);
    onClose();
  }, [onPlaceOnMap, onClose]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[60] flex items-center justify-center bg-black bg-opacity-60 backdrop-blur-md">
      <div className="bg-gray-900 bg-opacity-95 border border-gray-600 rounded-lg shadow-2xl w-full max-w-6xl mx-4 max-h-[90vh] overflow-hidden backdrop-blur-sm">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-600 bg-gray-800 bg-opacity-80">
          <div className="flex items-center space-x-3">
            <Shield size={20} className="text-blue-400" />
            <h2 className="text-lg font-mono font-bold text-white uppercase tracking-wider">Symbol Manager</h2>
            <span className="text-xs text-gray-400 font-mono">NATO MIL-STD-2525</span>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              className="text-gray-400 hover:text-white p-2 h-8 w-8"
              title={`Switch to ${viewMode === 'grid' ? 'List' : 'Grid'} View`}
            >
              {viewMode === 'grid' ? <List size={14} /> : <Grid size={14} />}
            </Button>
            <div className="border-l border-gray-600 h-6 mx-1"></div>
            <Button
              size="sm"
              variant="ghost"
              onClick={onClose}
              className="text-gray-400 hover:text-red-400 p-2 h-8 w-8"
              title="Close Symbol Manager"
            >
              <X size={14} />
            </Button>
          </div>
        </div>

        <div className="flex h-[calc(90vh-8rem)]">
          {/* Sidebar - Categories and Filters */}
          <div className="w-80 border-r border-gray-600 bg-gray-850 bg-opacity-50 overflow-y-auto">
            <div className="p-4">
              {/* Search */}
              <div className="relative mb-4">
                <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search symbols..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-600 rounded text-white text-sm focus:border-blue-500 focus:outline-none"
                />
              </div>

              {/* Category Filter */}
              <div className="mb-4">
                <label className="block text-xs font-mono text-gray-400 uppercase tracking-wider mb-2">
                  Category Filter
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none"
                >
                  <option value="all">All Categories</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Categories List */}
              <div className="space-y-2">
                <h3 className="text-xs font-mono text-gray-400 uppercase tracking-wider mb-2 border-b border-gray-700 pb-1">
                  Symbol Categories
                </h3>
                {categories.map(category => (
                  <div key={category.id} className="border border-gray-700 rounded">
                    <button
                      onClick={() => toggleCategory(category.id)}
                      className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-700 transition-colors"
                    >
                      <div>
                        <div className="font-medium text-white text-sm font-mono">{category.name}</div>
                        <div className="text-xs text-gray-400">{category.symbols.length} symbols</div>
                      </div>
                      {category.expanded ? <ChevronDown size={14} /> : <ChevronRight size={14} />}
                    </button>

                    {category.expanded && (
                      <div className="border-t border-gray-700 p-2 bg-gray-800 bg-opacity-50">
                        <div className="text-xs text-gray-400 mb-2">{category.description}</div>
                        <div className="grid grid-cols-4 gap-1">
                          {category.symbols.map(symbol => (
                            <button
                              key={symbol.id}
                              onClick={() => handleSymbolSelect(symbol)}
                              className={`p-2 rounded border text-lg hover:bg-gray-600 transition-colors ${
                                selectedSymbol?.id === symbol.id
                                  ? 'border-blue-500 bg-blue-900'
                                  : 'border-gray-600'
                              }`}
                              style={{ color: symbol.color }}
                              title={symbol.name}
                            >
                              {symbol.symbol}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="flex-1 flex flex-col">
            {/* Symbols Grid/List */}
            <div className="flex-1 p-4 overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-mono font-bold text-gray-300 uppercase tracking-wider">
                  Available Symbols ({filteredSymbols.length})
                </h3>
                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    onClick={() => {/* TODO: Add custom symbol creation */}}
                    className="flex items-center space-x-1"
                  >
                    <Plus size={14} />
                    <span>Create Custom</span>
                  </Button>
                </div>
              </div>

              {/* Symbols Display */}
              {viewMode === 'grid' ? (
                <div className="grid grid-cols-6 gap-3">
                  {filteredSymbols.map(symbol => (
                    <div
                      key={symbol.id}
                      className={`p-3 bg-gray-800 bg-opacity-60 rounded border cursor-pointer hover:bg-gray-700 hover:border-blue-500 transition-all duration-200 ${
                        selectedSymbol?.id === symbol.id ? 'border-blue-500 bg-blue-900' : 'border-gray-700'
                      }`}
                      onClick={() => handleSymbolSelect(symbol)}
                    >
                      <div className="text-center">
                        <div
                          className="text-2xl mb-2"
                          style={{ color: symbol.color }}
                        >
                          {symbol.symbol}
                        </div>
                        <div className="text-xs text-white font-mono truncate">{symbol.name}</div>
                        <div className="text-xs text-gray-400 mt-1">{symbol.category}</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredSymbols.map(symbol => (
                    <div
                      key={symbol.id}
                      className={`p-3 bg-gray-800 bg-opacity-60 rounded border cursor-pointer hover:bg-gray-700 hover:border-blue-500 transition-all duration-200 ${
                        selectedSymbol?.id === symbol.id ? 'border-blue-500 bg-blue-900' : 'border-gray-700'
                      }`}
                      onClick={() => handleSymbolSelect(symbol)}
                    >
                      <div className="flex items-center space-x-3">
                        <div
                          className="text-xl"
                          style={{ color: symbol.color }}
                        >
                          {symbol.symbol}
                        </div>
                        <div className="flex-1">
                          <div className="font-medium text-white text-sm font-mono">{symbol.name}</div>
                          <div className="text-xs text-gray-400">{symbol.description}</div>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className="text-xs text-blue-400 font-mono">{symbol.category}</span>
                            {symbol.natoCode && (
                              <span className="text-xs text-gray-500 font-mono">{symbol.natoCode}</span>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation();
                              handlePlaceOnMap(symbol);
                            }}
                            className="p-1 h-6 w-6 text-gray-400 hover:text-white"
                            title="Place on Map"
                          >
                            <MapPin size={12} />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {filteredSymbols.length === 0 && (
                <div className="text-center py-12 text-gray-500">
                  <Shield size={48} className="mx-auto mb-4 opacity-50" />
                  <p>No symbols found</p>
                  <p className="text-sm mt-1">Try adjusting your search or filter criteria</p>
                </div>
              )}
            </div>

            {/* Symbol Details Panel */}
            {selectedSymbol && (
              <div className="border-t border-gray-600 p-4 bg-gray-800 bg-opacity-50">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div
                      className="text-3xl"
                      style={{ color: selectedSymbol.color }}
                    >
                      {selectedSymbol.symbol}
                    </div>
                    <div>
                      <h4 className="font-medium text-white font-mono">{selectedSymbol.name}</h4>
                      <p className="text-sm text-gray-400 mt-1">{selectedSymbol.description}</p>
                      <div className="flex items-center space-x-4 mt-2 text-xs">
                        <span className="text-blue-400 font-mono">Category: {selectedSymbol.category}</span>
                        {selectedSymbol.natoCode && (
                          <span className="text-gray-500 font-mono">NATO: {selectedSymbol.natoCode}</span>
                        )}
                        <span className="text-gray-500 font-mono">Size: {selectedSymbol.size}px</span>
                      </div>
                      <div className="flex flex-wrap gap-1 mt-2">
                        {selectedSymbol.tags.map(tag => (
                          <span key={tag} className="px-2 py-1 bg-gray-700 rounded text-xs text-gray-300 font-mono">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      onClick={() => handlePlaceOnMap(selectedSymbol)}
                      className="flex items-center space-x-1"
                    >
                      <MapPin size={14} />
                      <span>Place on Map</span>
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SymbolManager;
