import { Response, ActionType, ResponseStatus } from '@/types/incident';

// Generate a random date within a range
const randomDate = (start: Date, end: Date): Date => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
};

// Generate a random location in Pakistan
const randomLocation = () => {
  // Approximate bounds for Pakistan
  const latMin = 23.5;
  const latMax = 37.0;
  const lngMin = 60.5;
  const lngMax = 77.0;

  return {
    latitude: latMin + Math.random() * (latMax - latMin),
    longitude: lngMin + Math.random() * (lngMax - lngMin)
  };
};

// List of sample locations in Pakistan
const pakistanLocations = [
  { name: 'Islamabad', region: 'Federal Territory' },
  { name: 'Karachi', region: 'Sindh' },
  { name: 'Lahore', region: 'Punjab' },
  { name: 'Peshawar', region: 'Khyber Pakhtunkhwa' },
  { name: 'Quetta', region: 'Balochistan' },
  { name: 'Rawalpindi', region: 'Punjab' },
  { name: 'Faisalabad', region: 'Punjab' },
  { name: 'Multan', region: 'Punjab' },
  { name: 'Hyderabad', region: 'Sindh' },
  { name: 'Gujranwala', region: 'Punjab' },
  { name: 'Sialkot', region: 'Punjab' },
  { name: 'Bahawalpur', region: 'Punjab' },
  { name: 'Sargodha', region: 'Punjab' },
  { name: 'Sukkur', region: 'Sindh' },
  { name: 'Larkana', region: 'Sindh' },
  { name: 'Sheikhupura', region: 'Punjab' },
  { name: 'Rahim Yar Khan', region: 'Punjab' },
  { name: 'Jhang', region: 'Punjab' },
  { name: 'Dera Ghazi Khan', region: 'Punjab' },
  { name: 'Gujrat', region: 'Punjab' },
  { name: 'Sahiwal', region: 'Punjab' },
  { name: 'Wah Cantonment', region: 'Punjab' },
  { name: 'Mardan', region: 'Khyber Pakhtunkhwa' },
  { name: 'Kasur', region: 'Punjab' },
  { name: 'Okara', region: 'Punjab' },
  { name: 'Mingora', region: 'Khyber Pakhtunkhwa' },
  { name: 'Nawabshah', region: 'Sindh' },
  { name: 'Chiniot', region: 'Punjab' },
  { name: 'Kotri', region: 'Sindh' },
  { name: 'Kāmoke', region: 'Punjab' },
  { name: 'Hafizabad', region: 'Punjab' },
  { name: 'Sadiqabad', region: 'Punjab' },
  { name: 'Mirpur Khas', region: 'Sindh' },
  { name: 'Burewala', region: 'Punjab' },
  { name: 'Kohat', region: 'Khyber Pakhtunkhwa' },
  { name: 'Khanewal', region: 'Punjab' },
  { name: 'Dera Ismail Khan', region: 'Khyber Pakhtunkhwa' },
  { name: 'Turbat', region: 'Balochistan' },
  { name: 'Muzaffargarh', region: 'Punjab' },
  { name: 'Abbottabad', region: 'Khyber Pakhtunkhwa' }
];

// Sample military units
const militaryUnits = [
  'Alpha Company, 1st Battalion',
  'Bravo Company, 2nd Battalion',
  'Charlie Company, 3rd Battalion',
  'Delta Company, 4th Battalion',
  'Echo Company, 5th Battalion',
  'Special Forces Group A',
  'Special Forces Group B',
  'Rapid Response Unit',
  'Counter-Terrorism Force',
  'Border Security Force',
  'Military Intelligence Unit',
  'Reconnaissance Battalion',
  'Air Support Squadron',
  'Logistics Support Group',
  'Engineering Corps',
  'Medical Support Team',
  'Communications Battalion',
  'Mountain Warfare Unit',
  'Desert Warfare Unit',
  'Urban Combat Team'
];

// Sample commanders
const commanders = [
  'Col. Ahmed Khan',
  'Maj. Zafar Ali',
  'Lt. Col. Imran Malik',
  'Brig. Sohail Akbar',
  'Maj. Gen. Asif Ghafoor',
  'Col. Rashid Mahmood',
  'Lt. Col. Faraz Ahmed',
  'Brig. Tariq Mehmood',
  'Maj. Kamran Shafiq',
  'Col. Naveed Hussain'
];

// Generate a random response
const generateResponse = (id: number): Response => {
  const location = randomLocation();
  const randomLocationIndex = Math.floor(Math.random() * pakistanLocations.length);
  const address = pakistanLocations[randomLocationIndex].name + ', ' + pakistanLocations[randomLocationIndex].region;

  // Get random action type (excluding NONE)
  const actionTypes = Object.values(ActionType).filter(type => type !== ActionType.NONE);
  const randomActionType = actionTypes[Math.floor(Math.random() * actionTypes.length)];

  // Get random status
  const statuses = Object.values(ResponseStatus);
  const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];

  // Generate start date (within last 6 months)
  const now = new Date();
  const sixMonthsAgo = new Date();
  sixMonthsAgo.setMonth(now.getMonth() - 6);
  const startDate = randomDate(sixMonthsAgo, now);

  // Generate end date (50% chance of having an end date if not PLANNED)
  let endDate;
  if (randomStatus !== ResponseStatus.PLANNED && Math.random() > 0.5) {
    const maxEndDate = new Date();
    const minEndDate = new Date(startDate);
    minEndDate.setDate(startDate.getDate() + 1);
    endDate = randomDate(minEndDate, maxEndDate);
  }

  // Random number of units (1-3)
  const numUnits = Math.floor(Math.random() * 3) + 1;
  const units: string[] = [];
  for (let i = 0; i < numUnits; i++) {
    const randomUnit = militaryUnits[Math.floor(Math.random() * militaryUnits.length)];
    if (!units.includes(randomUnit)) {
      units.push(randomUnit);
    }
  }

  // Random commander
  const commander = commanders[Math.floor(Math.random() * commanders.length)];

  // Generate title based on action type and location
  const title = `${randomActionType.replace('_', ' ')} - ${address}`;

  // Generate random equipment used
  const equipmentTypes = [
    'Armored Vehicles', 'Helicopters', 'Drones', 'Night Vision Equipment',
    'Tactical Radios', 'Surveillance Systems', 'Armored Personnel Carriers',
    'Transport Vehicles', 'Medical Equipment', 'Tactical Gear'
  ];
  const numEquipment = Math.floor(Math.random() * 3) + 1;
  const equipmentUsed: string[] = [];
  for (let i = 0; i < numEquipment; i++) {
    const randomEquipment = equipmentTypes[Math.floor(Math.random() * equipmentTypes.length)];
    if (!equipmentUsed.includes(randomEquipment)) {
      equipmentUsed.push(randomEquipment);
    }
  }

  // Generate random personnel count (20-200)
  const personnelCount = Math.floor(Math.random() * 180) + 20;

  // Generate random objectives
  const objectiveOptions = [
    'Secure the area',
    'Establish perimeter control',
    'Gather intelligence',
    'Neutralize threats',
    'Evacuate civilians',
    'Provide humanitarian aid',
    'Conduct reconnaissance',
    'Clear routes of IEDs',
    'Establish communication networks',
    'Support local security forces'
  ];
  const numObjectives = Math.floor(Math.random() * 3) + 1;
  const objectives: string[] = [];
  for (let i = 0; i < numObjectives; i++) {
    const randomObjective = objectiveOptions[Math.floor(Math.random() * objectiveOptions.length)];
    if (!objectives.includes(randomObjective)) {
      objectives.push(randomObjective);
    }
  }

  // Generate outcome and success rate for completed or in-progress responses
  let outcome = '';
  let successRate: number | undefined = undefined;
  let casualties = 0;
  let afterActionReport = '';

  if (randomStatus === ResponseStatus.COMPLETED || randomStatus === ResponseStatus.IN_PROGRESS) {
    const outcomeOptions = [
      'Operation successfully completed with all objectives achieved',
      'Partial success with most objectives achieved',
      'Operation ongoing with positive progress',
      'Operation faced significant challenges but achieved primary objective',
      'Operation completed with mixed results'
    ];
    outcome = outcomeOptions[Math.floor(Math.random() * outcomeOptions.length)];
    successRate = Math.floor(Math.random() * 60) + 40; // 40-100%
    casualties = Math.floor(Math.random() * 5); // 0-4 casualties

    if (randomStatus === ResponseStatus.COMPLETED) {
      afterActionReport = `After action report for ${title}. Operation conducted by ${units.join(', ')} under command of ${commander}. ${outcome}. Lessons learned include improved coordination and communication protocols.`;
    }
  }

  // Generate notes
  const notes = `Operational notes for ${randomActionType} in ${address}. Coordination required with local authorities.`;

  return {
    id: `RESP-${id}`,
    title,
    description: `Military response operation in ${address} area. ${numUnits} units deployed under command of ${commander}.`,
    type: randomActionType,
    status: randomStatus,
    location,
    address,
    startDate,
    endDate,
    commander,
    units,
    relatedIncidents: [],
    tags: ['military', randomActionType.toLowerCase(), randomStatus.toLowerCase()],
    notes,
    equipmentUsed,
    personnelCount,
    objectives,
    outcome: outcome || undefined,
    casualties,
    successRate,
    afterActionReport: afterActionReport || undefined
  };
};

// Generate 30 mock responses
export const mockResponses: Response[] = Array.from({ length: 30 }, (_, i) => generateResponse(i + 1));

export default mockResponses;
