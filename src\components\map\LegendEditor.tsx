import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  X,
  Plus,
  Trash2,
  Edit3,
  Save,
  Download,
  Upload,
  Eye,
  EyeOff,
  GripVertical,
  Copy,
  Palette,
  FileText,
  CheckCircle,
  AlertCircle,
  Move,
  Settings,
  Layers
} from 'lucide-react';
import Button from '@/components/ui/Button';

export interface LegendItem {
  id: string;
  label: string;
  color: string;
  symbol: string;
  visible: boolean;
  category: string;
  order: number;
  description?: string;
  opacity?: number;
}

export interface LegendTemplate {
  id: string;
  name: string;
  description: string;
  category: 'tactical' | 'operational' | 'strategic' | 'custom';
  items: Omit<LegendItem, 'id'>[];
}

export interface LegendEditorProps {
  isOpen: boolean;
  onClose: () => void;
  initialItems?: LegendItem[];
  onSave: (items: LegendItem[]) => void;
  onPreview?: (items: LegendItem[]) => void;
  className?: string;
}

// Enhanced tactical legend templates with NATO-compatible symbols
const LEGEND_TEMPLATES: LegendTemplate[] = [
  {
    id: 'tactical-operations',
    name: 'Tactical Operations',
    description: 'NATO-compatible tactical operation symbols',
    category: 'tactical',
    items: [
      { label: 'Friendly Forces', color: '#0066CC', symbol: '⬜', visible: true, category: 'units', order: 0, description: 'Blue forces - friendly units', opacity: 0.9 },
      { label: 'Hostile Forces', color: '#CC0000', symbol: '♦️', visible: true, category: 'units', order: 1, description: 'Red forces - enemy units', opacity: 0.9 },
      { label: 'Neutral Forces', color: '#00CC00', symbol: '⭕', visible: true, category: 'units', order: 2, description: 'Green forces - neutral/civilian', opacity: 0.8 },
      { label: 'Unknown Forces', color: '#FFCC00', symbol: '❓', visible: true, category: 'units', order: 3, description: 'Yellow forces - unknown/pending ID', opacity: 0.8 },
      { label: 'Objectives', color: '#FF6600', symbol: '🎯', visible: true, category: 'locations', order: 4, description: 'Mission objectives', opacity: 1.0 },
      { label: 'Checkpoints', color: '#9900CC', symbol: '🛑', visible: true, category: 'locations', order: 5, description: 'Security checkpoints', opacity: 0.9 }
    ]
  },
  {
    id: 'emergency-response',
    name: 'Emergency Response',
    description: 'Emergency response and crisis management',
    category: 'operational',
    items: [
      { label: 'Critical Incident', color: '#FF0000', symbol: '🚨', visible: true, category: 'incidents', order: 0, description: 'High priority emergency', opacity: 1.0 },
      { label: 'Medical Emergency', color: '#FF6B6B', symbol: '🏥', visible: true, category: 'incidents', order: 1, description: 'Medical assistance required', opacity: 0.9 },
      { label: 'Fire Emergency', color: '#FF4444', symbol: '🔥', visible: true, category: 'incidents', order: 2, description: 'Fire suppression needed', opacity: 0.9 },
      { label: 'Police Response', color: '#4169E1', symbol: '🚔', visible: true, category: 'response', order: 3, description: 'Law enforcement response', opacity: 0.9 },
      { label: 'Evacuation Zone', color: '#FFA500', symbol: '🚪', visible: true, category: 'response', order: 4, description: 'Evacuation assembly point', opacity: 0.8 }
    ]
  },
  {
    id: 'infrastructure',
    name: 'Critical Infrastructure',
    description: 'Command, control, and logistics facilities',
    category: 'strategic',
    items: [
      { label: 'Command Post', color: '#FFD700', symbol: '⌘', visible: true, category: 'facilities', order: 0, description: 'Primary command center', opacity: 1.0 },
      { label: 'Observation Post', color: '#00FF00', symbol: '👁️', visible: true, category: 'facilities', order: 1, description: 'Surveillance position', opacity: 0.9 },
      { label: 'Supply Depot', color: '#8B4513', symbol: '📦', visible: true, category: 'facilities', order: 2, description: 'Logistics supply point', opacity: 0.8 },
      { label: 'Communication Hub', color: '#6495ED', symbol: '📡', visible: true, category: 'facilities', order: 3, description: 'Radio relay station', opacity: 0.9 },
      { label: 'Medical Station', color: '#DC143C', symbol: '🏥', visible: true, category: 'facilities', order: 4, description: 'Field medical facility', opacity: 0.9 }
    ]
  },
  {
    id: 'route-planning',
    name: 'Route Planning',
    description: 'Movement and navigation symbols',
    category: 'tactical',
    items: [
      { label: 'Primary Route', color: '#0066FF', symbol: '➡️', visible: true, category: 'routes', order: 0, description: 'Main movement corridor', opacity: 0.9 },
      { label: 'Alternate Route', color: '#FF6600', symbol: '↗️', visible: true, category: 'routes', order: 1, description: 'Secondary movement option', opacity: 0.8 },
      { label: 'Danger Area', color: '#FF0000', symbol: '⚠️', visible: true, category: 'routes', order: 2, description: 'High-risk zone', opacity: 0.9 },
      { label: 'Safe Corridor', color: '#00AA00', symbol: '🛡️', visible: true, category: 'routes', order: 3, description: 'Secure passage', opacity: 0.8 }
    ]
  }
];

const SYMBOL_OPTIONS = [
  '🔴', '🟢', '🔵', '🟡', '🟠', '🟣', '⚫', '⚪',
  '🎯', '📍', '🚩', '⭐', '💎', '🔷', '🔶', '🔸',
  '🔥', '🚑', '🚔', '🚁', '✈️', '🚛', '⚡', '💧',
  '📡', '🏥', '🏭', '🏢', '🛡️', '⚠️', '☢️', '☣️'
];

const COLOR_PRESETS = [
  '#FF0000', '#00AA00', '#0066CC', '#FFFF00', '#FFA500', '#9900CC',
  '#FF4444', '#44FF44', '#4444FF', '#FFFF44', '#FF44FF', '#44FFFF',
  '#800000', '#008000', '#000080', '#808000', '#800080', '#008080',
  '#C0C0C0', '#808080', '#000000', '#FFFFFF', '#FFB6C1', '#98FB98'
];

const LegendEditor: React.FC<LegendEditorProps> = ({
  isOpen,
  onClose,
  initialItems = [],
  onSave,
  onPreview
}) => {
  const [items, setItems] = useState<LegendItem[]>(initialItems);
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  const [showTemplates, setShowTemplates] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Validate legend items
  const validateItems = useCallback((itemsToValidate: LegendItem[]) => {
    const errors: Record<string, string> = {};
    const labels = new Set<string>();
    const colors = new Set<string>();

    itemsToValidate.forEach(item => {
      // Check for duplicate labels
      if (labels.has(item.label.toLowerCase())) {
        errors[item.id] = 'Duplicate label';
      } else {
        labels.add(item.label.toLowerCase());
      }

      // Check for similar colors
      if (colors.has(item.color)) {
        errors[item.id] = errors[item.id] ? `${errors[item.id]}, Duplicate color` : 'Duplicate color';
      } else {
        colors.add(item.color);
      }

      // Check for empty labels
      if (!item.label.trim()) {
        errors[item.id] = errors[item.id] ? `${errors[item.id]}, Empty label` : 'Empty label';
      }
    });

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, []);

  // Add new legend item
  const addItem = useCallback(() => {
    const newItem: LegendItem = {
      id: `item-${Date.now()}`,
      label: 'New Item',
      color: COLOR_PRESETS[Math.floor(Math.random() * COLOR_PRESETS.length)],
      symbol: SYMBOL_OPTIONS[Math.floor(Math.random() * SYMBOL_OPTIONS.length)],
      visible: true,
      category: 'custom',
      order: items.length
    };

    const newItems = [...items, newItem];
    setItems(newItems);
    setEditingItem(newItem.id);
    onPreview?.(newItems);
  }, [items, onPreview]);

  // Remove legend item
  const removeItem = useCallback((itemId: string) => {
    const newItems = items.filter(item => item.id !== itemId);
    setItems(newItems);
    onPreview?.(newItems);

    if (editingItem === itemId) {
      setEditingItem(null);
    }
  }, [items, editingItem, onPreview]);

  // Update legend item
  const updateItem = useCallback((itemId: string, updates: Partial<LegendItem>) => {
    const newItems = items.map(item =>
      item.id === itemId ? { ...item, ...updates } : item
    );
    setItems(newItems);
    onPreview?.(newItems);
    validateItems(newItems);
  }, [items, onPreview, validateItems]);

  // Duplicate legend item
  const duplicateItem = useCallback((itemId: string) => {
    const itemToDuplicate = items.find(item => item.id === itemId);
    if (!itemToDuplicate) return;

    const newItem: LegendItem = {
      ...itemToDuplicate,
      id: `item-${Date.now()}`,
      label: `${itemToDuplicate.label} Copy`,
      order: items.length
    };

    const newItems = [...items, newItem];
    setItems(newItems);
    onPreview?.(newItems);
  }, [items, onPreview]);

  // Handle drag and drop reordering
  const handleDragStart = useCallback((itemId: string) => {
    setDraggedItem(itemId);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent, targetItemId: string) => {
    e.preventDefault();
    if (!draggedItem || draggedItem === targetItemId) return;

    const draggedIndex = items.findIndex(item => item.id === draggedItem);
    const targetIndex = items.findIndex(item => item.id === targetItemId);

    if (draggedIndex === -1 || targetIndex === -1) return;

    const newItems = [...items];
    const [draggedItemData] = newItems.splice(draggedIndex, 1);
    newItems.splice(targetIndex, 0, draggedItemData);

    // Update order values
    newItems.forEach((item, index) => {
      item.order = index;
    });

    setItems(newItems);
    onPreview?.(newItems);
  }, [draggedItem, items, onPreview]);

  const handleDragEnd = useCallback(() => {
    setDraggedItem(null);
  }, []);

  // Apply template
  const applyTemplate = useCallback((template: LegendTemplate) => {
    const templateItems: LegendItem[] = template.items.map((item, index) => ({
      ...item,
      id: `template-${template.id}-${index}`,
      order: index
    }));

    setItems(templateItems);
    setShowTemplates(false);
    onPreview?.(templateItems);
  }, [onPreview]);

  // Export legend configuration
  const exportLegend = useCallback(() => {
    const config = {
      version: '1.0',
      name: 'Custom Legend',
      created: new Date().toISOString(),
      items: items
    };

    const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `legend-config-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, [items]);

  // Import legend configuration
  const importLegend = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const config = JSON.parse(e.target?.result as string);
        if (config.items && Array.isArray(config.items)) {
          setItems(config.items);
          onPreview?.(config.items);
        }
      } catch (error) {
        console.error('Failed to import legend configuration:', error);
      }
    };
    reader.readAsText(file);

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [onPreview]);

  // Save and close - Define this before it's used in useEffect
  const handleSave = useCallback(() => {
    if (validateItems(items)) {
      onSave(items);
      onClose();
    }
  }, [items, validateItems, onSave, onClose]);

  // Real-time preview with debouncing
  useEffect(() => {
    const timer = setTimeout(() => {
      onPreview?.(items);
    }, 300);
    return () => clearTimeout(timer);
  }, [items, onPreview]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      if (e.key === 'Escape') {
        onClose();
      } else if (e.key === 's' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        handleSave();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose, handleSave]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[60] flex items-center justify-center bg-black bg-opacity-60 backdrop-blur-md">
      <div className="bg-gray-900 bg-opacity-95 border border-gray-600 rounded-lg shadow-2xl w-full max-w-5xl mx-4 max-h-[90vh] overflow-hidden backdrop-blur-sm">
        {/* Header - Tactical Style */}
        <div className="flex items-center justify-between p-4 border-b border-gray-600 bg-gray-800 bg-opacity-80">
          <div className="flex items-center space-x-3">
            <Layers size={20} className="text-blue-400" />
            <h2 className="text-lg font-mono font-bold text-white uppercase tracking-wider">Legend Editor</h2>
            <span className="text-xs text-gray-400 font-mono">({items.length} items)</span>
            {Object.keys(validationErrors).length > 0 && (
              <span className="text-xs text-red-400 font-mono flex items-center">
                <AlertCircle size={12} className="mr-1" />
                {Object.keys(validationErrors).length} errors
              </span>
            )}
          </div>
          <div className="flex items-center space-x-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowTemplates(!showTemplates)}
              className={`text-gray-400 hover:text-white p-2 h-8 w-8 ${showTemplates ? 'bg-blue-600 text-white' : ''}`}
              title="Toggle Templates"
            >
              <Palette size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => fileInputRef.current?.click()}
              className="text-gray-400 hover:text-white p-2 h-8 w-8"
              title="Import Configuration"
            >
              <Upload size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={exportLegend}
              className="text-gray-400 hover:text-white p-2 h-8 w-8"
              title="Export Configuration"
            >
              <Download size={14} />
            </Button>
            <div className="border-l border-gray-600 h-6 mx-1"></div>
            <Button
              size="sm"
              variant="ghost"
              onClick={onClose}
              className="text-gray-400 hover:text-red-400 p-2 h-8 w-8"
              title="Close Editor"
            >
              <X size={14} />
            </Button>
          </div>
        </div>

        <div className="flex h-[calc(90vh-8rem)]">
          {/* Templates Sidebar - Enhanced */}
          {showTemplates && (
            <div className="w-80 border-r border-gray-600 bg-gray-850 bg-opacity-50 overflow-y-auto">
              <div className="p-4">
                <h3 className="text-sm font-mono font-bold text-gray-300 mb-4 uppercase tracking-wider flex items-center">
                  <Settings size={14} className="mr-2" />
                  Legend Templates
                </h3>

                {/* Group templates by category */}
                {['tactical', 'operational', 'strategic'].map(category => {
                  const categoryTemplates = LEGEND_TEMPLATES.filter(t => t.category === category);
                  if (categoryTemplates.length === 0) return null;

                  return (
                    <div key={category} className="mb-4">
                      <h4 className="text-xs font-mono text-gray-400 uppercase tracking-wider mb-2 border-b border-gray-700 pb-1">
                        {category}
                      </h4>
                      <div className="space-y-2">
                        {categoryTemplates.map(template => (
                          <div
                            key={template.id}
                            className="p-3 bg-gray-800 bg-opacity-60 rounded border border-gray-700 cursor-pointer hover:bg-gray-700 hover:border-blue-500 transition-all duration-200"
                            onClick={() => applyTemplate(template)}
                          >
                            <div className="font-medium text-white text-sm font-mono">{template.name}</div>
                            <div className="text-xs text-gray-400 mt-1 leading-relaxed">{template.description}</div>
                            <div className="flex items-center justify-between mt-2">
                              <span className="text-xs text-gray-500 font-mono">{template.items.length} items</span>
                              <span className="text-xs text-blue-400 font-mono uppercase">{template.category}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Main Content */}
          <div className="flex-1 flex">
            {/* Legend Items List */}
            <div className="flex-1 p-4 overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-semibold text-gray-300">Legend Items</h3>
                <Button
                  size="sm"
                  onClick={addItem}
                  className="flex items-center space-x-1"
                >
                  <Plus size={14} />
                  <span>Add Item</span>
                </Button>
              </div>

              <div className="space-y-2">
                {items.map(item => (
                  <div
                    key={item.id}
                    draggable
                    onDragStart={() => handleDragStart(item.id)}
                    onDragOver={(e) => handleDragOver(e, item.id)}
                    onDragEnd={handleDragEnd}
                    className={`
                      p-3 bg-gray-800 rounded-lg border transition-all cursor-move
                      ${editingItem === item.id ? 'border-blue-500 bg-gray-750' : 'border-gray-700'}
                      ${validationErrors[item.id] ? 'border-red-500' : ''}
                      hover:bg-gray-750
                    `}
                  >
                    <div className="flex items-center space-x-3">
                      <GripVertical size={14} className="text-gray-500" />

                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => updateItem(item.id, { visible: !item.visible })}
                        className="p-1 h-6 w-6"
                      >
                        {item.visible ? <Eye size={12} /> : <EyeOff size={12} />}
                      </Button>

                      <div
                        className="w-6 h-6 rounded flex items-center justify-center text-sm cursor-pointer"
                        style={{ backgroundColor: item.color }}
                        onClick={() => setEditingItem(editingItem === item.id ? null : item.id)}
                      >
                        {item.symbol}
                      </div>

                      <div className="flex-1 min-w-0">
                        {editingItem === item.id ? (
                          <input
                            type="text"
                            value={item.label}
                            onChange={(e) => updateItem(item.id, { label: e.target.value })}
                            className="w-full bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-sm"
                            onBlur={() => setEditingItem(null)}
                            onKeyDown={(e) => e.key === 'Enter' && setEditingItem(null)}
                            autoFocus
                          />
                        ) : (
                          <div
                            className="text-sm text-white truncate cursor-pointer"
                            onClick={() => setEditingItem(item.id)}
                          >
                            {item.label}
                          </div>
                        )}
                        {validationErrors[item.id] && (
                          <div className="text-xs text-red-400 mt-1 flex items-center">
                            <AlertCircle size={10} className="mr-1" />
                            {validationErrors[item.id]}
                          </div>
                        )}
                      </div>

                      <div className="flex items-center space-x-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => duplicateItem(item.id)}
                          className="p-1 h-6 w-6 text-gray-400 hover:text-white"
                          title="Duplicate"
                        >
                          <Copy size={12} />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => setEditingItem(editingItem === item.id ? null : item.id)}
                          className="p-1 h-6 w-6 text-gray-400 hover:text-white"
                          title="Edit"
                        >
                          <Edit3 size={12} />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => removeItem(item.id)}
                          className="p-1 h-6 w-6 text-red-400 hover:text-red-300"
                          title="Delete"
                        >
                          <Trash2 size={12} />
                        </Button>
                      </div>
                    </div>

                    {/* Expanded editing panel */}
                    {editingItem === item.id && (
                      <div className="mt-3 pt-3 border-t border-gray-700 space-y-3">
                        {/* Color picker */}
                        <div>
                          <label className="block text-xs text-gray-400 mb-2">Color</label>
                          <div className="flex flex-wrap gap-1">
                            {COLOR_PRESETS.map(color => (
                              <button
                                key={color}
                                className={`w-6 h-6 rounded border-2 ${
                                  item.color === color ? 'border-white' : 'border-gray-600'
                                }`}
                                style={{ backgroundColor: color }}
                                onClick={() => updateItem(item.id, { color })}
                              />
                            ))}
                          </div>
                          <input
                            type="color"
                            value={item.color}
                            onChange={(e) => updateItem(item.id, { color: e.target.value })}
                            className="mt-2 w-full h-8 rounded border border-gray-600"
                          />
                        </div>

                        {/* Symbol picker */}
                        <div>
                          <label className="block text-xs text-gray-400 mb-2">Symbol</label>
                          <div className="flex flex-wrap gap-1">
                            {SYMBOL_OPTIONS.map(symbol => (
                              <button
                                key={symbol}
                                className={`w-8 h-8 rounded border text-sm ${
                                  item.symbol === symbol
                                    ? 'border-blue-500 bg-blue-900'
                                    : 'border-gray-600 bg-gray-700 hover:bg-gray-600'
                                }`}
                                onClick={() => updateItem(item.id, { symbol })}
                              >
                                {symbol}
                              </button>
                            ))}
                          </div>
                        </div>

                        {/* Category */}
                        <div>
                          <label className="block text-xs text-gray-400 mb-2">Category</label>
                          <select
                            value={item.category}
                            onChange={(e) => updateItem(item.id, { category: e.target.value })}
                            className="w-full bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-sm"
                          >
                            <option value="custom">Custom</option>
                            <option value="units">Units</option>
                            <option value="equipment">Equipment</option>
                            <option value="facilities">Facilities</option>
                            <option value="activities">Activities</option>
                            <option value="incidents">Incidents</option>
                            <option value="response">Response</option>
                            <option value="locations">Locations</option>
                            <option value="routes">Routes</option>
                            <option value="infrastructure">Infrastructure</option>
                          </select>
                        </div>
                      </div>
                    )}
                  </div>
                ))}

                {items.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <FileText size={48} className="mx-auto mb-4 opacity-50" />
                    <p>No legend items yet</p>
                    <p className="text-sm mt-1">Add items or use a template to get started</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-4 border-t border-gray-600 bg-gray-850">
          <div className="flex items-center space-x-2 text-sm text-gray-400">
            {Object.keys(validationErrors).length > 0 ? (
              <div className="flex items-center text-red-400">
                <AlertCircle size={14} className="mr-1" />
                {Object.keys(validationErrors).length} validation error(s)
              </div>
            ) : (
              <div className="flex items-center text-green-400">
                <CheckCircle size={14} className="mr-1" />
                All items valid
              </div>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={Object.keys(validationErrors).length > 0}
              className="flex items-center space-x-1"
            >
              <Save size={14} />
              <span>Save Legend</span>
            </Button>
          </div>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          accept=".json"
          onChange={importLegend}
          className="hidden"
        />
      </div>
    </div>
  );
};

export default LegendEditor;
