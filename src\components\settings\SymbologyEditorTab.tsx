import React, { useState, useEffect } from 'react';
import { Palette, Download, Upload, Save, RefreshCw, Check, AlertTriangle, Eye } from 'lucide-react';
import Button from '@/components/ui/Button';
import { IncidentType, ActionType } from '@/types/incident';
import { tacticalSymbols, responseSymbols } from '@/components/map/TacticalSymbols';

interface SymbolConfig {
  symbol: string;
  color: string;
  description: string;
  shape?: string;
  text?: string;
}

interface SymbologyState {
  incidents: Record<string, SymbolConfig>;
  responses: Record<string, SymbolConfig>;
}

const SymbologyEditorTab: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'incidents' | 'responses'>('incidents');
  const [symbology, setSymbology] = useState<SymbologyState>({
    incidents: { ...tacticalSymbols },
    responses: { ...responseSymbols }
  });
  const [selectedSymbol, setSelectedSymbol] = useState<string | null>(null);
  const [editedSymbol, setEditedSymbol] = useState<SymbolConfig | null>(null);
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);

  // Load symbology from localStorage on component mount
  useEffect(() => {
    const savedSymbology = localStorage.getItem('symbology');
    if (savedSymbology) {
      try {
        const parsed = JSON.parse(savedSymbology);
        setSymbology(parsed);
      } catch (error) {
        console.error('Failed to parse saved symbology:', error);
      }
    }
  }, []);

  const handleSymbolSelect = (key: string) => {
    setSelectedSymbol(key);
    const symbolData = activeTab === 'incidents'
      ? symbology.incidents[key]
      : symbology.responses[key];

    setEditedSymbol({ ...symbolData });
  };

  const handleColorChange = (color: string) => {
    if (editedSymbol) {
      setEditedSymbol({ ...editedSymbol, color });
    }
  };

  const handleDescriptionChange = (description: string) => {
    if (editedSymbol) {
      setEditedSymbol({ ...editedSymbol, description });
    }
  };

  const handleShapeChange = (shape: string) => {
    if (editedSymbol) {
      setEditedSymbol({ ...editedSymbol, shape });
    }
  };

  const handleTextChange = (text: string) => {
    if (editedSymbol) {
      setEditedSymbol({ ...editedSymbol, text });
    }
  };

  const handleSaveSymbol = () => {
    if (!selectedSymbol || !editedSymbol) return;

    const newSymbology = { ...symbology };
    if (activeTab === 'incidents') {
      newSymbology.incidents = { ...newSymbology.incidents, [selectedSymbol]: editedSymbol };
    } else {
      newSymbology.responses = { ...newSymbology.responses, [selectedSymbol]: editedSymbol };
    }

    setSymbology(newSymbology);
    saveSymbologyToLocalStorage(newSymbology);

    setResult({
      success: true,
      message: `Symbol ${editedSymbol.description} updated successfully.`
    });

    // Clear result message after 3 seconds
    setTimeout(() => setResult(null), 3000);
  };

  const saveSymbologyToLocalStorage = (data: SymbologyState) => {
    try {
      localStorage.setItem('symbology', JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save symbology to localStorage:', error);
    }
  };

  const handleExportSymbology = () => {
    try {
      const dataStr = JSON.stringify(symbology, null, 2);
      const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;

      const exportFileDefaultName = `symbology_export_${new Date().toISOString().slice(0, 10)}.json`;

      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();

      setResult({
        success: true,
        message: 'Symbology exported successfully.'
      });

      // Clear result message after 3 seconds
      setTimeout(() => setResult(null), 3000);
    } catch (error) {
      console.error('Failed to export symbology:', error);
      setResult({
        success: false,
        message: `Failed to export symbology: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  };

  const handleImportSymbology = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const importedSymbology = JSON.parse(content);

        // Validate the imported data
        if (!importedSymbology.incidents || !importedSymbology.responses) {
          throw new Error('Invalid symbology file format');
        }

        setSymbology(importedSymbology);
        saveSymbologyToLocalStorage(importedSymbology);

        setResult({
          success: true,
          message: 'Symbology imported successfully.'
        });
      } catch (error) {
        console.error('Failed to import symbology:', error);
        setResult({
          success: false,
          message: `Failed to import symbology: ${error instanceof Error ? error.message : String(error)}`
        });
      }

      // Reset file input
      event.target.value = '';
    };

    reader.onerror = () => {
      setResult({
        success: false,
        message: 'Failed to read the file'
      });
      // Reset file input
      event.target.value = '';
    };

    reader.readAsText(file);
  };

  const handleResetSymbology = () => {
    const defaultSymbology = {
      incidents: { ...tacticalSymbols },
      responses: { ...responseSymbols }
    };

    setSymbology(defaultSymbology);
    saveSymbologyToLocalStorage(defaultSymbology);
    setSelectedSymbol(null);
    setEditedSymbol(null);

    setResult({
      success: true,
      message: 'Symbology reset to defaults.'
    });

    // Clear result message after 3 seconds
    setTimeout(() => setResult(null), 3000);
  };

  const renderSymbolPreview = (symbolType: string, config: SymbolConfig, isSelected: boolean) => {
    // Create a simple SVG preview based on the symbol type and color
    let svgPath = '';
    let shape = config.shape || 'circle'; // Default to circle if no shape specified

    // Generate SVG path based on shape
    switch (shape) {
      case 'diamond':
        svgPath = '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.8" />';
        break;
      case 'square':
        svgPath = '<rect x="4" y="4" width="24" height="24" fill-opacity="0.8" />';
        break;
      case 'triangle':
        svgPath = '<polygon points="16,2 30,30 2,30" fill-opacity="0.8" />';
        break;
      case 'hexagon':
        svgPath = '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.8" />';
        break;
      case 'star':
        svgPath = '<polygon points="16,2 19,12 30,12 21,18 25,28 16,22 7,28 11,18 2,12 13,12" fill-opacity="0.8" />';
        break;
      case 'circle':
      default:
        svgPath = '<circle cx="16" cy="16" r="14" fill-opacity="0.8" />';
        break;
    }

    // Add text if specified
    if (config.text) {
      svgPath += `<text x="16" y="20" font-size="10" text-anchor="middle" fill="#fff">${config.text}</text>`;
    }

    const svgIcon = `
      <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
        <g fill="${config.color}" stroke="#000" stroke-width="1">
          ${svgPath}
        </g>
      </svg>
    `;

    const svgBase64 = btoa(svgIcon);
    const dataUrl = `data:image/svg+xml;base64,${svgBase64}`;

    return (
      <div
        className={`p-2 border ${isSelected ? 'border-military-accent' : 'border-military-border'} cursor-pointer hover:bg-military-navy`}
        onClick={() => handleSymbolSelect(symbolType)}
      >
        <div className="flex items-center">
          <img src={dataUrl} alt={config.description} width="32" height="32" />
          <div className="ml-2">
            <div className="text-sm text-military-white">{config.description}</div>
            <div className="text-xs text-military-white opacity-70">{symbolType}</div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div>
      <h3 className="text-lg font-military-heading text-military-white uppercase mb-6">SYMBOLOGY EDITOR</h3>
      <p className="text-military-white font-military-body mb-4">
        Customize the symbols used on the map for incidents and responses.
      </p>

      {/* Tab selector */}
      <div className="flex mb-4">
        <Button
          variant="military"
          className={`military-btn ${activeTab === 'incidents' ? 'bg-military-darkgreen' : 'bg-military-navy'}`}
          onClick={() => setActiveTab('incidents')}
        >
          INCIDENT SYMBOLS
        </Button>
        <Button
          variant="military"
          className={`military-btn ml-2 ${activeTab === 'responses' ? 'bg-military-darkgreen' : 'bg-military-navy'}`}
          onClick={() => setActiveTab('responses')}
        >
          RESPONSE SYMBOLS
        </Button>
      </div>

      {/* Import/Export/Reset buttons */}
      <div className="mb-4 flex">
        <Button
          variant="military"
          className="military-btn bg-military-blue"
          leftIcon={<Download size={16} />}
          onClick={handleExportSymbology}
        >
          EXPORT SYMBOLOGY
        </Button>

        <label className="ml-2">
          <Button
            variant="military"
            className="military-btn bg-military-blue"
            leftIcon={<Upload size={16} />}
            onClick={() => document.getElementById('symbology-import')?.click()}
          >
            IMPORT SYMBOLOGY
          </Button>
          <input
            id="symbology-import"
            type="file"
            accept=".json"
            onChange={handleImportSymbology}
            className="hidden"
          />
        </label>

        <Button
          variant="military"
          className="military-btn bg-military-red ml-2"
          leftIcon={<RefreshCw size={16} />}
          onClick={handleResetSymbology}
        >
          RESET TO DEFAULTS
        </Button>
      </div>

      {/* Symbol list and editor */}
      <div className="flex border border-military-border">
        {/* Symbol list */}
        <div className="w-1/2 border-r border-military-border overflow-y-auto" style={{ maxHeight: '400px' }}>
          <div className="p-2 bg-military-navy border-b border-military-border">
            <h4 className="text-sm font-military-heading text-military-white uppercase">
              {activeTab === 'incidents' ? 'INCIDENT SYMBOLS' : 'RESPONSE SYMBOLS'}
            </h4>
          </div>
          <div className="divide-y divide-military-border">
            {Object.entries(activeTab === 'incidents' ? symbology.incidents : symbology.responses).map(([key, config]) => (
              renderSymbolPreview(key, config, selectedSymbol === key)
            ))}
          </div>
        </div>

        {/* Symbol editor */}
        <div className="w-1/2 p-4">
          {selectedSymbol && editedSymbol ? (
            <div>
              <h4 className="text-md font-military-heading text-military-white uppercase mb-4">EDIT SYMBOL</h4>

              <div className="mb-4">
                <label className="block text-sm font-military-body text-military-white uppercase mb-1">Symbol Type</label>
                <input
                  type="text"
                  value={selectedSymbol}
                  disabled
                  className="w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 opacity-50"
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-military-body text-military-white uppercase mb-1">Description</label>
                <input
                  type="text"
                  value={editedSymbol.description}
                  onChange={(e) => handleDescriptionChange(e.target.value)}
                  className="w-full bg-military-navy border border-military-border text-military-white font-military-body p-2"
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-military-body text-military-white uppercase mb-1">Shape</label>
                <select
                  value={editedSymbol.shape || 'circle'}
                  onChange={(e) => handleShapeChange(e.target.value)}
                  className="w-full bg-military-navy border border-military-border text-military-white font-military-body p-2"
                >
                  <option value="circle">Circle</option>
                  <option value="diamond">Diamond</option>
                  <option value="square">Square</option>
                  <option value="triangle">Triangle</option>
                  <option value="hexagon">Hexagon</option>
                  <option value="star">Star</option>
                </select>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-military-body text-military-white uppercase mb-1">Symbol Text</label>
                <input
                  type="text"
                  value={editedSymbol.text || ''}
                  onChange={(e) => handleTextChange(e.target.value)}
                  placeholder="Enter text to display inside symbol (e.g., ADO, IBO)"
                  className="w-full bg-military-navy border border-military-border text-military-white font-military-body p-2"
                />
                <p className="text-xs text-military-white opacity-70 mt-1">
                  Leave empty for no text. Keep text short (1-4 characters) for best results.
                </p>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-military-body text-military-white uppercase mb-1">Color (Hex)</label>
                <div className="flex items-center">
                  <input
                    type="text"
                    value={editedSymbol.color}
                    onChange={(e) => handleColorChange(e.target.value)}
                    className="flex-1 bg-military-navy border border-military-border text-military-white font-military-body p-2"
                  />
                  <input
                    type="color"
                    value={editedSymbol.color}
                    onChange={(e) => handleColorChange(e.target.value)}
                    className="ml-2 h-8 w-8 border border-military-border cursor-pointer"
                  />
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-military-body text-military-white uppercase mb-1">Preview</label>
                <div className="p-4 border border-military-border flex justify-center">
                  {renderSymbolPreview(selectedSymbol, editedSymbol, false)}
                </div>
              </div>

              <Button
                variant="military"
                className="military-btn bg-military-darkgreen"
                leftIcon={<Save size={16} />}
                onClick={handleSaveSymbol}
              >
                SAVE CHANGES
              </Button>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full">
              <Eye size={48} className="text-military-white opacity-30 mb-4" />
              <p className="text-military-white font-military-body text-center">
                Select a symbol from the list to edit its properties.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Result message */}
      {result && (
        <div className={`mt-4 p-3 border ${result.success ? 'border-military-darkgreen bg-military-darkgreen bg-opacity-20' : 'border-military-red bg-military-red bg-opacity-20'}`}>
          <p className="text-military-white font-military-body flex items-start">
            {result.success ? (
              <Check size={16} className="text-military-green mr-2 mt-1 flex-shrink-0" />
            ) : (
              <AlertTriangle size={16} className="text-military-amber mr-2 mt-1 flex-shrink-0" />
            )}
            {result.message}
          </p>
        </div>
      )}
    </div>
  );
};

export default SymbologyEditorTab;
