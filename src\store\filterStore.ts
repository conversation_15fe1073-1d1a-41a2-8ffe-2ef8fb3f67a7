import { create } from 'zustand';
import { Incident, IncidentType, IncidentSeverity, IncidentStatus, ActionType } from '@/types/incident';
import { useIncidentStore } from './incidentStore';

export interface SpatialFilter {
  active: boolean;
  bounds?: L.LatLngBounds;
  polygon?: GeoJSON.Polygon;
}

export interface DateRangeFilter {
  startDate?: Date;
  endDate?: Date;
}

export interface ChartFilter {
  source: 'type' | 'severity' | 'status' | 'timeline';
  value: string | number | Date;
  label: string;
}

export interface FilterState {
  // Filter states
  types: IncidentType[];
  severities: IncidentSeverity[];
  statuses: IncidentStatus[];
  actions: ActionType[];
  dateRange: DateRangeFilter;
  searchTerm: string;
  tags: string[];
  spatial: SpatialFilter;
  chartFilters: ChartFilter[];
  
  // Active state tracking
  activeFilters: string[];
  
  // Filter actions
  setTypeFilter: (types: IncidentType[]) => void;
  setSeverityFilter: (severities: IncidentSeverity[]) => void;
  setStatusFilter: (statuses: IncidentStatus[]) => void;
  setActionFilter: (actions: ActionType[]) => void;
  setDateRangeFilter: (dateRange: DateRangeFilter) => void;
  setSearchTermFilter: (searchTerm: string) => void;
  setTagsFilter: (tags: string[]) => void;
  setSpatialFilter: (spatial: SpatialFilter) => void;
  addChartFilter: (filter: ChartFilter) => void;
  removeChartFilter: (source: string, value: string | number | Date) => void;
  
  // Filter management
  removeFilter: (filterType: string, value?: string | number | Date) => void;
  resetAllFilters: () => void;
  
  // Apply filters to incidents
  applyFilters: (incidents: Incident[]) => Incident[];
}

export const useFilterStore = create<FilterState>((set, get) => ({
  // Initial filter states
  types: [],
  severities: [],
  statuses: [],
  actions: [],
  dateRange: {},
  searchTerm: '',
  tags: [],
  spatial: { active: false },
  chartFilters: [],
  activeFilters: [],
  
  // Filter setters
  setTypeFilter: (types) => {
    set((state) => {
      const newActiveFilters = [...state.activeFilters];
      if (types.length > 0 && !newActiveFilters.includes('type')) {
        newActiveFilters.push('type');
      } else if (types.length === 0) {
        const index = newActiveFilters.indexOf('type');
        if (index !== -1) newActiveFilters.splice(index, 1);
      }
      
      return { 
        types, 
        activeFilters: newActiveFilters 
      };
    });
    
    // Apply filters to incident store
    const { setFilter } = useIncidentStore.getState();
    setFilter({ types: types.length > 0 ? types : undefined });
  },
  
  setSeverityFilter: (severities) => {
    set((state) => {
      const newActiveFilters = [...state.activeFilters];
      if (severities.length > 0 && !newActiveFilters.includes('severity')) {
        newActiveFilters.push('severity');
      } else if (severities.length === 0) {
        const index = newActiveFilters.indexOf('severity');
        if (index !== -1) newActiveFilters.splice(index, 1);
      }
      
      return { 
        severities, 
        activeFilters: newActiveFilters 
      };
    });
    
    // Apply filters to incident store
    const { setFilter } = useIncidentStore.getState();
    setFilter({ severities: severities.length > 0 ? severities : undefined });
  },
  
  setStatusFilter: (statuses) => {
    set((state) => {
      const newActiveFilters = [...state.activeFilters];
      if (statuses.length > 0 && !newActiveFilters.includes('status')) {
        newActiveFilters.push('status');
      } else if (statuses.length === 0) {
        const index = newActiveFilters.indexOf('status');
        if (index !== -1) newActiveFilters.splice(index, 1);
      }
      
      return { 
        statuses, 
        activeFilters: newActiveFilters 
      };
    });
    
    // Apply filters to incident store
    const { setFilter } = useIncidentStore.getState();
    setFilter({ statuses: statuses.length > 0 ? statuses : undefined });
  },
  
  setActionFilter: (actions) => {
    set((state) => {
      const newActiveFilters = [...state.activeFilters];
      if (actions.length > 0 && !newActiveFilters.includes('action')) {
        newActiveFilters.push('action');
      } else if (actions.length === 0) {
        const index = newActiveFilters.indexOf('action');
        if (index !== -1) newActiveFilters.splice(index, 1);
      }
      
      return { 
        actions, 
        activeFilters: newActiveFilters 
      };
    });
    
    // Apply filters to incident store
    const { setFilter } = useIncidentStore.getState();
    setFilter({ actions: actions.length > 0 ? actions : undefined });
  },
  
  setDateRangeFilter: (dateRange) => {
    set((state) => {
      const newActiveFilters = [...state.activeFilters];
      if ((dateRange.startDate || dateRange.endDate) && !newActiveFilters.includes('dateRange')) {
        newActiveFilters.push('dateRange');
      } else if (!dateRange.startDate && !dateRange.endDate) {
        const index = newActiveFilters.indexOf('dateRange');
        if (index !== -1) newActiveFilters.splice(index, 1);
      }
      
      return { 
        dateRange, 
        activeFilters: newActiveFilters 
      };
    });
    
    // Apply filters to incident store
    const { setFilter } = useIncidentStore.getState();
    setFilter({ 
      startDate: dateRange.startDate, 
      endDate: dateRange.endDate 
    });
  },
  
  setSearchTermFilter: (searchTerm) => {
    set((state) => {
      const newActiveFilters = [...state.activeFilters];
      if (searchTerm && !newActiveFilters.includes('searchTerm')) {
        newActiveFilters.push('searchTerm');
      } else if (!searchTerm) {
        const index = newActiveFilters.indexOf('searchTerm');
        if (index !== -1) newActiveFilters.splice(index, 1);
      }
      
      return { 
        searchTerm, 
        activeFilters: newActiveFilters 
      };
    });
    
    // Apply filters to incident store
    const { setFilter } = useIncidentStore.getState();
    setFilter({ searchTerm: searchTerm || undefined });
  },
  
  setTagsFilter: (tags) => {
    set((state) => {
      const newActiveFilters = [...state.activeFilters];
      if (tags.length > 0 && !newActiveFilters.includes('tags')) {
        newActiveFilters.push('tags');
      } else if (tags.length === 0) {
        const index = newActiveFilters.indexOf('tags');
        if (index !== -1) newActiveFilters.splice(index, 1);
      }
      
      return { 
        tags, 
        activeFilters: newActiveFilters 
      };
    });
    
    // Apply filters to incident store
    const { setFilter } = useIncidentStore.getState();
    setFilter({ tags: tags.length > 0 ? tags : undefined });
  },
  
  setSpatialFilter: (spatial) => {
    set((state) => {
      const newActiveFilters = [...state.activeFilters];
      if (spatial.active && !newActiveFilters.includes('spatial')) {
        newActiveFilters.push('spatial');
      } else if (!spatial.active) {
        const index = newActiveFilters.indexOf('spatial');
        if (index !== -1) newActiveFilters.splice(index, 1);
      }
      
      return { 
        spatial, 
        activeFilters: newActiveFilters 
      };
    });
    
    // Spatial filtering is handled separately through the map component
  },
  
  addChartFilter: (filter) => {
    set((state) => {
      // Check if this filter already exists
      const existingIndex = state.chartFilters.findIndex(
        f => f.source === filter.source && f.value === filter.value
      );
      
      let newChartFilters = [...state.chartFilters];
      if (existingIndex === -1) {
        newChartFilters.push(filter);
      }
      
      const newActiveFilters = [...state.activeFilters];
      if (!newActiveFilters.includes('chart')) {
        newActiveFilters.push('chart');
      }
      
      return { 
        chartFilters: newChartFilters, 
        activeFilters: newActiveFilters 
      };
    });
    
    // Apply chart filters to the appropriate store filters
    const { types, severities, statuses, dateRange } = get();
    const { setFilter } = useIncidentStore.getState();
    
    if (filter.source === 'type' && typeof filter.value === 'string') {
      const newTypes = [...types, filter.value as IncidentType];
      set({ types: newTypes });
      setFilter({ types: newTypes });
    } else if (filter.source === 'severity' && typeof filter.value === 'string') {
      const newSeverities = [...severities, filter.value as IncidentSeverity];
      set({ severities: newSeverities });
      setFilter({ severities: newSeverities });
    } else if (filter.source === 'status' && typeof filter.value === 'string') {
      const newStatuses = [...statuses, filter.value as IncidentStatus];
      set({ statuses: newStatuses });
      setFilter({ statuses: newStatuses });
    } else if (filter.source === 'timeline' && filter.value instanceof Date) {
      // For timeline, we'll set a date range of ±1 day around the clicked point
      const date = filter.value as Date;
      const startDate = new Date(date);
      startDate.setDate(startDate.getDate() - 1);
      
      const endDate = new Date(date);
      endDate.setDate(endDate.getDate() + 1);
      
      const newDateRange = { startDate, endDate };
      set({ dateRange: newDateRange });
      setFilter({ startDate, endDate });
    }
  },
  
  removeChartFilter: (source, value) => {
    set((state) => {
      const newChartFilters = state.chartFilters.filter(
        f => !(f.source === source && f.value === value)
      );
      
      const newActiveFilters = [...state.activeFilters];
      if (newChartFilters.length === 0) {
        const index = newActiveFilters.indexOf('chart');
        if (index !== -1) newActiveFilters.splice(index, 1);
      }
      
      return { 
        chartFilters: newChartFilters, 
        activeFilters: newActiveFilters 
      };
    });
    
    // Remove this filter from the appropriate store filters
    const { types, severities, statuses, dateRange } = get();
    const { setFilter } = useIncidentStore.getState();
    
    if (source === 'type' && typeof value === 'string') {
      const newTypes = types.filter(t => t !== value);
      set({ types: newTypes });
      setFilter({ types: newTypes.length > 0 ? newTypes : undefined });
    } else if (source === 'severity' && typeof value === 'string') {
      const newSeverities = severities.filter(s => s !== value);
      set({ severities: newSeverities });
      setFilter({ severities: newSeverities.length > 0 ? newSeverities : undefined });
    } else if (source === 'status' && typeof value === 'string') {
      const newStatuses = statuses.filter(s => s !== value);
      set({ statuses: newStatuses });
      setFilter({ statuses: newStatuses.length > 0 ? newStatuses : undefined });
    } else if (source === 'timeline') {
      set({ dateRange: {} });
      setFilter({ startDate: undefined, endDate: undefined });
    }
  },
  
  removeFilter: (filterType, value) => {
    switch (filterType) {
      case 'type':
        if (value) {
          const newTypes = get().types.filter(t => t !== value);
          get().setTypeFilter(newTypes);
        } else {
          get().setTypeFilter([]);
        }
        break;
      case 'severity':
        if (value) {
          const newSeverities = get().severities.filter(s => s !== value);
          get().setSeverityFilter(newSeverities);
        } else {
          get().setSeverityFilter([]);
        }
        break;
      case 'status':
        if (value) {
          const newStatuses = get().statuses.filter(s => s !== value);
          get().setStatusFilter(newStatuses);
        } else {
          get().setStatusFilter([]);
        }
        break;
      case 'action':
        if (value) {
          const newActions = get().actions.filter(a => a !== value);
          get().setActionFilter(newActions);
        } else {
          get().setActionFilter([]);
        }
        break;
      case 'dateRange':
        get().setDateRangeFilter({});
        break;
      case 'searchTerm':
        get().setSearchTermFilter('');
        break;
      case 'tags':
        if (value) {
          const newTags = get().tags.filter(t => t !== value);
          get().setTagsFilter(newTags);
        } else {
          get().setTagsFilter([]);
        }
        break;
      case 'spatial':
        get().setSpatialFilter({ active: false });
        break;
      case 'chart':
        if (value) {
          // Find the chart filter and remove it
          const chartFilter = get().chartFilters.find(f => f.value === value);
          if (chartFilter) {
            get().removeChartFilter(chartFilter.source, chartFilter.value);
          }
        } else {
          // Remove all chart filters
          set({ chartFilters: [], activeFilters: get().activeFilters.filter(f => f !== 'chart') });
          // Reset any filters that might have been set by chart filters
          get().setTypeFilter([]);
          get().setSeverityFilter([]);
          get().setStatusFilter([]);
          get().setDateRangeFilter({});
        }
        break;
      default:
        break;
    }
  },
  
  resetAllFilters: () => {
    set({
      types: [],
      severities: [],
      statuses: [],
      actions: [],
      dateRange: {},
      searchTerm: '',
      tags: [],
      spatial: { active: false },
      chartFilters: [],
      activeFilters: []
    });
    
    // Reset filters in incident store
    const { resetFilter } = useIncidentStore.getState();
    resetFilter();
  },
  
  // Apply all filters to a set of incidents
  applyFilters: (incidents) => {
    const { 
      types, severities, statuses, actions, 
      dateRange, searchTerm, tags, spatial 
    } = get();
    
    return incidents.filter(incident => {
      // Type filter
      if (types.length > 0 && !types.includes(incident.type)) {
        return false;
      }
      
      // Severity filter
      if (severities.length > 0 && !severities.includes(incident.severity)) {
        return false;
      }
      
      // Status filter
      if (statuses.length > 0 && !statuses.includes(incident.status)) {
        return false;
      }
      
      // Action filter
      if (actions.length > 0 && incident.action && !actions.includes(incident.action)) {
        return false;
      }
      
      // Date range filter
      if (dateRange.startDate && new Date(incident.reportedAt) < dateRange.startDate) {
        return false;
      }
      if (dateRange.endDate && new Date(incident.reportedAt) > dateRange.endDate) {
        return false;
      }
      
      // Search term filter
      if (searchTerm) {
        const term = searchTerm.toLowerCase();
        const matchesSearch = 
          incident.title.toLowerCase().includes(term) || 
          incident.description.toLowerCase().includes(term) ||
          incident.address.toLowerCase().includes(term);
        
        if (!matchesSearch) {
          return false;
        }
      }
      
      // Tags filter
      if (tags.length > 0) {
        if (!incident.tags || !incident.tags.some(tag => tags.includes(tag))) {
          return false;
        }
      }
      
      // Spatial filter is handled separately by the map component
      
      return true;
    });
  }
}));
