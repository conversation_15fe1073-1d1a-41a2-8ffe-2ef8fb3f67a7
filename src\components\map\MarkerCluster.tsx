import React from 'react';
import { Incident } from '@/types/incident';

interface MarkerClusterProps {
  incidents: Incident[];
  onSelectIncident: (id: string) => void;
}

const MarkerCluster: React.FC<MarkerClusterProps> = ({ incidents, onSelectIncident }) => {
  // This is a placeholder component for Leaflet marker clustering
  // The actual clustering is handled by the leaflet.markercluster plugin
  return null;
};

export default MarkerCluster;
