import React, { useState } from 'react';
import { Layers, Home, Navigation, Square, Circle, Trash2 } from 'lucide-react';
import Button from '@/components/ui/Button';
import L from 'leaflet';

interface MilitaryToolbarProps {
  drawnItems?: L.FeatureGroup | null;
  onDrawCreated?: (layer: any) => void;
  onBaseLayerChange?: (layer: string) => void;
  activeBaseLayer?: string;
}

const MilitaryToolbar: React.FC<MilitaryToolbarProps> = ({
  drawnItems,
  onDrawCreated,
  onBaseLayerChange,
  activeBaseLayer = 'satellite'
}) => {
  const [isDrawing, setIsDrawing] = useState(false);

  const handleBaseLayerChange = (layer: string) => {
    if (onBaseLayerChange) {
      onBaseLayerChange(layer);
    }
  };

  const handleHomeClick = () => {
    // Dispatch custom event for home button
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('home-button-clicked'));
    }
  };

  return (
    <div className="absolute top-2 right-2 z-1000 bg-gray-900 bg-opacity-90 rounded p-2 space-y-2">
      <div className="text-white text-xs font-bold mb-2">TACTICAL TOOLS</div>
      
      {/* Base Layer Controls */}
      <div className="space-y-1">
        <Button
          size="sm"
          variant="ghost"
          className={`w-full text-left justify-start text-white ${activeBaseLayer === 'satellite' ? 'bg-blue-600' : ''}`}
          onClick={() => handleBaseLayerChange('satellite')}
        >
          <Layers size={14} className="mr-2" />
          Satellite
        </Button>
        <Button
          size="sm"
          variant="ghost"
          className={`w-full text-left justify-start text-white ${activeBaseLayer === 'terrain' ? 'bg-blue-600' : ''}`}
          onClick={() => handleBaseLayerChange('terrain')}
        >
          <Layers size={14} className="mr-2" />
          Terrain
        </Button>
      </div>

      {/* Navigation */}
      <Button
        size="sm"
        variant="ghost"
        className="w-full text-left justify-start text-white"
        onClick={handleHomeClick}
      >
        <Home size={14} className="mr-2" />
        Home
      </Button>

      {/* Drawing Tools */}
      <div className="space-y-1">
        <Button
          size="sm"
          variant="ghost"
          className="w-full text-left justify-start text-white"
          onClick={() => setIsDrawing(!isDrawing)}
        >
          <Square size={14} className="mr-2" />
          Rectangle
        </Button>
        <Button
          size="sm"
          variant="ghost"
          className="w-full text-left justify-start text-white"
          onClick={() => setIsDrawing(!isDrawing)}
        >
          <Circle size={14} className="mr-2" />
          Circle
        </Button>
        <Button
          size="sm"
          variant="ghost"
          className="w-full text-left justify-start text-white"
          onClick={() => {
            if (drawnItems) {
              drawnItems.clearLayers();
            }
          }}
        >
          <Trash2 size={14} className="mr-2" />
          Clear
        </Button>
      </div>
    </div>
  );
};

export default MilitaryToolbar;
