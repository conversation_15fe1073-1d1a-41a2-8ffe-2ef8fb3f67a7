import React, { useState } from 'react';
import { Database, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON>w } from 'lucide-react';
import Button from '@/components/ui/Button';
import { db } from '@/services/db/database';
import { useIncidentStore } from '@/store/incidentStore';
import { useResponseStore } from '@/store/responseStore';

interface DatabaseManagementModalProps {
  onClose: () => void;
}

const DatabaseManagementModal: React.FC<DatabaseManagementModalProps> = ({ onClose }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);
  const { loadIncidents } = useIncidentStore();
  const { loadResponses } = useResponseStore();

  // Function to clear all incidents and responses but keep the database structure
  const handleClearAll = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      // Clear all incidents and responses
      await db.incidents.clear();
      await db.responses.clear();

      // Reload the stores to reflect the changes
      await loadIncidents();
      await loadResponses();

      setResult({
        success: true,
        message: 'All incidents and responses have been deleted. You can now start fresh.'
      });
    } catch (error) {
      console.error('Failed to clear database:', error);
      setResult({
        success: false,
        message: `Failed to clear database: ${error instanceof Error ? error.message : String(error)}`
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to clear all data and keep the database empty (no mock data)
  const handleClearAndKeepEmpty = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      // Clear all incidents and responses
      await db.incidents.clear();
      await db.responses.clear();

      // Reload the stores to reflect the changes
      await loadIncidents();
      await loadResponses();

      setResult({
        success: true,
        message: 'All incidents and responses have been deleted. The database is now empty.'
      });
    } catch (error) {
      console.error('Failed to clear database:', error);
      setResult({
        success: false,
        message: `Failed to clear database: ${error instanceof Error ? error.message : String(error)}`
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to reset the database with mock data
  const handleResetWithMockData = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      // Use the built-in resetDatabase function
      const success = await db.resetDatabase();

      // Reload the stores to reflect the changes
      await loadIncidents();
      await loadResponses();

      if (success) {
        setResult({
          success: true,
          message: 'Database has been reset with mock data.'
        });
      } else {
        throw new Error('Database reset returned false');
      }
    } catch (error) {
      console.error('Failed to reset database:', error);
      setResult({
        success: false,
        message: `Failed to reset database: ${error instanceof Error ? error.message : String(error)}`
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-military-panel border border-military-border max-w-md w-full"
         style={{ clipPath: 'polygon(0 0, calc(100% - 12px) 0, 100% 12px, 100% 100%, 12px 100%, 0 calc(100% - 12px))' }}>
      <div className="flex items-center justify-between px-6 py-4 bg-military-navy border-b border-military-border">
        <h2 className="text-lg font-military-heading text-military-white uppercase tracking-wider flex items-center">
          <Database size={20} className="mr-2" />
          DATABASE MANAGEMENT
        </h2>
        <Button
          variant="military"
          size="sm"
          onClick={onClose}
          className="military-btn"
        >
          X
        </Button>
      </div>

      <div className="p-6 bg-military-black">
        <div className="mb-6">
          <h3 className="text-md font-military-heading text-military-white uppercase mb-2">CLEAR ALL DATA</h3>
          <p className="text-military-white font-military-body mb-4">
            This will delete all incidents and responses from the database. You can then start fresh with your own data.
          </p>
          <div className="flex space-x-2">
            <Button
              variant="military"
              className="military-btn bg-military-red"
              onClick={handleClearAndKeepEmpty}
              isLoading={isLoading}
              leftIcon={<Trash2 size={16} />}
              disabled={isLoading}
            >
              DELETE ALL DATA
            </Button>
          </div>
        </div>

        <div className="mb-6">
          <h3 className="text-md font-military-heading text-military-white uppercase mb-2">RESET WITH MOCK DATA</h3>
          <p className="text-military-white font-military-body mb-4">
            This will delete all current data and reload the default mock incidents and responses.
          </p>
          <Button
            variant="military"
            className="military-btn bg-military-darkgreen"
            onClick={handleResetWithMockData}
            isLoading={isLoading}
            leftIcon={<RefreshCw size={16} />}
            disabled={isLoading}
          >
            RESET WITH MOCK DATA
          </Button>
        </div>

        {result && (
          <div className={`mt-4 p-3 border ${result.success ? 'border-military-darkgreen bg-military-darkgreen bg-opacity-20' : 'border-military-red bg-military-red bg-opacity-20'}`}>
            <p className="text-military-white font-military-body flex items-start">
              {result.success ? (
                <span className="text-military-green mr-2">✓</span>
              ) : (
                <AlertTriangle size={16} className="text-military-amber mr-2 mt-1 flex-shrink-0" />
              )}
              {result.message}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DatabaseManagementModal;
