import React from 'react';
import { MapPin, Plus, Target, Navigation } from 'lucide-react';
import Button from '@/components/ui/Button';
import { LatLng } from 'leaflet';

interface MapContextMenuProps {
  position: { x: number; y: number };
  mapPosition: LatLng;
  onClose: () => void;
  onAddIncident?: (location: { latitude: number; longitude: number }) => void;
  onAddResponse?: (location: { latitude: number; longitude: number }) => void;
}

const MapContextMenu: React.FC<MapContextMenuProps> = ({
  position,
  mapPosition,
  onClose,
  onAddIncident,
  onAddResponse
}) => {
  const handleAddIncident = () => {
    if (onAddIncident) {
      onAddIncident({
        latitude: mapPosition.lat,
        longitude: mapPosition.lng
      });
    }
    onClose();
  };

  const handleAddResponse = () => {
    if (onAddResponse) {
      onAddResponse({
        latitude: mapPosition.lat,
        longitude: mapPosition.lng
      });
    }
    onClose();
  };

  const handleAddWaypoint = () => {
    console.log('Add waypoint at:', mapPosition);
    onClose();
  };

  const handleSetTarget = () => {
    console.log('Set target at:', mapPosition);
    onClose();
  };

  return (
    <div
      className="absolute z-[2000] bg-gray-900 bg-opacity-95 rounded shadow-lg border border-gray-700 min-w-[160px]"
      style={{ left: position.x, top: position.y }}
    >
      <div className="p-1 space-y-1">
        <Button
          size="sm"
          variant="ghost"
          className="w-full text-left justify-start text-white hover:bg-gray-700"
          onClick={handleAddIncident}
        >
          <Plus size={14} className="mr-2" />
          Add Incident
        </Button>
        
        <Button
          size="sm"
          variant="ghost"
          className="w-full text-left justify-start text-white hover:bg-gray-700"
          onClick={handleAddResponse}
        >
          <Plus size={14} className="mr-2" />
          Add Response
        </Button>
        
        <Button
          size="sm"
          variant="ghost"
          className="w-full text-left justify-start text-white hover:bg-gray-700"
          onClick={handleAddWaypoint}
        >
          <MapPin size={14} className="mr-2" />
          Add Waypoint
        </Button>
        
        <Button
          size="sm"
          variant="ghost"
          className="w-full text-left justify-start text-white hover:bg-gray-700"
          onClick={handleSetTarget}
        >
          <Target size={14} className="mr-2" />
          Set Target
        </Button>
      </div>
      
      <div className="border-t border-gray-700 p-2 text-xs text-gray-400 font-mono">
        {mapPosition.lat.toFixed(5)}, {mapPosition.lng.toFixed(5)}
      </div>
    </div>
  );
};

export default MapContextMenu;
