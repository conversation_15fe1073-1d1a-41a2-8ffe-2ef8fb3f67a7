import React, { useState, useEffect } from 'react';
import { X, MapPin, Calendar, Users, Link } from 'lucide-react';
import Button from '@/components/ui/Button';
import { useResponseStore } from '@/store/responseStore';
import { useIncidentStore } from '@/store/incidentStore';
import {
  Response,
  ActionType,
  ResponseStatus,
  Coordinates
} from '@/types/incident';

interface ResponseFormProps {
  response?: Response;
  onClose: () => void;
  mapCoordinates?: Coordinates | null;
}

const ResponseForm: React.FC<ResponseFormProps> = ({
  response,
  onClose,
  mapCoordinates
}) => {
  console.log('ResponseForm rendered', { response, mapCoordinates });
  const { addResponse, updateResponse } = useResponseStore();
  const { incidents } = useIncidentStore();

  // Initialize form data
  const [formData, setFormData] = useState({
    title: response?.title || '',
    description: response?.description || '',
    type: response?.type || ActionType.NONE,
    status: response?.status || ResponseStatus.PLANNED,
    location: response?.location || mapCoordinates || { latitude: 34.0151, longitude: 71.5249 },
    address: response?.address || '',
    commander: response?.commander || '',
    units: response?.units?.join(', ') || '',
    startDate: response?.startDate ? new Date(response.startDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
    startTime: response?.startDate ? new Date(response.startDate).toISOString().split('T')[1].substring(0, 5) : new Date().toISOString().split('T')[1].substring(0, 5),
    endDate: response?.endDate ? new Date(response.endDate).toISOString().split('T')[0] : '',
    endTime: response?.endDate ? new Date(response.endDate).toISOString().split('T')[1].substring(0, 5) : '',
    relatedIncidents: response?.relatedIncidents?.join(', ') || '',
    tags: response?.tags?.join(', ') || '',
    notes: response?.notes || '',
    equipmentUsed: response?.equipmentUsed?.join(', ') || '',
    personnelCount: response?.personnelCount?.toString() || '',
    objectives: response?.objectives?.join('\n') || '',
    outcome: response?.outcome || '',
    casualties: response?.casualties?.toString() || '0',
    successRate: response?.successRate?.toString() || '',
    afterActionReport: response?.afterActionReport || ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // Process form data
      const responseData: Partial<Response> = {
        title: formData.title,
        description: formData.description,
        type: formData.type as ActionType,
        status: formData.status as ResponseStatus,
        location: formData.location as Coordinates,
        address: formData.address,
        commander: formData.commander,
        units: formData.units.split(',').map(unit => unit.trim()).filter(Boolean),
        startDate: new Date(`${formData.startDate}T${formData.startTime || '00:00'}`),
        endDate: formData.endDate ? new Date(`${formData.endDate}T${formData.endTime || '00:00'}`) : undefined,
        relatedIncidents: formData.relatedIncidents.split(',').map(id => id.trim()).filter(Boolean),
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        notes: formData.notes,
        equipmentUsed: formData.equipmentUsed.split(',').map(item => item.trim()).filter(Boolean),
        personnelCount: formData.personnelCount ? parseInt(formData.personnelCount) : undefined,
        objectives: formData.objectives.split('\n').map(obj => obj.trim()).filter(Boolean),
        outcome: formData.outcome,
        casualties: formData.casualties ? parseInt(formData.casualties) : 0,
        successRate: formData.successRate ? parseInt(formData.successRate) : undefined,
        afterActionReport: formData.afterActionReport
      };

      if (response) {
        await updateResponse(response.id, responseData);
      } else {
        await addResponse(responseData as Omit<Response, 'id'>);
      }

      onClose();
    } catch (error) {
      console.error('Failed to save response:', error);
    }
  };

  return (
    <div className="fixed inset-0 bg-military-black bg-opacity-80 flex items-center justify-center z-[1000] p-4">
      <div className="bg-military-panel border border-military-border max-w-2xl w-full max-h-[90vh] flex flex-col"
           style={{ clipPath: 'polygon(0 0, calc(100% - 12px) 0, 100% 12px, 100% 100%, 12px 100%, 0 calc(100% - 12px))' }}>
        <div className="flex items-center justify-between px-6 py-4 bg-military-navy border-b border-military-border">
          <h2 className="text-lg font-military-heading text-military-white uppercase tracking-wider">
            {response ? 'EDIT TACTICAL RESPONSE' : 'NEW TACTICAL RESPONSE'}
          </h2>
          <Button
            variant="military"
            size="sm"
            onClick={onClose}
            className="military-btn"
          >
            <X size={18} />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="flex-grow overflow-y-auto p-6 bg-military-black">
          <div className="space-y-4">
            {/* Basic Information Section */}
            <div className="military-container p-3 mb-4">
              <h3 className="text-sm font-military-heading text-military-white uppercase mb-3">Basic Information</h3>

              <div>
                <label className="block text-sm font-military-body text-military-white uppercase">Title</label>
                <input
                  type="text"
                  required
                  className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                  style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                />
              </div>

              <div className="mt-3">
                <label className="block text-sm font-military-body text-military-white uppercase">Description</label>
                <textarea
                  required
                  rows={3}
                  className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                  style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                />
              </div>

              <div className="grid grid-cols-2 gap-4 mt-3">
                <div>
                  <label className="block text-sm font-military-body text-military-white uppercase">Type</label>
                  <select
                    required
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={formData.type}
                    onChange={(e) => setFormData({ ...formData, type: e.target.value as ActionType })}
                  >
                    {Object.values(ActionType).map(type => (
                      <option key={type} value={type}>
                        {type.replace('_', ' ')}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-military-body text-military-white uppercase">Status</label>
                  <select
                    required
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={formData.status}
                    onChange={(e) => setFormData({ ...formData, status: e.target.value as ResponseStatus })}
                  >
                    {Object.values(ResponseStatus).map(status => (
                      <option key={status} value={status}>{status.replace('_', ' ')}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Location Section */}
            <div className="military-container p-3 mb-4">
              <h3 className="text-sm font-military-heading text-military-white uppercase mb-3">Location</h3>

              <div>
                <label className="block text-sm font-military-body text-military-white uppercase">Address</label>
                <input
                  type="text"
                  required
                  className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                  style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                  value={formData.address}
                  onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                />
              </div>

              <div className="grid grid-cols-2 gap-4 mt-3">
                <div>
                  <label className="block text-sm font-military-body text-military-white uppercase">Latitude</label>
                  <input
                    type="number"
                    step="0.000001"
                    required
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={formData.location.latitude}
                    onChange={(e) => setFormData({
                      ...formData,
                      location: { ...formData.location, latitude: parseFloat(e.target.value) }
                    })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-military-body text-military-white uppercase">Longitude</label>
                  <input
                    type="number"
                    step="0.000001"
                    required
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={formData.location.longitude}
                    onChange={(e) => setFormData({
                      ...formData,
                      location: { ...formData.location, longitude: parseFloat(e.target.value) }
                    })}
                  />
                </div>
              </div>
            </div>

            {/* Timing Section */}
            <div className="military-container p-3 mb-4">
              <h3 className="text-sm font-military-heading text-military-white uppercase mb-3">Timing</h3>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-military-body text-military-white uppercase">Start Date</label>
                  <input
                    type="date"
                    required
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={formData.startDate}
                    onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-military-body text-military-white uppercase">Start Time</label>
                  <input
                    type="time"
                    required
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={formData.startTime}
                    onChange={(e) => setFormData({ ...formData, startTime: e.target.value })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mt-3">
                <div>
                  <label className="block text-sm font-military-body text-military-white uppercase">End Date</label>
                  <input
                    type="date"
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={formData.endDate}
                    onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-military-body text-military-white uppercase">End Time</label>
                  <input
                    type="time"
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={formData.endTime}
                    onChange={(e) => setFormData({ ...formData, endTime: e.target.value })}
                  />
                </div>
              </div>
            </div>

            {/* Personnel Section */}
            <div className="military-container p-3 mb-4">
              <h3 className="text-sm font-military-heading text-military-white uppercase mb-3">Personnel</h3>

              <div>
                <label className="block text-sm font-military-body text-military-white uppercase">Commander</label>
                <input
                  type="text"
                  required
                  className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                  style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                  value={formData.commander}
                  onChange={(e) => setFormData({ ...formData, commander: e.target.value })}
                />
              </div>

              <div className="mt-3">
                <label className="block text-sm font-military-body text-military-white uppercase">Units (comma-separated)</label>
                <input
                  type="text"
                  className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                  style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                  value={formData.units}
                  onChange={(e) => setFormData({ ...formData, units: e.target.value })}
                  placeholder="e.g. Alpha Company, 1st Battalion, Special Forces Group A"
                />
              </div>

              <div className="mt-3">
                <label className="block text-sm font-military-body text-military-white uppercase">Personnel Count</label>
                <input
                  type="number"
                  min="0"
                  className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                  style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                  value={formData.personnelCount}
                  onChange={(e) => setFormData({ ...formData, personnelCount: e.target.value })}
                />
              </div>
            </div>

            {/* Mission Details Section */}
            <div className="military-container p-3 mb-4">
              <h3 className="text-sm font-military-heading text-military-white uppercase mb-3">Mission Details</h3>

              <div>
                <label className="block text-sm font-military-body text-military-white uppercase">Objectives (one per line)</label>
                <textarea
                  rows={3}
                  className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                  style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                  value={formData.objectives}
                  onChange={(e) => setFormData({ ...formData, objectives: e.target.value })}
                  placeholder="Enter each objective on a new line"
                />
              </div>

              <div className="mt-3">
                <label className="block text-sm font-military-body text-military-white uppercase">Equipment Used (comma-separated)</label>
                <input
                  type="text"
                  className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                  style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                  value={formData.equipmentUsed}
                  onChange={(e) => setFormData({ ...formData, equipmentUsed: e.target.value })}
                  placeholder="e.g. Vehicles, Weapons, Communication devices"
                />
              </div>

              <div className="mt-3">
                <label className="block text-sm font-military-body text-military-white uppercase">Related Incidents (comma-separated IDs)</label>
                <input
                  type="text"
                  className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                  style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                  value={formData.relatedIncidents}
                  onChange={(e) => setFormData({ ...formData, relatedIncidents: e.target.value })}
                  placeholder="e.g. INC-1001, INC-1002"
                />
              </div>
            </div>

            {/* Results Section (only shown for completed or in-progress responses) */}
            {(formData.status === ResponseStatus.COMPLETED || formData.status === ResponseStatus.IN_PROGRESS) && (
              <div className="military-container p-3 mb-4">
                <h3 className="text-sm font-military-heading text-military-white uppercase mb-3">Results</h3>

                <div>
                  <label className="block text-sm font-military-body text-military-white uppercase">Outcome</label>
                  <textarea
                    rows={2}
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={formData.outcome}
                    onChange={(e) => setFormData({ ...formData, outcome: e.target.value })}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4 mt-3">
                  <div>
                    <label className="block text-sm font-military-body text-military-white uppercase">Casualties</label>
                    <input
                      type="number"
                      min="0"
                      className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                      style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                      value={formData.casualties}
                      onChange={(e) => setFormData({ ...formData, casualties: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-military-body text-military-white uppercase">Success Rate (%)</label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                      style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                      value={formData.successRate}
                      onChange={(e) => setFormData({ ...formData, successRate: e.target.value })}
                    />
                  </div>
                </div>

                <div className="mt-3">
                  <label className="block text-sm font-military-body text-military-white uppercase">After Action Report</label>
                  <textarea
                    rows={3}
                    className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                    style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                    value={formData.afterActionReport}
                    onChange={(e) => setFormData({ ...formData, afterActionReport: e.target.value })}
                  />
                </div>
              </div>
            )}

            {/* Additional Information */}
            <div className="military-container p-3 mb-4">
              <h3 className="text-sm font-military-heading text-military-white uppercase mb-3">Additional Information</h3>

              <div>
                <label className="block text-sm font-military-body text-military-white uppercase">Notes</label>
                <textarea
                  rows={2}
                  className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                  style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                />
              </div>

              <div className="mt-3">
                <label className="block text-sm font-military-body text-military-white uppercase">Tags (comma-separated)</label>
                <input
                  type="text"
                  className="mt-1 block w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 focus:outline-none focus:border-military-accent"
                  style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
                  value={formData.tags}
                  onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
                  placeholder="e.g. urgent, high-priority, classified"
                />
              </div>
            </div>

            <div className="mt-6 flex justify-end space-x-3">
              <Button variant="military" onClick={onClose} className="military-btn">
                CANCEL
              </Button>
              <Button type="submit" variant="military" className="military-btn bg-military-darkgreen">
                {response ? 'UPDATE RESPONSE' : 'CREATE RESPONSE'}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ResponseForm;
