import { Incident, IncidentType } from '@/types/incident';
import { getIncidentSymbol } from '@/components/map/TacticalSymbols';

// This would be replaced with actual offline MBTiles implementation
export const initializeOfflineMaps = async () => {
  console.log('Initializing offline maps...');
  // In a real implementation, this would load MBTiles files
  return { success: true };
};

// Function to create a GeoJSON feature collection from incidents
export const createGeoJsonFromIncidents = (incidents: Incident[]) => {
  return {
    type: 'FeatureCollection',
    features: incidents.map(incident => ({
      type: 'Feature',
      properties: {
        id: incident.id,
        title: incident.title,
        type: incident.type,
        action: incident.action,
        severity: incident.severity,
        status: incident.status,
        reportedAt: incident.reportedAt,
        color: getIncidentSymbol(incident.type).color
      },
      geometry: {
        type: 'Point',
        coordinates: [incident.location.longitude, incident.location.latitude]
      }
    }))
  };
};

// Function to create a heatmap data from incidents
export const createHeatmapData = (incidents: Incident[]) => {
  return incidents.map(incident => {
    // Weight based on severity
    let weight = 1;
    switch (incident.severity) {
      case 'LOW': weight = 1; break;
      case 'MEDIUM': weight = 2; break;
      case 'HIGH': weight = 3; break;
      case 'CRITICAL': weight = 4; break;
    }

    return {
      lat: incident.location.latitude,
      lng: incident.location.longitude,
      weight
    };
  });
};