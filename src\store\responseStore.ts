import { create } from 'zustand';
import { db } from '@/services/db/database';
import {
  Response,
  ResponseFilter,
  ResponseStatus,
  ActionType
} from '@/types/incident';

// Default filter
const defaultFilter: ResponseFilter = {
  types: undefined,
  statuses: undefined,
  startDate: undefined,
  endDate: undefined,
  searchTerm: '',
  tags: undefined
};

// Custom filter function type
export type CustomResponseFilterFn = (response: Response) => boolean;

// Extended filter interface with custom filter function
export interface ExtendedResponseFilter extends ResponseFilter {
  customFilter?: CustomResponseFilterFn;
}

interface ResponseState {
  responses: Response[];
  filteredResponses: Response[];
  selectedResponse: Response | null;
  filter: ExtendedResponseFilter;
  loading: boolean;
  showingForm: boolean;
  formLocation: { latitude: number; longitude: number } | null;
  loadResponses: () => Promise<void>;
  setFilter: (filter: Partial<ExtendedResponseFilter>) => void;
  resetFilter: () => void;
  selectResponse: (id: string | null) => Promise<void>;
  addResponse: (response: Omit<Response, 'id'>) => Promise<void>;
  updateResponse: (id: string, updates: Partial<Response>) => Promise<void>;
  deleteResponse: (id: string) => Promise<void>;
  cleanupInvalidResponses: () => Promise<number>;
  showResponseForm: (location?: { latitude: number; longitude: number }) => void;
  hideResponseForm: () => void;
}

// Helper function to validate if a date value is valid
const isValidDate = (dateValue: any): boolean => {
  if (!dateValue) return false;

  try {
    const date = new Date(dateValue);
    return !isNaN(date.getTime());
  } catch {
    return false;
  }
};

// Helper function to validate a response object
const isValidResponse = (response: Response): boolean => {
  // Check if required date fields are valid
  if (!isValidDate(response.startDate)) {
    console.warn(`Response ${response.id} has invalid startDate: ${response.startDate}`);
    return false;
  }

  // Check endDate if it exists
  if (response.endDate && !isValidDate(response.endDate)) {
    console.warn(`Response ${response.id} has invalid endDate: ${response.endDate}`);
    return false;
  }

  // Add other validation checks as needed
  if (!response.id || !response.title) {
    console.warn(`Response ${response.id} is missing required fields`);
    return false;
  }

  return true;
};

// Helper function to filter responses based on criteria
const getFilteredResponses = async (filter: ExtendedResponseFilter): Promise<Response[]> => {
  try {
    const responses = await db.responses.toArray();

    return responses.filter(response => {
      // Type filter
      if (filter.types && filter.types.length > 0 && !filter.types.includes(response.type)) {
        return false;
      }

      // Status filter
      if (filter.statuses && filter.statuses.length > 0 && !filter.statuses.includes(response.status)) {
        return false;
      }

      // Date range filter for start date
      if (filter.startDate && new Date(response.startDate) < filter.startDate) {
        return false;
      }

      // Date range filter for end date
      if (filter.endDate) {
        if (!response.endDate) return false;
        if (new Date(response.endDate) > filter.endDate) return false;
      }

      // Search term filter
      if (filter.searchTerm) {
        const term = filter.searchTerm.toLowerCase();
        const matchesSearch =
          response.title.toLowerCase().includes(term) ||
          response.description.toLowerCase().includes(term) ||
          response.address.toLowerCase().includes(term) ||
          response.commander.toLowerCase().includes(term);

        if (!matchesSearch) {
          return false;
        }
      }

      // Tags filter
      if (filter.tags && filter.tags.length > 0) {
        if (!response.tags || !response.tags.some(tag => filter.tags!.includes(tag))) {
          return false;
        }
      }

      // Custom filter function
      if (filter.customFilter && !filter.customFilter(response)) {
        return false;
      }

      return true;
    });
  } catch (error) {
    console.error('Error filtering responses:', error);
    return [];
  }
};

export const useResponseStore = create<ResponseState>((set, get) => ({
  responses: [],
  filteredResponses: [],
  selectedResponse: null,
  filter: defaultFilter,
  loading: false,
  showingForm: false,
  formLocation: null,

  loadResponses: async () => {
    set({ loading: true });
    try {
      // First, clean up any invalid responses
      const deletedCount = await get().cleanupInvalidResponses();
      if (deletedCount > 0) {
        console.log(`Cleaned up ${deletedCount} invalid response(s)`);
      }

      const responses = await db.responses.toArray();
      set({ responses });

      // Apply current filter
      const filter = get().filter;
      const filtered = await getFilteredResponses(filter);

      set({
        filteredResponses: filtered
      });
    } catch (error) {
      console.error('Failed to load responses:', error);
    } finally {
      set({ loading: false });
    }
  },

  setFilter: async (filterUpdate) => {
    set({ loading: true });
    try {
      const newFilter = { ...get().filter, ...filterUpdate };
      const filtered = await getFilteredResponses(newFilter);

      set({
        filter: newFilter,
        filteredResponses: filtered
      });
    } catch (error) {
      console.error('Failed to apply filter:', error);
    } finally {
      set({ loading: false });
    }
  },

  resetFilter: async () => {
    set({ loading: true });
    try {
      const responses = get().responses;

      set({
        filter: defaultFilter,
        filteredResponses: responses
      });
    } catch (error) {
      console.error('Failed to reset filter:', error);
    } finally {
      set({ loading: false });
    }
  },

  selectResponse: async (id) => {
    if (!id) {
      set({ selectedResponse: null });
      return;
    }

    try {
      const response = await db.responses.get(id);
      set({ selectedResponse: response || null });
    } catch (error) {
      console.error('Failed to select response:', error);
      set({ selectedResponse: null });
    }
  },

  addResponse: async (responseData) => {
    set({ loading: true });
    try {
      const id = `RESP-${Date.now()}`;
      const newResponse = { ...responseData, id };

      await db.responses.add(newResponse);
      await get().loadResponses();
    } catch (error) {
      console.error('Failed to add response:', error);
    } finally {
      set({ loading: false });
    }
  },

  updateResponse: async (id, updates) => {
    set({ loading: true });
    try {
      await db.responses.update(id, updates);
      await get().loadResponses();

      // If the updated response is the selected one, refresh it
      if (get().selectedResponse?.id === id) {
        get().selectResponse(id);
      }
    } catch (error) {
      console.error('Failed to update response:', error);
    } finally {
      set({ loading: false });
    }
  },

  deleteResponse: async (id) => {
    set({ loading: true });
    try {
      await db.responses.delete(id);
      await get().loadResponses();

      // If the deleted response was selected, clear selection
      if (get().selectedResponse?.id === id) {
        set({ selectedResponse: null });
      }
    } catch (error) {
      console.error('Failed to delete response:', error);
    } finally {
      set({ loading: false });
    }
  },

  cleanupInvalidResponses: async () => {
    try {
      const allResponses = await db.responses.toArray();
      const invalidResponses = allResponses.filter(response => !isValidResponse(response));

      if (invalidResponses.length === 0) {
        return 0;
      }

      console.log(`Found ${invalidResponses.length} invalid response(s), deleting...`);

      // Delete invalid responses
      for (const response of invalidResponses) {
        console.log(`Deleting invalid response: ${response.id} - ${response.title}`);
        await db.responses.delete(response.id);
      }

      return invalidResponses.length;
    } catch (error) {
      console.error('Failed to cleanup invalid responses:', error);
      return 0;
    }
  },

  // Show the response form with optional location
  showResponseForm: (location) => {
    console.log('ResponseStore: showResponseForm called');
    set({
      showingForm: true,
      formLocation: location || null
    });
    console.log('ResponseStore: showingForm set to true');
  },

  // Hide the response form
  hideResponseForm: () => {
    console.log('ResponseStore: hideResponseForm called');
    set({
      showingForm: false,
      formLocation: null
    });
    console.log('ResponseStore: showingForm set to false');
  }
}));

export default useResponseStore;
