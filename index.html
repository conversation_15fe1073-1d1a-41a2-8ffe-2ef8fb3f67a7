<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Tactical Operations Dashboard</title>

    <!-- Military-themed fonts from Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;500;700&family=Black+Ops+One&family=Orbitron:wght@400;500;700;900&family=Quantico:wght@400;700&display=swap" rel="stylesheet">

    <!-- Military font styling -->
    <style>
      /* Use Google Fonts as alternatives to avoid CORS issues */
      :root {
        --font-military-heading: 'Orbitron', 'Black Ops One', 'Impact', 'Arial Black', sans-serif;
        --font-military-body: 'Roboto Mono', 'Quantico', 'Courier New', monospace;
        --font-military-stencil: 'Black Ops One', 'Impact', sans-serif;
      }

      /* Apply custom font variables */
      .font-military-heading {
        font-family: var(--font-military-heading) !important;
      }

      .font-military-body {
        font-family: var(--font-military-body) !important;
      }

      .font-military-stencil {
        font-family: var(--font-military-stencil) !important;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>