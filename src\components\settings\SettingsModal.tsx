import React, { useState } from 'react';
import { X, Database, Settings as SettingsIcon, Download, Upload, Trash2, Refresh<PERSON>w, AlertTriangle, Check, FileSpreadsheet, FileCheck, Table, Palette } from 'lucide-react';
import Button from '@/components/ui/Button';
import { db } from '@/services/db/database';
import { useIncidentStore } from '@/store/incidentStore';
import { useResponseStore } from '@/store/responseStore';
import { Incident, Response } from '@/types/incident';
import {
  exportToExcel,
  prepareIncidentsForExport,
  prepareResponsesForExport,
  parseExcelToIncidents,
  parseExcelToResponses
} from '@/utils/excelUtils';
import {
  detectCoordinateFormat,
  CoordinateFormat,
  parseCoordinate
} from '@/utils/coordinateUtils';
import DataValidationTab from './DataValidationTab';
import DataExplorationTab from './DataExplorationTab';
import SymbologyEditorTab from './SymbologyEditorTab';

interface SettingsModalProps {
  onClose: () => void;
}

type TabType = 'data-management' | 'data-validation' | 'data-exploration' | 'symbology-editor' | 'general' | 'appearance';

const SettingsModal: React.FC<SettingsModalProps> = ({ onClose }) => {
  const [activeTab, setActiveTab] = useState<TabType>('data-management');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);
  const { loadIncidents, incidents } = useIncidentStore();
  const { loadResponses, responses } = useResponseStore();

  // File input refs
  const incidentFileInputRef = React.useRef<HTMLInputElement>(null);
  const responseFileInputRef = React.useRef<HTMLInputElement>(null);

  // Function to clear all incidents and responses but keep the database empty
  const handleClearAll = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      // Clear all incidents and responses
      await db.incidents.clear();
      await db.responses.clear();

      // Reload the stores to reflect the changes
      await loadIncidents();
      await loadResponses();

      setResult({
        success: true,
        message: 'All incidents and responses have been deleted. The database is now empty.'
      });
    } catch (error) {
      console.error('Failed to clear database:', error);
      setResult({
        success: false,
        message: `Failed to clear database: ${error instanceof Error ? error.message : String(error)}`
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to reset the database with mock data
  const handleResetWithMockData = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      // Use the built-in resetDatabase function
      const success = await db.resetDatabase();

      // Reload the stores to reflect the changes
      await loadIncidents();
      await loadResponses();

      if (success) {
        setResult({
          success: true,
          message: 'Database has been reset with mock data.'
        });
      } else {
        throw new Error('Database reset returned false');
      }
    } catch (error) {
      console.error('Failed to reset database:', error);
      setResult({
        success: false,
        message: `Failed to reset database: ${error instanceof Error ? error.message : String(error)}`
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to export incidents data as CSV
  const handleExportIncidentsAsCSV = () => {
    try {
      if (incidents.length === 0) {
        setResult({
          success: false,
          message: 'No incidents to export.'
        });
        return;
      }

      // Create CSV header
      const headers = Object.keys(incidents[0]).filter(key =>
        key !== 'attachments' && key !== 'location' && key !== 'tags'
      );

      // Add special headers for complex fields - ensure latitude comes before longitude
      if (!headers.includes('latitude')) headers.push('latitude');
      if (!headers.includes('longitude')) headers.push('longitude');
      if (!headers.includes('tags')) headers.push('tags');

      // Create CSV content
      let csvContent = headers.join(',') + '\n';

      // Add data rows
      incidents.forEach(incident => {
        const row = headers.map(header => {
          if (header === 'latitude') {
            // Ensure latitude is a valid number
            const lat = incident.location?.latitude;
            return typeof lat === 'number' && !isNaN(lat) ? lat : 0;
          }
          if (header === 'longitude') {
            // Ensure longitude is a valid number
            const lng = incident.location?.longitude;
            return typeof lng === 'number' && !isNaN(lng) ? lng : 0;
          }
          if (header === 'tags') return incident.tags ? incident.tags.join(';') : '';

          // Handle dates
          if (header === 'reportedAt' || header === 'resolvedAt') {
            return incident[header as keyof Incident] instanceof Date
              ? (incident[header as keyof Incident] as Date).toISOString()
              : incident[header as keyof Incident];
          }

          return incident[header as keyof Incident];
        }).join(',');

        csvContent += row + '\n';
      });

      // Create and download the file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `incidents_export_${new Date().toISOString().slice(0, 10)}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setResult({
        success: true,
        message: `Successfully exported ${incidents.length} incidents as CSV.`
      });
    } catch (error) {
      console.error('Failed to export incidents as CSV:', error);
      setResult({
        success: false,
        message: `Failed to export incidents as CSV: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  };

  // Function to export incidents data as Excel
  const handleExportIncidentsAsExcel = () => {
    try {
      if (incidents.length === 0) {
        setResult({
          success: false,
          message: 'No incidents to export.'
        });
        return;
      }

      // Prepare data for Excel export
      const data = prepareIncidentsForExport(incidents);

      // Export to Excel
      exportToExcel(data, `incidents_export_${new Date().toISOString().slice(0, 10)}.xlsx`);

      setResult({
        success: true,
        message: `Successfully exported ${incidents.length} incidents as Excel.`
      });
    } catch (error) {
      console.error('Failed to export incidents as Excel:', error);
      setResult({
        success: false,
        message: `Failed to export incidents as Excel: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  };

  // Function to export incidents (wrapper that calls the appropriate function)
  const handleExportIncidents = () => {
    handleExportIncidentsAsExcel();
  };

  // Function to export responses data as CSV
  const handleExportResponsesAsCSV = () => {
    try {
      if (responses.length === 0) {
        setResult({
          success: false,
          message: 'No responses to export.'
        });
        return;
      }

      // Create CSV header
      const headers = Object.keys(responses[0]).filter(key =>
        key !== 'attachments' && key !== 'location' && key !== 'tags' &&
        key !== 'units' && key !== 'relatedIncidents' && key !== 'equipmentUsed' &&
        key !== 'objectives'
      );

      // Add special headers for complex fields - ensure latitude comes before longitude
      if (!headers.includes('latitude')) headers.push('latitude');
      if (!headers.includes('longitude')) headers.push('longitude');
      if (!headers.includes('tags')) headers.push('tags');
      if (!headers.includes('units')) headers.push('units');
      if (!headers.includes('relatedIncidents')) headers.push('relatedIncidents');
      if (!headers.includes('equipmentUsed')) headers.push('equipmentUsed');
      if (!headers.includes('objectives')) headers.push('objectives');

      // Create CSV content
      let csvContent = headers.join(',') + '\n';

      // Add data rows
      responses.forEach(response => {
        const row = headers.map(header => {
          if (header === 'latitude') {
            // Ensure latitude is a valid number
            const lat = response.location?.latitude;
            return typeof lat === 'number' && !isNaN(lat) ? lat : 0;
          }
          if (header === 'longitude') {
            // Ensure longitude is a valid number
            const lng = response.location?.longitude;
            return typeof lng === 'number' && !isNaN(lng) ? lng : 0;
          }
          if (header === 'tags') return response.tags ? response.tags.join(';') : '';
          if (header === 'units') return response.units ? response.units.join(';') : '';
          if (header === 'relatedIncidents') return response.relatedIncidents ? response.relatedIncidents.join(';') : '';
          if (header === 'equipmentUsed') return response.equipmentUsed ? response.equipmentUsed.join(';') : '';
          if (header === 'objectives') return response.objectives ? response.objectives.join(';') : '';

          // Handle dates
          if (header === 'startDate' || header === 'endDate') {
            return response[header as keyof Response] instanceof Date
              ? (response[header as keyof Response] as Date).toISOString()
              : response[header as keyof Response];
          }

          return response[header as keyof Response];
        }).join(',');

        csvContent += row + '\n';
      });

      // Create and download the file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `responses_export_${new Date().toISOString().slice(0, 10)}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setResult({
        success: true,
        message: `Successfully exported ${responses.length} responses as CSV.`
      });
    } catch (error) {
      console.error('Failed to export responses as CSV:', error);
      setResult({
        success: false,
        message: `Failed to export responses as CSV: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  };

  // Function to export responses data as Excel
  const handleExportResponsesAsExcel = () => {
    try {
      if (responses.length === 0) {
        setResult({
          success: false,
          message: 'No responses to export.'
        });
        return;
      }

      // Prepare data for Excel export
      const data = prepareResponsesForExport(responses);

      // Export to Excel
      exportToExcel(data, `responses_export_${new Date().toISOString().slice(0, 10)}.xlsx`);

      setResult({
        success: true,
        message: `Successfully exported ${responses.length} responses as Excel.`
      });
    } catch (error) {
      console.error('Failed to export responses as Excel:', error);
      setResult({
        success: false,
        message: `Failed to export responses as Excel: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  };

  // Function to export responses (wrapper that calls the appropriate function)
  const handleExportResponses = () => {
    handleExportResponsesAsExcel();
  };

  // Function to handle incident import (CSV or Excel)
  const handleIncidentImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsLoading(true);
    setResult(null);

    // Check file extension to determine how to process it
    const fileExtension = file.name.split('.').pop()?.toLowerCase();

    if (fileExtension === 'xlsx' || fileExtension === 'xls') {
      // Handle Excel file
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const data = e.target?.result;
          if (!data) throw new Error('Failed to read file');

          // Parse Excel data
          const incidents = parseExcelToIncidents(data as ArrayBuffer);

          console.log('Processed incidents from Excel:', incidents.length);

          // Add to database
          if (incidents.length > 0) {
            await db.incidents.bulkAdd(incidents as Incident[]);
            await loadIncidents();

            setResult({
              success: true,
              message: `Successfully imported ${incidents.length} incidents from Excel.`
            });
          } else {
            throw new Error('No valid incidents found in Excel file.');
          }
        } catch (error) {
          console.error('Failed to import incidents from Excel:', error);
          setResult({
            success: false,
            message: `Failed to import incidents from Excel: ${error instanceof Error ? error.message : String(error)}`
          });
        } finally {
          setIsLoading(false);
          // Reset file input
          if (incidentFileInputRef.current) {
            incidentFileInputRef.current.value = '';
          }
        }
      };

      reader.onerror = () => {
        setIsLoading(false);
        setResult({
          success: false,
          message: 'Failed to read Excel file'
        });
        // Reset file input
        if (incidentFileInputRef.current) {
          incidentFileInputRef.current.value = '';
        }
      };

      reader.readAsArrayBuffer(file);
    } else {
      // Handle CSV file
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const csvData = e.target?.result as string;
          if (!csvData) throw new Error('Failed to read file');

          // Parse CSV
          const rows = csvData.split('\n').filter(row => row.trim()); // Remove empty rows
          if (rows.length < 2) {
            throw new Error('CSV file must contain at least a header row and one data row');
          }

          // Parse headers, handling potential quotes
          const headers = rows[0].split(',').map(header => header.trim().replace(/^"(.*)"$/, '$1'));

          // Check if required fields are present
          const requiredFields = ['title', 'description', 'type', 'severity', 'status', 'address'];
          const missingFields = requiredFields.filter(field => !headers.includes(field));

          if (missingFields.length > 0) {
            throw new Error(`CSV is missing required fields: ${missingFields.join(', ')}`);
          }

          // Parse rows into incidents
          const incidents: Partial<Incident>[] = [];

          // Debug info
          console.log('CSV Headers:', headers);
          console.log('Total rows to process:', rows.length - 1);

          for (let i = 1; i < rows.length; i++) {
            // Skip empty rows
            if (!rows[i].trim()) continue;

            // Split the row by comma, but handle quoted values properly
            let values: string[] = [];
            let inQuotes = false;
            let currentValue = '';

            for (let j = 0; j < rows[i].length; j++) {
              const char = rows[i][j];

              if (char === '"' && (j === 0 || rows[i][j-1] !== '\\')) {
                inQuotes = !inQuotes;
              } else if (char === ',' && !inQuotes) {
                values.push(currentValue);
                currentValue = '';
              } else {
                currentValue += char;
              }
            }

            // Add the last value
            values.push(currentValue);

            // Ensure we have the right number of values
            if (values.length !== headers.length) {
              console.warn(`Row ${i} has ${values.length} values, expected ${headers.length}. Attempting to process anyway.`);
              // Pad or truncate values to match headers length
              if (values.length < headers.length) {
                values = [...values, ...Array(headers.length - values.length).fill('')];
              } else {
                values = values.slice(0, headers.length);
              }
            }

            const incident: any = {};

            // Process each field
            headers.forEach((header, index) => {
              let value = values[index]?.trim() || '';
              // Remove quotes if present
              value = value.replace(/^"(.*)"$/, '$1');

              if (!value) return;

              if (header === 'latitude') {
                if (!incident.location) incident.location = { latitude: 0, longitude: 0 };

                // Handle empty or non-numeric values
                if (value === '' || value === 'undefined' || value === 'null') {
                  console.warn(`Empty latitude value, using default`);
                  incident.location.latitude = 0;
                  return;
                }

                // Detect coordinate format
                const format = detectCoordinateFormat(value);

                let parsedLat: number | null = null;

                if (format === CoordinateFormat.DECIMAL_DEGREES) {
                  // Parse as decimal degrees
                  parsedLat = parseFloat(value);
                } else if (format === CoordinateFormat.DMS) {
                  // Parse DMS format
                  parsedLat = parseCoordinate(value);
                  if (parsedLat !== null) {
                    console.log(`Converted DMS latitude: ${value} to decimal: ${parsedLat}`);
                  }
                } else {
                  // Try to parse as decimal
                  parsedLat = parseFloat(value);
                }

                // Ensure latitude is a valid number and within range (-90 to 90)
                if (parsedLat !== null && !isNaN(parsedLat) && parsedLat >= -90 && parsedLat <= 90) {
                  incident.location.latitude = parsedLat;
                  console.log(`Parsed latitude: ${parsedLat} from value: ${value}`);
                } else {
                  console.warn(`Invalid latitude value: ${value}, using default`);
                  incident.location.latitude = 0;
                }
              } else if (header === 'longitude') {
                if (!incident.location) incident.location = { latitude: 0, longitude: 0 };

                // Handle empty or non-numeric values
                if (value === '' || value === 'undefined' || value === 'null') {
                  console.warn(`Empty longitude value, using default`);
                  incident.location.longitude = 0;
                  return;
                }

                // Detect coordinate format
                const format = detectCoordinateFormat(value);

                let parsedLng: number | null = null;

                if (format === CoordinateFormat.DECIMAL_DEGREES) {
                  // Parse as decimal degrees
                  parsedLng = parseFloat(value);
                } else if (format === CoordinateFormat.DMS) {
                  // Parse DMS format
                  parsedLng = parseCoordinate(value);
                  if (parsedLng !== null) {
                    console.log(`Converted DMS longitude: ${value} to decimal: ${parsedLng}`);
                  }
                } else {
                  // Try to parse as decimal
                  parsedLng = parseFloat(value);
                }

                // Ensure longitude is a valid number and within range (-180 to 180)
                if (parsedLng !== null && !isNaN(parsedLng) && parsedLng >= -180 && parsedLng <= 180) {
                  incident.location.longitude = parsedLng;
                  console.log(`Parsed longitude: ${parsedLng} from value: ${value}`);
                } else {
                  console.warn(`Invalid longitude value: ${value}, using default`);
                  incident.location.longitude = 0;
                }
              } else if (header === 'tags') {
                incident.tags = value.split(';').map(tag => tag.trim()).filter(Boolean);
              } else if (header === 'reportedAt' || header === 'resolvedAt') {
                try {
                  incident[header] = new Date(value);
                  // Check if date is valid
                  if (isNaN(incident[header].getTime())) {
                    console.warn(`Invalid date for ${header}: ${value}, using current date`);
                    incident[header] = header === 'reportedAt' ? new Date() : undefined;
                  }
                } catch (err) {
                  console.warn(`Error parsing date for ${header}: ${value}, using current date`);
                  incident[header] = header === 'reportedAt' ? new Date() : undefined;
                }
              } else {
                incident[header] = value;
              }
            });

            // Generate ID if not provided or empty
            if (!incident.id || incident.id === '') {
              incident.id = `INC-${Date.now()}-${i}`;
            }

            // Ensure location exists and is valid
            if (!incident.location) {
              incident.location = { latitude: 0, longitude: 0 };
            } else {
              // Validate latitude and longitude
              if (typeof incident.location.latitude !== 'number' || isNaN(incident.location.latitude)) {
                console.warn(`Invalid latitude in row ${i}, using default`);
                incident.location.latitude = 0;
              }
              if (typeof incident.location.longitude !== 'number' || isNaN(incident.location.longitude)) {
                console.warn(`Invalid longitude in row ${i}, using default`);
                incident.location.longitude = 0;
              }
            }

            // Set reportedAt if not provided
            if (!incident.reportedAt) {
              incident.reportedAt = new Date();
            }

            // Ensure required fields have values
            let missingRequiredValues = false;
            for (const field of requiredFields) {
              if (!incident[field]) {
                if (field === 'severity') incident[field] = 'LOW';
                else if (field === 'status') incident[field] = 'REPORTED';
                else if (field === 'type') incident[field] = 'OTHER';
                else if (field === 'title') incident[field] = `Incident ${i}`;
                else if (field === 'description') incident[field] = 'Imported incident';
                else if (field === 'address') incident[field] = 'Unknown location';
                else {
                  missingRequiredValues = true;
                  console.warn(`Row ${i} is missing required field: ${field}`);
                }
              }
            }

            if (!missingRequiredValues) {
              incidents.push(incident);
            }
          }

          console.log('Processed incidents from CSV:', incidents.length);

          // Count invalid coordinates for reporting
          const invalidCoordinatesCount = rows.length - 1 - incidents.length;

          // Add to database
          if (incidents.length > 0) {
            await db.incidents.bulkAdd(incidents as Incident[]);
            await loadIncidents();

            let message = `Successfully imported ${incidents.length} incidents from CSV.`;

            // Add warning about invalid records if any
            if (invalidCoordinatesCount > 0) {
              message += ` Warning: ${invalidCoordinatesCount} records were skipped due to invalid data (see console for details).`;
            }

            setResult({
              success: true,
              message: message
            });
          } else {
            throw new Error('No valid incidents found in CSV. Check the console for more details.');
          }
        } catch (error) {
          console.error('Failed to import incidents from CSV:', error);
          setResult({
            success: false,
            message: `Failed to import incidents from CSV: ${error instanceof Error ? error.message : String(error)}`
          });
        } finally {
          setIsLoading(false);
          // Reset file input
          if (incidentFileInputRef.current) {
            incidentFileInputRef.current.value = '';
          }
        }
      };

      reader.onerror = () => {
        setIsLoading(false);
        setResult({
          success: false,
          message: 'Failed to read CSV file'
        });
        // Reset file input
        if (incidentFileInputRef.current) {
          incidentFileInputRef.current.value = '';
        }
      };

      reader.readAsText(file);
    }
  };

  // Function to handle response import (CSV or Excel)
  const handleResponseImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsLoading(true);
    setResult(null);

    // Check file extension to determine how to process it
    const fileExtension = file.name.split('.').pop()?.toLowerCase();

    if (fileExtension === 'xlsx' || fileExtension === 'xls') {
      // Handle Excel file
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const data = e.target?.result;
          if (!data) throw new Error('Failed to read file');

          // Parse Excel data with the updated function that returns both responses and date warnings
          const { responses, dateWarnings } = parseExcelToResponses(data as ArrayBuffer);

          console.log('Processed responses from Excel:', responses.length);

          if (dateWarnings.length > 0) {
            console.log('Date format warnings:', dateWarnings);
          }

          // Add to database
          if (responses.length > 0) {
            await db.responses.bulkAdd(responses as Response[]);
            await loadResponses();

            let message = `Successfully imported ${responses.length} responses from Excel.`;

            // Add warning about date format conversions if any
            if (dateWarnings.length > 0) {
              const uniqueFormats = new Set(dateWarnings.map(w => w.detectedFormat));
              const formatList = Array.from(uniqueFormats).join(', ');

              // Check if we have any Excel date format issues
              const hasExcelDateIssues = dateWarnings.some(w => w.detectedFormat === 'EXCEL_DATE');

              if (hasExcelDateIssues) {
                message += ` WARNING: Detected problematic Excel date formats (e.g., "01 Jan 45570 00:00"). The system attempted to extract the original dates from the file. Please verify that the dates were correctly imported. Your original dates appear to be in MM/DD/YYYY format.`;
              } else {
                message += ` Warning: ${dateWarnings.length} date fields were automatically converted from non-standard formats (${formatList}). Please verify the dates are correct and consider using YYYY-MM-DD format in future imports.`;
              }
            }

            setResult({
              success: true,
              message: message
            });
          } else {
            throw new Error('No valid responses found in Excel file.');
          }
        } catch (error) {
          console.error('Failed to import responses from Excel:', error);
          setResult({
            success: false,
            message: `Failed to import responses from Excel: ${error instanceof Error ? error.message : String(error)}`
          });
        } finally {
          setIsLoading(false);
          // Reset file input
          if (responseFileInputRef.current) {
            responseFileInputRef.current.value = '';
          }
        }
      };

      reader.onerror = () => {
        setIsLoading(false);
        setResult({
          success: false,
          message: 'Failed to read Excel file'
        });
        // Reset file input
        if (responseFileInputRef.current) {
          responseFileInputRef.current.value = '';
        }
      };

      reader.readAsArrayBuffer(file);
    } else {
      // Handle CSV file
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const csvData = e.target?.result as string;
          if (!csvData) throw new Error('Failed to read file');

          // Parse CSV
          const rows = csvData.split('\n').filter(row => row.trim()); // Remove empty rows
          if (rows.length < 2) {
            throw new Error('CSV file must contain at least a header row and one data row');
          }

          // Parse headers, handling potential quotes
          const headers = rows[0].split(',').map(header => header.trim().replace(/^"(.*)"$/, '$1'));

          // Check if required fields are present
          const requiredFields = ['title', 'description', 'type', 'status', 'address', 'commander'];
          const missingFields = requiredFields.filter(field => !headers.includes(field));

          if (missingFields.length > 0) {
            throw new Error(`CSV is missing required fields: ${missingFields.join(', ')}`);
          }

          // Parse rows into responses
          const responses: Partial<Response>[] = [];

          // Debug info
          console.log('CSV Headers:', headers);
          console.log('Total rows to process:', rows.length - 1);

          for (let i = 1; i < rows.length; i++) {
            // Skip empty rows
            if (!rows[i].trim()) continue;

            // Split the row by comma, but handle quoted values properly
            let values: string[] = [];
            let inQuotes = false;
            let currentValue = '';

            for (let j = 0; j < rows[i].length; j++) {
              const char = rows[i][j];

              if (char === '"' && (j === 0 || rows[i][j-1] !== '\\')) {
                inQuotes = !inQuotes;
              } else if (char === ',' && !inQuotes) {
                values.push(currentValue);
                currentValue = '';
              } else {
                currentValue += char;
              }
            }

            // Add the last value
            values.push(currentValue);

            // Ensure we have the right number of values
            if (values.length !== headers.length) {
              console.warn(`Row ${i} has ${values.length} values, expected ${headers.length}. Attempting to process anyway.`);
              // Pad or truncate values to match headers length
              if (values.length < headers.length) {
                values = [...values, ...Array(headers.length - values.length).fill('')];
              } else {
                values = values.slice(0, headers.length);
              }
            }

            const response: any = {};

            // Process each field
            headers.forEach((header, index) => {
              let value = values[index]?.trim() || '';
              // Remove quotes if present
              value = value.replace(/^"(.*)"$/, '$1');

              if (!value) return;

              if (header === 'latitude') {
                if (!response.location) response.location = { latitude: 0, longitude: 0 };

                // Handle empty or non-numeric values
                if (value === '' || value === 'undefined' || value === 'null') {
                  console.warn(`Empty latitude value, using default`);
                  response.location.latitude = 0;
                  return;
                }

                // Detect coordinate format
                const format = detectCoordinateFormat(value);

                let parsedLat: number | null = null;

                if (format === CoordinateFormat.DECIMAL_DEGREES) {
                  // Parse as decimal degrees
                  parsedLat = parseFloat(value);
                } else if (format === CoordinateFormat.DMS) {
                  // Parse DMS format
                  parsedLat = parseCoordinate(value);
                  if (parsedLat !== null) {
                    console.log(`Converted DMS latitude: ${value} to decimal: ${parsedLat}`);
                  }
                } else {
                  // Try to parse as decimal
                  parsedLat = parseFloat(value);
                }

                // Ensure latitude is a valid number and within range (-90 to 90)
                if (parsedLat !== null && !isNaN(parsedLat) && parsedLat >= -90 && parsedLat <= 90) {
                  response.location.latitude = parsedLat;
                  console.log(`Parsed latitude: ${parsedLat} from value: ${value}`);
                } else {
                  console.warn(`Invalid latitude value: ${value}, using default`);
                  response.location.latitude = 0;
                }
              } else if (header === 'longitude') {
                if (!response.location) response.location = { latitude: 0, longitude: 0 };

                // Handle empty or non-numeric values
                if (value === '' || value === 'undefined' || value === 'null') {
                  console.warn(`Empty longitude value, using default`);
                  response.location.longitude = 0;
                  return;
                }

                // Detect coordinate format
                const format = detectCoordinateFormat(value);

                let parsedLng: number | null = null;

                if (format === CoordinateFormat.DECIMAL_DEGREES) {
                  // Parse as decimal degrees
                  parsedLng = parseFloat(value);
                } else if (format === CoordinateFormat.DMS) {
                  // Parse DMS format
                  parsedLng = parseCoordinate(value);
                  if (parsedLng !== null) {
                    console.log(`Converted DMS longitude: ${value} to decimal: ${parsedLng}`);
                  }
                } else {
                  // Try to parse as decimal
                  parsedLng = parseFloat(value);
                }

                // Ensure longitude is a valid number and within range (-180 to 180)
                if (parsedLng !== null && !isNaN(parsedLng) && parsedLng >= -180 && parsedLng <= 180) {
                  response.location.longitude = parsedLng;
                  console.log(`Parsed longitude: ${parsedLng} from value: ${value}`);
                } else {
                  console.warn(`Invalid longitude value: ${value}, using default`);
                  response.location.longitude = 0;
                }
              } else if (header === 'tags') {
                response.tags = value.split(';').map(tag => tag.trim()).filter(Boolean);
              } else if (header === 'units') {
                response.units = value.split(';').map(unit => unit.trim()).filter(Boolean);
              } else if (header === 'relatedIncidents') {
                response.relatedIncidents = value.split(';').map(id => id.trim()).filter(Boolean);
              } else if (header === 'equipmentUsed') {
                response.equipmentUsed = value.split(';').map(eq => eq.trim()).filter(Boolean);
              } else if (header === 'objectives') {
                response.objectives = value.split(';').map(obj => obj.trim()).filter(Boolean);
              } else if (header === 'startDate' || header === 'endDate') {
                try {
                  response[header] = new Date(value);
                  // Check if date is valid
                  if (isNaN(response[header].getTime())) {
                    console.warn(`Invalid date for ${header}: ${value}, using current date`);
                    response[header] = header === 'startDate' ? new Date() : undefined;
                  }
                } catch (err) {
                  console.warn(`Error parsing date for ${header}: ${value}, using current date`);
                  response[header] = header === 'startDate' ? new Date() : undefined;
                }
              } else {
                response[header] = value;
              }
            });

            // Generate ID if not provided or empty
            if (!response.id || response.id === '') {
              response.id = `RESP-${Date.now()}-${i}`;
            }

            // Ensure location exists and is valid
            if (!response.location) {
              response.location = { latitude: 0, longitude: 0 };
            } else {
              // Validate latitude and longitude
              if (typeof response.location.latitude !== 'number' || isNaN(response.location.latitude)) {
                console.warn(`Invalid latitude in row ${i}, using default`);
                response.location.latitude = 0;
              }
              if (typeof response.location.longitude !== 'number' || isNaN(response.location.longitude)) {
                console.warn(`Invalid longitude in row ${i}, using default`);
                response.location.longitude = 0;
              }
            }

            // Set startDate if not provided
            if (!response.startDate) {
              response.startDate = new Date();
            }

            // Ensure units array exists
            if (!response.units) {
              response.units = [];
            }

            // Ensure required fields have values
            let missingRequiredValues = false;
            for (const field of requiredFields) {
              if (!response[field]) {
                if (field === 'status') response[field] = 'PLANNED';
                else if (field === 'type') response[field] = 'PATROL';
                else if (field === 'title') response[field] = `Response ${i}`;
                else if (field === 'description') response[field] = 'Imported response';
                else if (field === 'address') response[field] = 'Unknown location';
                else if (field === 'commander') response[field] = 'Unknown';
                else {
                  missingRequiredValues = true;
                  console.warn(`Row ${i} is missing required field: ${field}`);
                }
              }
            }

            if (!missingRequiredValues) {
              responses.push(response);
            }
          }

          console.log('Processed responses from CSV:', responses.length);

          // Count invalid coordinates for reporting
          const invalidCoordinatesCount = rows.length - 1 - responses.length;

          // Add to database
          if (responses.length > 0) {
            await db.responses.bulkAdd(responses as Response[]);
            await loadResponses();

            let message = `Successfully imported ${responses.length} responses from CSV.`;

            // Add warning about invalid records if any
            if (invalidCoordinatesCount > 0) {
              message += ` Warning: ${invalidCoordinatesCount} records were skipped due to invalid data (see console for details).`;
            }

            setResult({
              success: true,
              message: message
            });
          } else {
            throw new Error('No valid responses found in CSV. Check the console for more details.');
          }
        } catch (error) {
          console.error('Failed to import responses from CSV:', error);
          setResult({
            success: false,
            message: `Failed to import responses from CSV: ${error instanceof Error ? error.message : String(error)}`
          });
        } finally {
          setIsLoading(false);
          // Reset file input
          if (responseFileInputRef.current) {
            responseFileInputRef.current.value = '';
          }
        }
      };

      reader.onerror = () => {
        setIsLoading(false);
        setResult({
          success: false,
          message: 'Failed to read CSV file'
        });
        // Reset file input
        if (responseFileInputRef.current) {
          responseFileInputRef.current.value = '';
        }
      };

      reader.readAsText(file);
    }
  };

  return (
    <div className="bg-military-panel border border-military-border max-w-4xl w-full h-[80vh] flex flex-col"
         style={{ clipPath: 'polygon(0 0, calc(100% - 12px) 0, 100% 12px, 100% 100%, 12px 100%, 0 calc(100% - 12px))' }}>
      <div className="flex items-center justify-between px-6 py-4 bg-military-navy border-b border-military-border">
        <h2 className="text-lg font-military-heading text-military-white uppercase tracking-wider flex items-center">
          <SettingsIcon size={20} className="mr-2" />
          SETTINGS
        </h2>
        <Button
          variant="military"
          size="sm"
          onClick={onClose}
          className="military-btn"
        >
          <X size={16} />
        </Button>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <div className="w-48 bg-military-black border-r border-military-border p-4">
          <ul className="space-y-2">
            <li>
              <button
                className={`w-full text-left px-3 py-2 text-sm font-military-body ${activeTab === 'data-management' ? 'bg-military-darkgreen text-military-white' : 'text-military-white hover:bg-military-navy'}`}
                onClick={() => setActiveTab('data-management')}
              >
                <Database size={14} className="inline mr-2" />
                DATA MANAGEMENT
              </button>
            </li>
            <li>
              <button
                className={`w-full text-left px-3 py-2 text-sm font-military-body ${activeTab === 'data-validation' ? 'bg-military-darkgreen text-military-white' : 'text-military-white hover:bg-military-navy'}`}
                onClick={() => setActiveTab('data-validation')}
              >
                <FileCheck size={14} className="inline mr-2" />
                DATA VALIDATION
              </button>
            </li>
            <li>
              <button
                className={`w-full text-left px-3 py-2 text-sm font-military-body ${activeTab === 'data-exploration' ? 'bg-military-darkgreen text-military-white' : 'text-military-white hover:bg-military-navy'}`}
                onClick={() => setActiveTab('data-exploration')}
              >
                <Table size={14} className="inline mr-2" />
                DATA EXPLORATION
              </button>
            </li>
            <li>
              <button
                className={`w-full text-left px-3 py-2 text-sm font-military-body ${activeTab === 'symbology-editor' ? 'bg-military-darkgreen text-military-white' : 'text-military-white hover:bg-military-navy'}`}
                onClick={() => setActiveTab('symbology-editor')}
              >
                <Palette size={14} className="inline mr-2" />
                SYMBOLOGY EDITOR
              </button>
            </li>
            <li>
              <button
                className={`w-full text-left px-3 py-2 text-sm font-military-body ${activeTab === 'general' ? 'bg-military-darkgreen text-military-white' : 'text-military-white hover:bg-military-navy'}`}
                onClick={() => setActiveTab('general')}
              >
                <SettingsIcon size={14} className="inline mr-2" />
                GENERAL
              </button>
            </li>
            <li>
              <button
                className={`w-full text-left px-3 py-2 text-sm font-military-body ${activeTab === 'appearance' ? 'bg-military-darkgreen text-military-white' : 'text-military-white hover:bg-military-navy'}`}
                onClick={() => setActiveTab('appearance')}
              >
                <SettingsIcon size={14} className="inline mr-2" />
                APPEARANCE
              </button>
            </li>
          </ul>
        </div>

        {/* Content */}
        <div className="flex-1 bg-military-black p-6 overflow-y-auto">
          {activeTab === 'data-management' && (
            <div>
              <h3 className="text-lg font-military-heading text-military-white uppercase mb-6">DATA MANAGEMENT</h3>

              {/* Export Section */}
              <div className="mb-8 p-4 border border-military-border">
                <h4 className="text-md font-military-heading text-military-white uppercase mb-4">EXPORT DATA</h4>
                <p className="text-military-white font-military-body mb-4">
                  Export your incidents and responses data as Excel files for backup or analysis.
                </p>
                <div className="flex space-x-4">
                  <Button
                    variant="military"
                    className="military-btn bg-military-blue"
                    onClick={handleExportIncidents}
                    leftIcon={<FileSpreadsheet size={16} />}
                    disabled={isLoading || incidents.length === 0}
                  >
                    EXPORT INCIDENTS ({incidents.length})
                  </Button>
                  <Button
                    variant="military"
                    className="military-btn bg-military-blue"
                    onClick={handleExportResponses}
                    leftIcon={<FileSpreadsheet size={16} />}
                    disabled={isLoading || responses.length === 0}
                  >
                    EXPORT RESPONSES ({responses.length})
                  </Button>
                </div>
              </div>

              {/* Import Section */}
              <div className="mb-8 p-4 border border-military-border">
                <h4 className="text-md font-military-heading text-military-white uppercase mb-4">IMPORT DATA</h4>
                <p className="text-military-white font-military-body mb-4">
                  Import incidents and responses from Excel (.xlsx, .xls) or CSV files. The files must include all required fields.
                </p>

                <input
                  type="file"
                  accept=".csv,.xlsx,.xls"
                  ref={incidentFileInputRef}
                  onChange={handleIncidentImport}
                  className="hidden"
                />
                <input
                  type="file"
                  accept=".csv,.xlsx,.xls"
                  ref={responseFileInputRef}
                  onChange={handleResponseImport}
                  className="hidden"
                />

                <div className="flex space-x-4">
                  <Button
                    variant="military"
                    className="military-btn bg-military-darkgreen"
                    onClick={() => incidentFileInputRef.current?.click()}
                    leftIcon={<Upload size={16} />}
                    disabled={isLoading}
                  >
                    IMPORT INCIDENTS
                  </Button>
                  <Button
                    variant="military"
                    className="military-btn bg-military-darkgreen"
                    onClick={() => responseFileInputRef.current?.click()}
                    leftIcon={<Upload size={16} />}
                    disabled={isLoading}
                  >
                    IMPORT RESPONSES
                  </Button>
                </div>
              </div>

              {/* Database Management Section */}
              <div className="mb-8 p-4 border border-military-border">
                <h4 className="text-md font-military-heading text-military-white uppercase mb-4">DATABASE MANAGEMENT</h4>
                <p className="text-military-white font-military-body mb-4">
                  Clear all data or reset the database with mock data.
                </p>
                <div className="flex space-x-4">
                  <Button
                    variant="military"
                    className="military-btn bg-military-red"
                    onClick={handleClearAll}
                    leftIcon={<Trash2 size={16} />}
                    disabled={isLoading}
                  >
                    DELETE ALL DATA
                  </Button>
                  <Button
                    variant="military"
                    className="military-btn bg-military-darkgreen"
                    onClick={handleResetWithMockData}
                    leftIcon={<RefreshCw size={16} />}
                    disabled={isLoading}
                  >
                    LOAD MOCK DATA
                  </Button>
                </div>
              </div>

              {/* Result Message */}
              {result && (
                <div className={`mt-4 p-3 border ${result.success ? 'border-military-darkgreen bg-military-darkgreen bg-opacity-20' : 'border-military-red bg-military-red bg-opacity-20'}`}>
                  <p className="text-military-white font-military-body flex items-start">
                    {result.success ? (
                      <Check size={16} className="text-military-green mr-2 mt-1 flex-shrink-0" />
                    ) : (
                      <AlertTriangle size={16} className="text-military-amber mr-2 mt-1 flex-shrink-0" />
                    )}
                    {result.message}
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'data-validation' && (
            <DataValidationTab incidents={incidents} responses={responses} />
          )}

          {activeTab === 'data-exploration' && (
            <DataExplorationTab incidents={incidents} responses={responses} />
          )}

          {activeTab === 'symbology-editor' && (
            <SymbologyEditorTab />
          )}

          {activeTab === 'general' && (
            <div>
              <h3 className="text-lg font-military-heading text-military-white uppercase mb-6">GENERAL SETTINGS</h3>
              <p className="text-military-white font-military-body">
                General settings will be available in a future update.
              </p>
            </div>
          )}

          {activeTab === 'appearance' && (
            <div>
              <h3 className="text-lg font-military-heading text-military-white uppercase mb-6">APPEARANCE SETTINGS</h3>
              <p className="text-military-white font-military-body">
                Appearance settings will be available in a future update.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SettingsModal;
