import React from 'react';
import { Download, FileImage, FileText } from 'lucide-react';
import Button from '@/components/ui/Button';

interface ExportToolsProps {
  className?: string;
}

const ExportTools: React.FC<ExportToolsProps> = ({ className = '' }) => {
  const handleExportPNG = () => {
    console.log('Export PNG functionality not implemented');
  };

  const handleExportPDF = () => {
    console.log('Export PDF functionality not implemented');
  };

  const handleExportData = () => {
    console.log('Export data functionality not implemented');
  };

  return (
    <div className={`p-2 space-y-2 ${className}`}>
      <div className="text-white text-sm font-bold mb-2">EXPORT</div>
      <Button
        size="sm"
        variant="ghost"
        className="w-full text-left justify-start text-white"
        onClick={handleExportPNG}
      >
        <FileImage size={14} className="mr-2" />
        Export PNG
      </Button>
      <Button
        size="sm"
        variant="ghost"
        className="w-full text-left justify-start text-white"
        onClick={handleExportPDF}
      >
        <FileText size={14} className="mr-2" />
        Export PDF
      </Button>
      <Button
        size="sm"
        variant="ghost"
        className="w-full text-left justify-start text-white"
        onClick={handleExportData}
      >
        <Download size={14} className="mr-2" />
        Export Data
      </Button>
    </div>
  );
};

export default ExportTools;
