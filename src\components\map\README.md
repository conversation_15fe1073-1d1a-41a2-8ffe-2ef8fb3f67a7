# MapLibre Tactical Map System

A comprehensive tactical mapping system built with MapLibre GL JS, designed for military and tactical applications with advanced features including auto-hide toolbars, enhanced symbology, viewshed analysis, and performance optimizations.

## Features

### 🗺️ Core Map Functionality
- **MapLibre GL JS Integration**: High-performance vector and raster map rendering
- **Multiple Base Layers**: Satellite, terrain, and street map options
- **Performance Optimized**: Marker pooling, viewport filtering, and level-of-detail rendering
- **Responsive Design**: Adapts to different screen sizes and orientations

### 🎛️ Four-Corner Toolbar System
- **Top-Left**: Navigation and drawing tools
- **Top-Right**: Base layer controls
- **Bottom-Left**: Legend and symbols management
- **Bottom-Right**: Map controls (zoom, rotation, etc.)
- **Bottom-Center**: Real-time coordinate display

### 🔄 Auto-Hide Functionality
- **Activity Tracking**: Monitors mouse movement, keyboard input, and map interactions
- **Smart Timing**: Toolbars auto-hide after 3 seconds of inactivity
- **Hover Override**: Toolbars remain visible when hovered
- **Smooth Animations**: Consistent fade and slide transitions

### 🎨 Enhanced Symbology
- **Military Standards**: NATO-compatible tactical symbols
- **Customizable Symbols**: Create and edit custom military symbols
- **Category Organization**: Friendly, hostile, neutral, and unknown forces
- **Symbol Library**: Extensive collection of military symbols
- **Import/Export**: Save and share symbol configurations

### 👁️ Viewshed Analysis
- **Interactive Placement**: Click to place observation points
- **Configurable Parameters**: Observer height, target height, max distance
- **Visual Feedback**: Real-time viewshed polygon display
- **Multiple Viewsheds**: Support for multiple simultaneous analyses
- **Export Capabilities**: Save viewshed data for further analysis

### 🎯 Tactical Legend
- **Dynamic Updates**: Automatically reflects current map data
- **Editable Entries**: Modify legend items and symbols
- **Visibility Controls**: Show/hide specific data layers
- **Custom Categories**: Add custom symbol categories
- **Collapsible Interface**: Expandable sections for better space management

### ✏️ Drawing Tools
- **Vector Drawing**: Points, lines, polygons, rectangles, circles
- **Military Styling**: Tactical-themed drawing styles
- **Edit Capabilities**: Modify existing drawings
- **Export Options**: Save drawings as GeoJSON

### 📍 Advanced Markers
- **Optimized Rendering**: Canvas-based symbol generation
- **Performance Pooling**: Reuse marker instances for better performance
- **Interactive Popups**: Detailed information displays
- **Severity Indicators**: Visual severity and status indicators
- **Clustering Support**: Automatic clustering for large datasets

## Component Architecture

```
MapLibrePanel (Main Container)
├── MapLibreToolbar (Four-corner layout)
├── MapLibreMarkers (Optimized marker rendering)
├── MapLibreDrawing (Vector drawing tools)
├── MapLibreContextMenu (Right-click menu)
├── TacticalLegend (Enhanced legend system)
├── ViewshedAnalysis (Viewshed calculations)
└── EnhancedSymbology (Military symbol management)
```

## Usage

### Basic Implementation

```tsx
import MapLibrePanelWrapper from '@/components/map/MapLibrePanel';

function App() {
  return (
    <div className="h-screen w-screen">
      <MapLibrePanelWrapper />
    </div>
  );
}
```

### With Custom Configuration

```tsx
import { MapLibrePanel } from '@/components/map/MapLibrePanel';

function CustomMap() {
  return (
    <MapLibrePanel 
      className="custom-map-styles"
    />
  );
}
```

## Configuration

### Performance Settings

The system uses optimized performance settings defined in `@/utils/mapPerformance.ts`:

```typescript
export const defaultPerformanceConfig: PerformanceConfig = {
  maxMarkersBeforeClustering: 100,
  clusterRadius: 50,
  maxZoomForClustering: 14,
  debounceDelay: 100,
  useWebGL: true,
  enableLOD: true,
  maxRenderDistance: 50000 // 50km
};
```

### Base Layer Configuration

Supports multiple base layer types:
- **Satellite**: Google Satellite imagery with labels
- **Terrain**: Google Terrain maps
- **Streets**: OpenStreetMap tiles
- **Custom**: Any MapLibre-compatible style

### Auto-Hide Settings

Toolbar auto-hide behavior can be customized:
- **Hide Delay**: 3 seconds (configurable)
- **Activity Events**: Mouse, keyboard, map interactions
- **Hover Behavior**: Prevents hiding when hovered
- **Animation Duration**: 300ms transitions

## Styling

### CSS Classes

The system uses consistent CSS classes for styling:

```css
/* Main containers */
.maplibregl-map { /* Map container */ }
.tactical-popup { /* Popup styling */ }
.military-symbol { /* Symbol markers */ }
.viewshed-marker { /* Viewshed points */ }

/* Toolbar sections */
.toolbar-section { /* Individual toolbar sections */ }
.toolbar-section.hidden { /* Hidden state */ }

/* Severity indicators */
.severity-critical { /* Critical incidents */ }
.severity-high { /* High priority */ }
.severity-medium { /* Medium priority */ }
.severity-low { /* Low priority */ }
```

### Theme Integration

The system integrates with the application's dark theme:
- **Military Color Scheme**: Dark backgrounds with tactical colors
- **Consistent Typography**: Monospace fonts for technical displays
- **Professional Icons**: Lucide icons with military context
- **Accessibility**: High contrast and readable text

## Performance Optimizations

### Marker Management
- **Object Pooling**: Reuse marker instances
- **Viewport Filtering**: Only render visible markers
- **Level of Detail**: Reduce detail at lower zoom levels
- **Debounced Updates**: Prevent excessive re-renders

### Memory Management
- **Resource Cleanup**: Automatic cleanup of unused resources
- **Garbage Collection**: Periodic memory cleanup
- **Efficient Rendering**: Canvas-based symbol generation

### Adaptive Configuration
- **Device Detection**: Adjust settings based on device capabilities
- **Performance Monitoring**: Real-time performance tracking
- **Dynamic Optimization**: Automatic quality adjustments

## Integration with Stores

The system integrates with Zustand stores for state management:

```typescript
// Incident data
const { filteredIncidents, selectIncident } = useIncidentStore();

// Response data
const { responses, selectResponse } = useResponseStore();

// Filter settings
const { filters } = useFilterStore();
```

## Event Handling

### Map Events
- **Click**: Place symbols, select features
- **Context Menu**: Right-click actions
- **Move/Zoom**: Update coordinate display
- **Load**: Initialize components

### User Interactions
- **Toolbar Actions**: Tool selection, layer changes
- **Symbol Placement**: Interactive symbol positioning
- **Drawing**: Vector feature creation
- **Viewshed**: Analysis point placement

## Accessibility

### Keyboard Support
- **Tab Navigation**: Navigate through toolbar elements
- **Escape Key**: Close popups and menus
- **Enter/Space**: Activate buttons and controls

### Screen Reader Support
- **ARIA Labels**: Descriptive labels for all interactive elements
- **Role Attributes**: Proper semantic markup
- **Focus Management**: Logical focus order

### Visual Accessibility
- **High Contrast**: Military-themed color scheme
- **Scalable Text**: Responsive font sizes
- **Clear Icons**: Recognizable symbols and indicators

## Browser Compatibility

- **Modern Browsers**: Chrome 79+, Firefox 70+, Safari 13+, Edge 79+
- **WebGL Support**: Required for optimal performance
- **Mobile Support**: Responsive design for tablets and phones
- **Touch Interactions**: Touch-friendly controls and gestures

## Dependencies

### Core Dependencies
- `maplibre-gl`: Map rendering engine
- `@mapbox/mapbox-gl-draw`: Drawing tools (MapLibre compatible)
- `react`: UI framework
- `zustand`: State management

### Utility Dependencies
- `lucide-react`: Icons
- `@turf/turf`: Geospatial calculations
- Custom utilities for performance and coordinate handling

## Troubleshooting

### Common Issues

1. **Map Not Loading**
   - Check MapLibre GL JS version compatibility
   - Verify CSS imports are correct
   - Ensure container has proper dimensions

2. **Performance Issues**
   - Reduce `maxMarkersBeforeClustering` setting
   - Enable level-of-detail rendering
   - Check for memory leaks in marker cleanup

3. **Drawing Tools Not Working**
   - Verify @mapbox/mapbox-gl-draw is installed
   - Check for CSS import conflicts
   - Ensure proper MapLibre version compatibility

4. **Symbols Not Displaying**
   - Check symbol configuration format
   - Verify canvas rendering support
   - Review console for rendering errors

### Debug Mode

Enable debug logging by setting:
```javascript
localStorage.setItem('maplibre-debug', 'true');
```

This will provide detailed console output for troubleshooting.
