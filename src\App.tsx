import React, { useEffect, useState } from 'react';
import Dashboard from '@/components/dashboard/Dashboard';
import { Database, AlertCircle } from 'lucide-react';
import { initDatabase } from '@/services/db/database';
import Button from '@/components/ui/Button';
import ErrorBoundary from '@/components/ui/ErrorBoundary';
import { ModalProvider } from '@/components/ui/ModalManager';
import { errorService, ErrorCategory } from '@/services/logging/errorService';

// Clean up any NATO symbol settings from previous implementation
import '@/utils/cleanupNATOSymbols';

function App() {
  const [dbInitialized, setDbInitialized] = useState<boolean | null>(null);

  useEffect(() => {
    const init = async () => {
      try {
        // Log database initialization attempt
        errorService.info('Initializing database', ErrorCategory.DATABASE);

        const result = await initDatabase();

        if (result) {
          errorService.info('Database initialized successfully', ErrorCategory.DATABASE);
        } else {
          errorService.warn('Database initialization returned false', ErrorCategory.DATABASE);
        }

        setDbInitialized(result);
      } catch (error) {
        // Log the error
        errorService.logError(
          error instanceof Error ? error : new Error(String(error)),
          'Failed to initialize database',
          ErrorCategory.DATABASE
        );
        setDbInitialized(false);
      }
    };

    init();
  }, []);

  if (dbInitialized === null) {
    return (
      <div className="min-h-screen military-theme flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-16 h-16 border-4 border-military-accent border-t-transparent rounded-full mx-auto"></div>
          <p className="mt-4 text-military-white font-military-body">INITIALIZING TACTICAL SYSTEM...</p>
        </div>
      </div>
    );
  }

  if (dbInitialized === false) {
    return (
      <div className="min-h-screen military-theme flex items-center justify-center">
        <div className="military-container bg-military-black p-8 max-w-md text-center"
             style={{ clipPath: 'polygon(0 0, calc(100% - 12px) 0, 100% 12px, 100% 100%, 12px 100%, 0 calc(100% - 12px))' }}>
          <AlertCircle className="h-12 w-12 text-military-red mx-auto" />
          <h2 className="text-xl font-military-heading text-military-white uppercase mt-4">DATABASE INITIALIZATION FAILURE</h2>
          <p className="mt-2 text-military-white font-military-body">
            UNABLE TO INITIALIZE TACTICAL DATABASE. THIS MAY BE DUE TO BROWSER STORAGE RESTRICTIONS.
          </p>
          <Button
            variant="military"
            className="mt-6 military-btn bg-military-darkgreen"
            onClick={() => {
              errorService.info('Retrying database initialization', ErrorCategory.DATABASE);
              window.location.reload();
            }}
          >
            RETRY
          </Button>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <ModalProvider>
        <Dashboard />
      </ModalProvider>
    </ErrorBoundary>
  );
}

export default App;