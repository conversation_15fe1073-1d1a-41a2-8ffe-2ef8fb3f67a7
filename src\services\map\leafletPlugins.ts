import L from 'leaflet';

// Function to check if Leaflet plugins are available
export const checkLeafletPlugins = () => {
  // Check Leaflet Draw
  if (L.Draw) {
    console.log('Leaflet Draw is available');
  } else {
    console.warn('Leaflet Draw is not available');
  }

  // Check Leaflet Fullscreen
  // @ts-ignore
  if (L.control.fullscreen) {
    console.log('Leaflet Fullscreen is available');
  } else {
    console.warn('Leaflet Fullscreen is not available');
  }

  // Check Leaflet Measure
  // @ts-ignore
  if (L.Control.Measure) {
    console.log('Leaflet Measure is available');
  } else {
    console.warn('Leaflet Measure is not available');
  }

  // Check Leaflet Locate Control
  // @ts-ignore
  if (L.control.locate) {
    console.log('Leaflet Locate Control is available');
  } else {
    console.warn('Leaflet Locate Control is not available');
  }

  // Check Leaflet MarkerCluster
  // @ts-ignore
  if (L.markerClusterGroup) {
    console.log('Leaflet MarkerCluster is available');
  } else {
    console.warn('Leaflet MarkerCluster is not available');
  }

  // Check Leaflet Routing Machine
  // @ts-ignore
  if (L<PERSON>Routing) {
    console.log('Leaflet Routing Machine is available');
  } else {
    console.warn('Leaflet Routing Machine is not available');
  }
};

// Create a custom marker cluster icon factory
export const createClusterCustomIcon = (cluster: any) => {
  const markers = cluster.getAllChildMarkers();
  const count = cluster.getChildCount();

  // Determine cluster size class
  let size = 'small';
  if (count > 50) {
    size = 'large';
  } else if (count > 20) {
    size = 'medium';
  }

  // Create custom icon
  return L.divIcon({
    html: `<div><span>${count}</span></div>`,
    className: `marker-cluster marker-cluster-${size}`,
    iconSize: L.point(40, 40)
  });
};

// Function to create a GeoJSON layer with styling
export const createGeoJSONLayer = (geojson: any, options?: L.GeoJSONOptions) => {
  return L.geoJSON(geojson, {
    style: {
      color: '#3388ff',
      weight: 3,
      opacity: 0.7,
      fillOpacity: 0.2
    },
    pointToLayer: (feature, latlng) => {
      return L.circleMarker(latlng, {
        radius: 8,
        fillColor: '#3388ff',
        color: '#fff',
        weight: 1,
        opacity: 1,
        fillOpacity: 0.8
      });
    },
    ...options
  });
};

// Function to create a routing control
export const createRoutingControl = (waypoints: L.LatLng[], options?: any) => {
  try {
    // @ts-ignore
    if (typeof L.Routing?.control !== 'function') {
      console.warn('Routing control not available');
      return null;
    }

    // @ts-ignore
    return L.Routing.control({
      waypoints,
      routeWhileDragging: true,
      showAlternatives: true,
      altLineOptions: {
        styles: [
          { color: 'black', opacity: 0.15, weight: 9 },
          { color: 'white', opacity: 0.8, weight: 6 },
          { color: 'blue', opacity: 0.5, weight: 2 }
        ]
      },
      lineOptions: {
        styles: [
          { color: 'black', opacity: 0.15, weight: 9 },
          { color: 'white', opacity: 0.8, weight: 6 },
          { color: 'blue', opacity: 0.5, weight: 2 }
        ]
      },
      // @ts-ignore
      router: L.Routing?.osrmv1 ? L.Routing.osrmv1({
        serviceUrl: 'https://router.project-osrm.org/route/v1',
        profile: 'driving'
      }) : undefined,
      ...options
    });
  } catch (error) {
    console.warn('Failed to create routing control:', error);
    return null;
  }
};
