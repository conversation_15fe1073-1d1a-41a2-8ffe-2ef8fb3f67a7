import React from 'react';
import { format } from 'date-fns';
import {
  Clock,
  MapPin,
  User,
  Users,
  Tag,
  FileText,
  Link,
  Shield,
  Target,
  Clipboard,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { useResponseStore } from '@/store/responseStore';
import { useIncidentStore } from '@/store/incidentStore';
import { ActionType, ResponseStatus } from '@/types/incident';

interface ResponseViewProps {
  onClose: () => void;
  onEdit?: () => void;
}

const ResponseView: React.FC<ResponseViewProps> = ({ onClose, onEdit }) => {
  const { selectedResponse, deleteResponse } = useResponseStore();
  const { incidents, selectIncident } = useIncidentStore();

  if (!selectedResponse) {
    return null;
  }

  // Format the response type for display
  const formatResponseType = (type: ActionType): string => {
    return type.replace(/_/g, ' ');
  };

  // Get status color
  const getStatusColor = (status: ResponseStatus): string => {
    switch (status) {
      case ResponseStatus.PLANNED:
        return 'bg-military-amber';
      case ResponseStatus.IN_PROGRESS:
        return 'bg-military-blue';
      case ResponseStatus.COMPLETED:
        return 'bg-military-darkgreen';
      case ResponseStatus.CANCELLED:
        return 'bg-military-red';
      default:
        return 'bg-military-gray';
    }
  };

  // Get status icon
  const getStatusIcon = (status: ResponseStatus): React.ReactNode => {
    switch (status) {
      case ResponseStatus.PLANNED:
        return <Clipboard size={16} className="text-military-amber" />;
      case ResponseStatus.IN_PROGRESS:
        return <Clock size={16} className="text-military-blue" />;
      case ResponseStatus.COMPLETED:
        return <CheckCircle size={16} className="text-military-darkgreen" />;
      case ResponseStatus.CANCELLED:
        return <XCircle size={16} className="text-military-red" />;
      default:
        return <AlertTriangle size={16} className="text-military-gray" />;
    }
  };

  // Helper function to safely format dates
  const safeFormatDate = (dateValue: any): string => {
    if (!dateValue) return 'Not set';

    try {
      const date = new Date(dateValue);
      if (isNaN(date.getTime())) {
        console.warn(`Invalid date value: ${dateValue}`);
        return 'Invalid Date';
      }
      return format(date, 'dd MMM yyyy HH:mm');
    } catch (error) {
      console.warn(`Error formatting date: ${dateValue}`, error);
      return 'Invalid Date';
    }
  };

  // Format dates
  const formattedStartDate = safeFormatDate(selectedResponse.startDate);
  const formattedEndDate = selectedResponse.endDate
    ? safeFormatDate(selectedResponse.endDate)
    : 'Ongoing';

  // Get related incidents
  const relatedIncidents = selectedResponse.relatedIncidents
    ? incidents.filter(incident => selectedResponse.relatedIncidents?.includes(incident.id))
    : [];

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this response?')) {
      await deleteResponse(selectedResponse.id);
      onClose();
    }
  };

  const handleViewIncident = (id: string) => {
    selectIncident(id);
  };

  return (
    <div className="bg-military-panel border border-military-border max-w-4xl w-full max-h-[90vh] flex flex-col"
         style={{ clipPath: 'polygon(0 0, calc(100% - 12px) 0, 100% 12px, 100% 100%, 12px 100%, 0 calc(100% - 12px))' }}>
      <div className="flex items-center justify-between px-6 py-4 bg-military-navy border-b border-military-border">
        <h2 className="text-lg font-military-heading text-military-white uppercase tracking-wider">
          TACTICAL RESPONSE DETAILS
        </h2>
        <div className="flex space-x-1">
          {onEdit && (
            <Button
              size="sm"
              variant="military"
              onClick={onEdit}
              className="military-btn"
            >
              EDIT
            </Button>
          )}
          <Button
            size="sm"
            variant="military"
            onClick={handleDelete}
            className="military-btn bg-military-red"
          >
            DELETE
          </Button>
          <Button
            size="sm"
            variant="military"
            onClick={onClose}
            className="military-btn"
          >
            <XCircle size={18} />
          </Button>
        </div>
      </div>

      <div className="flex-grow overflow-y-auto p-6 bg-military-black">
        <div className="space-y-6">
          {/* Response title and badges */}
          <div>
            <h3 className="text-lg font-military-heading text-military-white uppercase">{selectedResponse.title}</h3>
            <div className="flex flex-wrap gap-2 mt-2">
              <Badge
                variant="military"
                label={formatResponseType(selectedResponse.type)}
                color="primary"
                className="bg-military-blue"
              />
              <Badge
                variant="military"
                label={selectedResponse.status}
                color="secondary"
                className={getStatusColor(selectedResponse.status)}
              />
              {selectedResponse.tags?.map(tag => (
                <Badge key={tag} variant="military" label={tag} color="default" />
              ))}
            </div>
          </div>

          {/* Description */}
          <div className="bg-military-navy border border-military-border p-4"
               style={{ clipPath: 'polygon(0 0, calc(100% - 8px) 0, 100% 8px, 100% 100%, 8px 100%, 0 calc(100% - 8px))' }}>
            <p className="text-military-white font-military-body">{selectedResponse.description}</p>
          </div>

          {/* Details grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Timing information */}
            <div className="bg-military-navy border border-military-border p-3"
                 style={{ clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' }}>
              <div className="flex items-start">
                <Clock size={18} className="text-military-accent mt-0.5 mr-2" />
                <div>
                  <p className="text-sm font-military-heading text-military-white uppercase">TIMING</p>

                  <div className="mt-2">
                    <p className="text-xs font-military-heading text-military-white uppercase">START DATE</p>
                    <p className="text-sm font-military-body text-military-white">{formattedStartDate}</p>
                  </div>

                  <div className="mt-2">
                    <p className="text-xs font-military-heading text-military-white uppercase">END DATE</p>
                    <p className="text-sm font-military-body text-military-white">{formattedEndDate}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Location information */}
            <div className="bg-military-navy border border-military-border p-3"
                 style={{ clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' }}>
              <div className="flex items-start">
                <MapPin size={18} className="text-military-accent mt-0.5 mr-2" />
                <div>
                  <p className="text-sm font-military-heading text-military-white uppercase">LOCATION</p>
                  <p className="text-sm font-military-body text-military-white">{selectedResponse.address}</p>

                  <div className="mt-2">
                    <p className="text-xs font-military-heading text-military-white uppercase">COORDINATES</p>
                    <p className="text-sm font-military-body text-military-white">
                      {selectedResponse.location.latitude.toFixed(6)}, {selectedResponse.location.longitude.toFixed(6)}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Personnel information */}
            <div className="bg-military-navy border border-military-border p-3"
                 style={{ clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' }}>
              <div className="flex items-start">
                <User size={18} className="text-military-accent mt-0.5 mr-2" />
                <div>
                  <p className="text-sm font-military-heading text-military-white uppercase">PERSONNEL</p>

                  <div className="mt-2">
                    <p className="text-xs font-military-heading text-military-white uppercase">COMMANDER</p>
                    <p className="text-sm font-military-body text-military-white">{selectedResponse.commander}</p>
                  </div>

                  {selectedResponse.personnelCount !== undefined && (
                    <div className="mt-2">
                      <p className="text-xs font-military-heading text-military-white uppercase">PERSONNEL COUNT</p>
                      <p className="text-sm font-military-body text-military-white">{selectedResponse.personnelCount}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Units information */}
            <div className="bg-military-navy border border-military-border p-3"
                 style={{ clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' }}>
              <div className="flex items-start">
                <Users size={18} className="text-military-accent mt-0.5 mr-2" />
                <div>
                  <p className="text-sm font-military-heading text-military-white uppercase">UNITS</p>
                  {selectedResponse.units && selectedResponse.units.length > 0 ? (
                    <ul className="mt-1 list-disc list-inside">
                      {selectedResponse.units.map((unit, index) => (
                        <li key={index} className="text-sm font-military-body text-military-white">{unit}</li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm font-military-body text-military-white mt-1">No units assigned</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Mission objectives */}
          {selectedResponse.objectives && selectedResponse.objectives.length > 0 && (
            <div className="bg-military-navy border border-military-border p-3"
                 style={{ clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' }}>
              <div className="flex items-start">
                <Target size={18} className="text-military-accent mt-0.5 mr-2" />
                <div>
                  <p className="text-sm font-military-heading text-military-white uppercase">OBJECTIVES</p>
                  <ul className="mt-1 list-disc list-inside">
                    {selectedResponse.objectives.map((objective, index) => (
                      <li key={index} className="text-sm font-military-body text-military-white">{objective}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Equipment used */}
          {selectedResponse.equipmentUsed && selectedResponse.equipmentUsed.length > 0 && (
            <div className="bg-military-navy border border-military-border p-3"
                 style={{ clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' }}>
              <div className="flex items-start">
                <Shield size={18} className="text-military-accent mt-0.5 mr-2" />
                <div>
                  <p className="text-sm font-military-heading text-military-white uppercase">EQUIPMENT</p>
                  <ul className="mt-1 list-disc list-inside">
                    {selectedResponse.equipmentUsed.map((equipment, index) => (
                      <li key={index} className="text-sm font-military-body text-military-white">{equipment}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Results section - only shown for completed or in-progress responses */}
          {(selectedResponse.status === ResponseStatus.COMPLETED || selectedResponse.status === ResponseStatus.IN_PROGRESS) && (
            <div className="bg-military-navy border border-military-border p-3"
                 style={{ clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' }}>
              <div className="flex items-start">
                <CheckCircle size={18} className="text-military-accent mt-0.5 mr-2" />
                <div>
                  <p className="text-sm font-military-heading text-military-white uppercase">RESULTS</p>

                  {selectedResponse.outcome && (
                    <div className="mt-2">
                      <p className="text-xs font-military-heading text-military-white uppercase">OUTCOME</p>
                      <p className="text-sm font-military-body text-military-white">{selectedResponse.outcome}</p>
                    </div>
                  )}

                  <div className="grid grid-cols-2 gap-4 mt-2">
                    {selectedResponse.casualties !== undefined && (
                      <div>
                        <p className="text-xs font-military-heading text-military-white uppercase">CASUALTIES</p>
                        <p className="text-sm font-military-body text-military-white">{selectedResponse.casualties}</p>
                      </div>
                    )}

                    {selectedResponse.successRate !== undefined && (
                      <div>
                        <p className="text-xs font-military-heading text-military-white uppercase">SUCCESS RATE</p>
                        <p className="text-sm font-military-body text-military-white">{selectedResponse.successRate}%</p>
                      </div>
                    )}
                  </div>

                  {selectedResponse.afterActionReport && (
                    <div className="mt-2">
                      <p className="text-xs font-military-heading text-military-white uppercase">AFTER ACTION REPORT</p>
                      <p className="text-sm font-military-body text-military-white whitespace-pre-wrap">{selectedResponse.afterActionReport}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Related incidents */}
          {relatedIncidents.length > 0 && (
            <div className="bg-military-navy border border-military-border p-3"
                 style={{ clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' }}>
              <div className="flex items-start">
                <Link size={18} className="text-military-accent mt-0.5 mr-2" />
                <div>
                  <p className="text-sm font-military-heading text-military-white uppercase">RELATED INCIDENTS</p>
                  <ul className="mt-1">
                    {relatedIncidents.map(incident => (
                      <li key={incident.id} className="mb-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewIncident(incident.id)}
                          className="text-left text-sm font-military-body text-military-white hover:text-military-accent"
                        >
                          {incident.id}: {incident.title}
                        </Button>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Notes */}
          {selectedResponse.notes && (
            <div className="bg-military-navy border border-military-border p-3"
                 style={{ clipPath: 'polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px))' }}>
              <div className="flex items-start">
                <FileText size={18} className="text-military-accent mt-0.5 mr-2" />
                <div>
                  <p className="text-sm font-military-heading text-military-white uppercase">NOTES</p>
                  <p className="text-sm font-military-body text-military-white mt-1 whitespace-pre-wrap">{selectedResponse.notes}</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ResponseView;
