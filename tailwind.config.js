/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        // Military theme colors
        military: {
          black: '#1A1A1A',      // Tactical black (primary background)
          darkgreen: '#4A5D23',  // Dark olive green (secondary background)
          navy: '#1C2541',       // Navy blue (tertiary elements)
          amber: '#FFBF00',      // Amber yellow (warning elements)
          red: '#C1121F',        // Signal red (alert/critical elements)
          white: '#F5F5F5',      // Off-white (text)
          border: '#5E8E3E',     // Border highlight color
          panel: '#2A3A2A',      // Panel background
          accent: '#7FAE5E',     // Accent color
        },
      },
      fontFamily: {
        'military-heading': ['var(--font-military-heading)'],
        'military-body': ['var(--font-military-body)'],
        'military-stencil': ['var(--font-military-stencil)'],
      },
      borderRadius: {
        'military': '0',
      },
    },
  },
  plugins: [],
};
