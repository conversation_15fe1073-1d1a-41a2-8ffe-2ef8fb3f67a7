import React from 'react';
import { Bar, <PERSON>, Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ChartData
} from 'chart.js';
import { format } from 'date-fns';
import Card from '@/components/ui/Card';
import { useResponseStore } from '@/store/responseStore';
import { ActionType as ResponseType, ResponseStatus } from '@/types/incident';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

// Chart options
const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
      labels: {
        boxWidth: 12,
        usePointStyle: true,
        color: '#F5F5F5',
        font: {
          family: "'Roboto Mono', 'Courier New', monospace",
          size: 10
        }
      },
    },
    title: {
      color: '#F5F5F5',
      font: {
        family: "'Roboto Mono', 'Courier New', monospace",
        size: 12,
        weight: 'bold'
      }
    }
  },
  scales: {
    x: {
      ticks: {
        color: '#F5F5F5',
        font: {
          family: "'Roboto Mono', 'Courier New', monospace",
          size: 10
        }
      },
      grid: {
        color: 'rgba(28, 37, 65, 0.2)'
      }
    },
    y: {
      ticks: {
        color: '#F5F5F5',
        font: {
          family: "'Roboto Mono', 'Courier New', monospace",
          size: 10
        }
      },
      grid: {
        color: 'rgba(28, 37, 65, 0.2)'
      }
    }
  }
};

// Response type colors - using ActionType values
const responseTypeColors: Record<ResponseType, string> = {
  // Area operations
  [ResponseType.ADO]: 'rgba(0, 48, 143, 0.8)',  // Blue
  [ResponseType.ASO]: 'rgba(0, 74, 143, 0.8)',  // Blue variant
  [ResponseType.IBO]: 'rgba(0, 100, 143, 0.8)', // Blue variant

  // Search operations
  [ResponseType.SEARCH_OPS]: 'rgba(74, 93, 35, 0.8)',  // Olive green
  [ResponseType.SEARCH_AND_CLEARANCE]: 'rgba(84, 103, 45, 0.8)', // Olive green variant
  [ResponseType.COMPOUND_SEARCH]: 'rgba(94, 113, 55, 0.8)', // Olive green variant
  [ResponseType.CARDON_AND_SEARCH]: 'rgba(104, 123, 65, 0.8)', // Olive green variant

  // Route operations
  [ResponseType.ROUTE_CLEARANCE]: 'rgba(255, 191, 0, 0.8)', // Amber
  [ResponseType.ROUTE_PICQUETTING]: 'rgba(245, 181, 0, 0.8)', // Amber variant
  [ResponseType.ROUTE_PATROLLING]: 'rgba(235, 171, 0, 0.8)', // Amber variant
  [ResponseType.ROUTE_RECCE]: 'rgba(225, 161, 0, 0.8)', // Amber variant
  [ResponseType.ROUTE_BD]: 'rgba(215, 151, 0, 0.8)', // Amber variant

  // Information operations
  [ResponseType.IO_CAMPAIGN]: 'rgba(0, 146, 143, 0.8)', // Teal
  [ResponseType.CIMIC]: 'rgba(0, 136, 133, 0.8)', // Teal variant
  [ResponseType.QIPS]: 'rgba(0, 126, 123, 0.8)', // Teal variant

  // Air operations
  [ResponseType.AIR_OPS]: 'rgba(193, 18, 31, 0.8)', // Red
  [ResponseType.DRONE_STRIKES]: 'rgba(183, 8, 21, 0.8)', // Red variant
  [ResponseType.ISR_MISSIONS]: 'rgba(173, 0, 11, 0.8)', // Red variant

  // Rescue operations
  [ResponseType.RESCUE_OPS]: 'rgba(255, 102, 0, 0.8)', // Orange
  [ResponseType.HOSTAGE_RESCUE]: 'rgba(245, 92, 0, 0.8)', // Orange variant

  // Clearance operations
  [ResponseType.LVL1_SE_CLEARANCE]: 'rgba(128, 0, 128, 0.8)', // Purple
  [ResponseType.LVL2_SE_CLEARANCE]: 'rgba(118, 0, 118, 0.8)', // Purple variant
  [ResponseType.LVL3_SE_CLEARANCE]: 'rgba(108, 0, 108, 0.8)', // Purple variant
  [ResponseType.TECH_SWEEP]: 'rgba(98, 0, 98, 0.8)', // Purple variant

  // Other
  [ResponseType.NONE]: 'rgba(128, 128, 128, 0.8)', // Gray
};

const ResponseStatisticsPanel: React.FC = () => {
  const { filteredResponses } = useResponseStore();

  if (!filteredResponses || filteredResponses.length === 0) {
    return (
      <Card title="RESPONSE STATISTICS" variant="military" className="h-full">
        <div className="flex items-center justify-center h-64">
          <p className="text-military-white font-military-body">NO RESPONSE DATA AVAILABLE</p>
        </div>
      </Card>
    );
  }

  // Calculate statistics
  const statistics = {
    total: filteredResponses.length,
    byType: {} as Record<ResponseType, number>,
    byStatus: {} as Record<ResponseStatus, number>,
    byDay: [] as { date: string; count: number }[]
  };

  // Initialize counters
  Object.values(ResponseType).forEach(type => {
    statistics.byType[type] = 0;
  });
  Object.values(ResponseStatus).forEach(status => {
    statistics.byStatus[status] = 0;
  });

  // Process responses
  filteredResponses.forEach(response => {
    // Count by type
    statistics.byType[response.type]++;

    // Count by status
    statistics.byStatus[response.status]++;
  });

  // Helper function to safely parse dates
  const safeParseDate = (dateValue: any): Date | null => {
    if (!dateValue) return null;

    try {
      const date = new Date(dateValue);
      // Check if date is valid
      if (isNaN(date.getTime())) {
        console.warn(`Invalid date value: ${dateValue}`);
        return null;
      }
      return date;
    } catch (error) {
      console.warn(`Error parsing date: ${dateValue}`, error);
      return null;
    }
  };

  // Group by day for timeline
  const dateMap = new Map<string, number>();
  filteredResponses.forEach(response => {
    try {
      const parsedDate = safeParseDate(response.startDate);
      if (parsedDate) {
        const date = format(parsedDate, 'yyyy-MM-dd');
        dateMap.set(date, (dateMap.get(date) || 0) + 1);
      }
    } catch (error) {
      console.error('Error processing response date:', error);
      // Skip this response for the timeline
    }
  });

  // Convert to array and sort by date
  statistics.byDay = Array.from(dateMap.entries())
    .map(([date, count]) => ({ date, count }))
    .sort((a, b) => a.date.localeCompare(b.date));

  // Prepare data for response types pie chart
  const typeData: ChartData<'pie'> = {
    labels: Object.keys(statistics.byType).filter(type => statistics.byType[type as ResponseType] > 0),
    datasets: [
      {
        data: Object.entries(statistics.byType)
          .filter(([_, count]) => count > 0)
          .map(([_, count]) => count),
        backgroundColor: Object.entries(statistics.byType)
          .filter(([_, count]) => count > 0)
          .map(([type]) => responseTypeColors[type as ResponseType]),
        borderWidth: 1,
      },
    ],
  };

  // Prepare data for response status bar chart
  const statusData: ChartData<'bar'> = {
    labels: Object.keys(statistics.byStatus),
    datasets: [
      {
        label: 'RESPONSES BY STATUS',
        data: Object.values(statistics.byStatus),
        backgroundColor: [
          'rgba(255, 191, 0, 0.8)', // PLANNED - amber
          'rgba(0, 48, 143, 0.8)',  // IN_PROGRESS - blue
          'rgba(74, 93, 35, 0.8)',  // COMPLETED - olive green
          'rgba(193, 18, 31, 0.8)', // CANCELLED - signal red
        ],
        borderColor: [
          'rgba(255, 191, 0, 1)',
          'rgba(0, 48, 143, 1)',
          'rgba(74, 93, 35, 1)',
          'rgba(193, 18, 31, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // Prepare data for responses timeline
  const timelineData: ChartData<'line'> = {
    labels: statistics.byDay.slice(-30).map(item => item.date), // Last 30 days
    datasets: [
      {
        label: 'DAILY RESPONSES',
        data: statistics.byDay.slice(-30).map(item => item.count),
        fill: true,
        backgroundColor: 'rgba(28, 37, 65, 0.2)',
        borderColor: 'rgba(0, 48, 143, 1)',
        tension: 0.4,
        pointBackgroundColor: 'rgba(0, 48, 143, 1)',
        pointBorderColor: '#F5F5F5',
        pointRadius: 4,
        pointHoverRadius: 6,
      },
    ],
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <Card title="RESPONSE TYPES" variant="military" className="h-full military-chart">
        <div className="h-64">
          <Pie data={typeData} options={chartOptions} />
        </div>
      </Card>

      <Card title="STATUS ANALYSIS" variant="military" className="h-full military-chart">
        <div className="h-64">
          <Bar data={statusData} options={chartOptions} />
        </div>
      </Card>

      <Card title="RESPONSE TIMELINE" variant="military" className="md:col-span-2 military-chart">
        <div className="h-64">
          <Line data={timelineData} options={chartOptions} />
        </div>
      </Card>

      <Card title="RESPONSE METRICS" variant="military" className="md:col-span-2">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="military-container bg-military-navy p-3">
            <h3 className="text-military-white text-xs font-military-body uppercase tracking-wider">TOTAL RESPONSES</h3>
            <p className="text-2xl font-military-heading text-military-white mt-2">{statistics.total}</p>
            <div className="h-1 w-full bg-military-border mt-2"></div>
          </div>

          <div className="military-container bg-military-darkgreen p-3">
            <h3 className="text-military-white text-xs font-military-body uppercase tracking-wider">COMPLETED</h3>
            <p className="text-2xl font-military-heading text-military-white mt-2">{statistics.byStatus[ResponseStatus.COMPLETED]}</p>
            <div className="h-1 w-full bg-military-border mt-2"></div>
          </div>

          <div className="military-container bg-military-panel p-3">
            <h3 className="text-military-amber text-xs font-military-body uppercase tracking-wider">IN PROGRESS</h3>
            <p className="text-2xl font-military-heading text-military-white mt-2">{statistics.byStatus[ResponseStatus.IN_PROGRESS]}</p>
            <div className="h-1 w-full bg-military-amber mt-2"></div>
          </div>

          <div className="military-container bg-military-panel p-3">
            <h3 className="text-military-red text-xs font-military-body uppercase tracking-wider">CANCELLED</h3>
            <p className="text-2xl font-military-heading text-military-white mt-2">{statistics.byStatus[ResponseStatus.CANCELLED] || 0}</p>
            <div className="h-1 w-full bg-military-red mt-2"></div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ResponseStatisticsPanel;
