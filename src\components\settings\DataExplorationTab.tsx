import React, { useState, useEffect } from 'react';
import { Table, Edit, Save, X, Search, Filter, RefreshCw, MapPin } from 'lucide-react';
import Button from '@/components/ui/Button';
import { Incident, Response, IncidentType, IncidentSeverity, IncidentStatus, ActionType, ResponseStatus } from '@/types/incident';
import { useIncidentStore } from '@/store/incidentStore';
import { useResponseStore } from '@/store/responseStore';
import { db } from '@/services/db/database';

interface DataExplorationTabProps {
  incidents: Incident[];
  responses: Response[];
}

type DataType = 'incidents' | 'responses';

interface EditableCell {
  id: string;
  field: string;
  value: any;
  rowIndex: number;
}

const DataExplorationTab: React.FC<DataExplorationTabProps> = ({ incidents, responses }) => {
  const [activeDataType, setActiveDataType] = useState<DataType>('incidents');
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredData, setFilteredData] = useState<Incident[] | Response[]>([]);
  const [editingCell, setEditingCell] = useState<EditableCell | null>(null);
  const [editValue, setEditValue] = useState<string>('');
  const { loadIncidents, updateIncident } = useIncidentStore();
  const { loadResponses, updateResponse } = useResponseStore();

  // Filter data based on search term
  useEffect(() => {
    if (activeDataType === 'incidents') {
      setFilteredData(
        incidents.filter(incident => 
          incident.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          incident.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          incident.address.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    } else {
      setFilteredData(
        responses.filter(response => 
          response.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          response.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          response.address.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }
  }, [searchTerm, incidents, responses, activeDataType]);

  // Initialize filtered data
  useEffect(() => {
    setFilteredData(activeDataType === 'incidents' ? incidents : responses);
  }, [activeDataType, incidents, responses]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Search is already handled by the useEffect
  };

  const handleCellClick = (id: string, field: string, value: any, rowIndex: number) => {
    // Don't allow editing of ID field
    if (field === 'id') return;
    
    // Don't allow editing of complex objects directly
    if (typeof value === 'object' && value !== null) {
      if (field === 'location') {
        // For location, we'll edit latitude and longitude separately
        return;
      }
      return;
    }

    setEditingCell({ id, field, value, rowIndex });
    setEditValue(value !== null && value !== undefined ? String(value) : '');
  };

  const handleSaveEdit = async () => {
    if (!editingCell) return;

    try {
      const { id, field, rowIndex } = editingCell;
      let parsedValue: any = editValue;

      // Parse value based on field type
      if (field === 'latitude' || field === 'longitude') {
        parsedValue = parseFloat(editValue);
        if (isNaN(parsedValue)) {
          alert('Please enter a valid number for coordinates');
          return;
        }
      } else if (field === 'reportedAt' || field === 'resolvedAt' || field === 'startDate' || field === 'endDate') {
        parsedValue = new Date(editValue);
        if (isNaN(parsedValue.getTime())) {
          alert('Please enter a valid date');
          return;
        }
      }

      if (activeDataType === 'incidents') {
        const incident = { ...filteredData[rowIndex] as Incident };
        
        // Handle special fields
        if (field === 'latitude') {
          incident.location = { ...incident.location, latitude: parsedValue };
        } else if (field === 'longitude') {
          incident.location = { ...incident.location, longitude: parsedValue };
        } else {
          // @ts-ignore - dynamic field assignment
          incident[field] = parsedValue;
        }
        
        await db.incidents.update(id, incident);
        await loadIncidents();
      } else {
        const response = { ...filteredData[rowIndex] as Response };
        
        // Handle special fields
        if (field === 'latitude') {
          response.location = { ...response.location, latitude: parsedValue };
        } else if (field === 'longitude') {
          response.location = { ...response.location, longitude: parsedValue };
        } else {
          // @ts-ignore - dynamic field assignment
          response[field] = parsedValue;
        }
        
        await db.responses.update(id, response);
        await loadResponses();
      }

      setEditingCell(null);
    } catch (error) {
      console.error('Failed to save edit:', error);
      alert('Failed to save edit. Please try again.');
    }
  };

  const handleCancelEdit = () => {
    setEditingCell(null);
  };

  const renderEditableCell = (id: string, field: string, value: any, rowIndex: number) => {
    const isEditing = editingCell && editingCell.id === id && editingCell.field === field;

    // Format display value
    let displayValue = value;
    if (value instanceof Date) {
      displayValue = value.toISOString().split('T')[0];
    } else if (field === 'location') {
      displayValue = `${value.latitude.toFixed(4)}, ${value.longitude.toFixed(4)}`;
    } else if (Array.isArray(value)) {
      displayValue = value.join(', ');
    } else if (value === null || value === undefined) {
      displayValue = '';
    }

    if (isEditing) {
      return (
        <div className="flex items-center">
          <input
            type="text"
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            className="bg-military-navy border border-military-border text-military-white font-military-body p-1 w-full"
            autoFocus
          />
          <div className="flex ml-1">
            <button onClick={handleSaveEdit} className="text-military-green mr-1">
              <Save size={14} />
            </button>
            <button onClick={handleCancelEdit} className="text-military-red">
              <X size={14} />
            </button>
          </div>
        </div>
      );
    }

    return (
      <div 
        className="cursor-pointer hover:bg-military-navy p-1 flex items-center justify-between"
        onClick={() => handleCellClick(id, field, value, rowIndex)}
      >
        <span className="truncate">{displayValue}</span>
        <Edit size={12} className="opacity-50 ml-1" />
      </div>
    );
  };

  const renderLocationCell = (location: { latitude: number; longitude: number }, id: string, rowIndex: number) => {
    return (
      <div className="flex flex-col">
        <div className="flex items-center justify-between">
          <span className="text-xs text-military-white opacity-70">Lat:</span>
          {renderEditableCell(id, 'latitude', location.latitude, rowIndex)}
        </div>
        <div className="flex items-center justify-between mt-1">
          <span className="text-xs text-military-white opacity-70">Lng:</span>
          {renderEditableCell(id, 'longitude', location.longitude, rowIndex)}
        </div>
      </div>
    );
  };

  const renderIncidentsTable = () => {
    return (
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-military-navy">
              <th className="p-2 text-left text-military-white font-military-body border border-military-border">ID</th>
              <th className="p-2 text-left text-military-white font-military-body border border-military-border">Title</th>
              <th className="p-2 text-left text-military-white font-military-body border border-military-border">Type</th>
              <th className="p-2 text-left text-military-white font-military-body border border-military-border">Severity</th>
              <th className="p-2 text-left text-military-white font-military-body border border-military-border">Status</th>
              <th className="p-2 text-left text-military-white font-military-body border border-military-border">Location</th>
              <th className="p-2 text-left text-military-white font-military-body border border-military-border">Address</th>
              <th className="p-2 text-left text-military-white font-military-body border border-military-border">Reported At</th>
            </tr>
          </thead>
          <tbody>
            {(filteredData as Incident[]).map((incident, index) => (
              <tr key={incident.id} className="border-b border-military-border hover:bg-military-navy">
                <td className="p-2 text-military-white font-military-body border border-military-border">{incident.id}</td>
                <td className="p-2 text-military-white font-military-body border border-military-border">
                  {renderEditableCell(incident.id, 'title', incident.title, index)}
                </td>
                <td className="p-2 text-military-white font-military-body border border-military-border">
                  {renderEditableCell(incident.id, 'type', incident.type, index)}
                </td>
                <td className="p-2 text-military-white font-military-body border border-military-border">
                  {renderEditableCell(incident.id, 'severity', incident.severity, index)}
                </td>
                <td className="p-2 text-military-white font-military-body border border-military-border">
                  {renderEditableCell(incident.id, 'status', incident.status, index)}
                </td>
                <td className="p-2 text-military-white font-military-body border border-military-border">
                  {renderLocationCell(incident.location, incident.id, index)}
                </td>
                <td className="p-2 text-military-white font-military-body border border-military-border">
                  {renderEditableCell(incident.id, 'address', incident.address, index)}
                </td>
                <td className="p-2 text-military-white font-military-body border border-military-border">
                  {renderEditableCell(incident.id, 'reportedAt', incident.reportedAt, index)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  const renderResponsesTable = () => {
    return (
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-military-navy">
              <th className="p-2 text-left text-military-white font-military-body border border-military-border">ID</th>
              <th className="p-2 text-left text-military-white font-military-body border border-military-border">Title</th>
              <th className="p-2 text-left text-military-white font-military-body border border-military-border">Type</th>
              <th className="p-2 text-left text-military-white font-military-body border border-military-border">Status</th>
              <th className="p-2 text-left text-military-white font-military-body border border-military-border">Location</th>
              <th className="p-2 text-left text-military-white font-military-body border border-military-border">Address</th>
              <th className="p-2 text-left text-military-white font-military-body border border-military-border">Start Date</th>
              <th className="p-2 text-left text-military-white font-military-body border border-military-border">Commander</th>
            </tr>
          </thead>
          <tbody>
            {(filteredData as Response[]).map((response, index) => (
              <tr key={response.id} className="border-b border-military-border hover:bg-military-navy">
                <td className="p-2 text-military-white font-military-body border border-military-border">{response.id}</td>
                <td className="p-2 text-military-white font-military-body border border-military-border">
                  {renderEditableCell(response.id, 'title', response.title, index)}
                </td>
                <td className="p-2 text-military-white font-military-body border border-military-border">
                  {renderEditableCell(response.id, 'type', response.type, index)}
                </td>
                <td className="p-2 text-military-white font-military-body border border-military-border">
                  {renderEditableCell(response.id, 'status', response.status, index)}
                </td>
                <td className="p-2 text-military-white font-military-body border border-military-border">
                  {renderLocationCell(response.location, response.id, index)}
                </td>
                <td className="p-2 text-military-white font-military-body border border-military-border">
                  {renderEditableCell(response.id, 'address', response.address, index)}
                </td>
                <td className="p-2 text-military-white font-military-body border border-military-border">
                  {renderEditableCell(response.id, 'startDate', response.startDate, index)}
                </td>
                <td className="p-2 text-military-white font-military-body border border-military-border">
                  {renderEditableCell(response.id, 'commander', response.commander, index)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div>
      <h3 className="text-lg font-military-heading text-military-white uppercase mb-6">DATA EXPLORATION</h3>
      <p className="text-military-white font-military-body mb-4">
        View and edit your data directly. Changes will automatically update on the map.
      </p>

      {/* Data type selector */}
      <div className="flex mb-4">
        <Button
          variant="military"
          className={`military-btn ${activeDataType === 'incidents' ? 'bg-military-darkgreen' : 'bg-military-navy'}`}
          onClick={() => setActiveDataType('incidents')}
        >
          INCIDENTS ({incidents.length})
        </Button>
        <Button
          variant="military"
          className={`military-btn ml-2 ${activeDataType === 'responses' ? 'bg-military-darkgreen' : 'bg-military-navy'}`}
          onClick={() => setActiveDataType('responses')}
        >
          RESPONSES ({responses.length})
        </Button>
      </div>

      {/* Search and filter */}
      <div className="mb-4 flex">
        <form onSubmit={handleSearch} className="flex-1 flex">
          <div className="relative flex-1">
            <input
              type="text"
              placeholder="Search by title, description, or address..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-military-navy border border-military-border text-military-white font-military-body p-2 pl-8"
              style={{ clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))' }}
            />
            <Search size={16} className="absolute left-2 top-2.5 text-military-white opacity-50" />
          </div>
          <Button
            type="submit"
            variant="military"
            className="military-btn bg-military-darkgreen ml-2"
          >
            SEARCH
          </Button>
        </form>
      </div>

      {/* Data table */}
      <div className="border border-military-border mb-4">
        {activeDataType === 'incidents' ? renderIncidentsTable() : renderResponsesTable()}
      </div>

      {/* Data count */}
      <div className="text-sm text-military-white opacity-70">
        Showing {filteredData.length} of {activeDataType === 'incidents' ? incidents.length : responses.length} {activeDataType}
      </div>
    </div>
  );
};

export default DataExplorationTab;
