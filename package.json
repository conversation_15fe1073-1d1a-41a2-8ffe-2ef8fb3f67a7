{"name": "incidents-analysis-dashboard", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@mapbox/mapbox-gl-draw": "^1.5.0", "@turf/boolean-point-in-polygon": "^7.2.0", "@turf/boolean-within": "^7.2.0", "@turf/helpers": "^7.2.0", "@turf/turf": "^6.5.0", "chart.js": "^4.4.1", "date-fns": "^3.3.1", "dexie": "^3.2.4", "leaflet": "^1.9.4", "leaflet-control-geocoder": "^3.1.0", "leaflet-draw": "^1.0.4", "leaflet-measure": "^3.1.0", "leaflet-minimap": "^3.6.1", "leaflet-routing-machine": "^3.2.12", "leaflet.fullscreen": "^4.0.0", "leaflet.heat": "^0.2.0", "leaflet.locatecontrol": "^0.84.2", "leaflet.markercluster": "^1.5.3", "lucide-react": "^0.344.0", "maplibre-gl": "^3.6.2", "pmtiles": "^2.11.0", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-leaflet": "^4.2.1", "react-router-dom": "^6.22.0", "recharts": "^2.15.3", "shpjs": "^6.1.0", "sql.js": "^1.8.0", "supercluster": "^8.0.1", "tokml": "^0.4.0", "xlsx": "^0.18.5", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/leaflet": "^1.9.8", "@types/leaflet-draw": "^1.0.12", "@types/leaflet.heat": "^0.2.3", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/sql.js": "^1.4.9", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vite-plugin-node-polyfills": "^0.21.0"}}