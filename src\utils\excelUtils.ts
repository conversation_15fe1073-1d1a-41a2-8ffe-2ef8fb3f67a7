import * as XLSX from 'xlsx';
import { Incident, Response } from '@/types/incident';
import {
  detectCoordinateFormat,
  CoordinateFormat,
  parseCoordinate
} from './coordinateUtils';

/**
 * Enum for date formats that can be detected
 */
export enum DateFormat {
  ISO = 'ISO',               // YYYY-MM-DD
  US_SHORT = 'US_SHORT',     // MM/DD/YY
  US_FULL = 'US_FULL',       // MM/DD/YYYY
  EU_SHORT = 'EU_SHORT',     // DD/MM/YY
  EU_FULL = 'EU_FULL',       // DD/MM/YYYY
  DOT_SHORT = 'DOT_SHORT',   // DD.MM.YY
  DOT_FULL = 'DOT_FULL',     // DD.MM.YYYY
  DASH_SHORT = 'DASH_SHORT', // DD-MM-YY
  DASH_FULL = 'DASH_FULL',   // DD-MM-YYYY
  MONTH_TEXT = 'MONTH_TEXT', // DD Month YYYY
  EXCEL_DATE = 'EXCEL_DATE', // Excel date format (e.g., "01 Jan 45570 00:00")
  UNKNOWN = 'UNKNOWN'
}

/**
 * Interface for date conversion results
 */
export interface DateConversionResult {
  originalValue: string;
  convertedValue: Date | null;
  success: boolean;
  detectedFormat: DateFormat;
  error?: string;
}

/**
 * Detects the format of a date string
 * @param value The date string to check
 * @returns The detected date format
 */
export const detectDateFormat = (value: string): DateFormat => {
  if (!value || typeof value !== 'string') {
    return DateFormat.UNKNOWN;
  }

  // Clean the input
  const cleanValue = value.trim();

  // ISO format: YYYY-MM-DD
  if (/^\d{4}-\d{2}-\d{2}$/.test(cleanValue)) {
    return DateFormat.ISO;
  }

  // US format: MM/DD/YY
  if (/^\d{1,2}\/\d{1,2}\/\d{2}$/.test(cleanValue)) {
    return DateFormat.US_SHORT;
  }

  // US format: MM/DD/YYYY
  if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(cleanValue)) {
    return DateFormat.US_FULL;
  }

  // EU format with dots: DD.MM.YY
  if (/^\d{1,2}\.\d{1,2}\.\d{2}$/.test(cleanValue)) {
    return DateFormat.DOT_SHORT;
  }

  // EU format with dots: DD.MM.YYYY
  if (/^\d{1,2}\.\d{1,2}\.\d{4}$/.test(cleanValue)) {
    return DateFormat.DOT_FULL;
  }

  // Format with dashes: DD-MM-YY
  if (/^\d{1,2}-\d{1,2}-\d{2}$/.test(cleanValue)) {
    return DateFormat.DASH_SHORT;
  }

  // Format with dashes: DD-MM-YYYY
  if (/^\d{1,2}-\d{1,2}-\d{4}$/.test(cleanValue)) {
    return DateFormat.DASH_FULL;
  }

  // Text month format: DD Month YYYY
  if (/^\d{1,2}\s+[A-Za-z]+\s+\d{4}$/.test(cleanValue)) {
    return DateFormat.MONTH_TEXT;
  }

  // Excel problematic date format: DD Month YYYY HH:MM or similar with very large year
  if (/^\d{1,2}\s+[A-Za-z]+\s+\d{5,}/.test(cleanValue)) {
    return DateFormat.EXCEL_DATE;
  }

  return DateFormat.UNKNOWN;
};

/**
 * Converts a date string to a Date object based on its detected format
 * @param value The date string to convert
 * @returns An object containing the conversion result
 */
export const convertDateString = (value: string): DateConversionResult => {
  if (!value || typeof value !== 'string') {
    return {
      originalValue: value,
      convertedValue: null,
      success: false,
      detectedFormat: DateFormat.UNKNOWN,
      error: 'Invalid input: empty or not a string'
    };
  }

  const cleanValue = value.trim();
  const format = detectDateFormat(cleanValue);
  let convertedDate: Date | null = null;
  let error: string | undefined;

  try {
    switch (format) {
      case DateFormat.ISO:
        // Already in the correct format
        convertedDate = new Date(cleanValue);
        break;

      case DateFormat.US_SHORT: {
        // MM/DD/YY
        const [month, day, shortYear] = cleanValue.split('/');
        const year = parseInt(shortYear) < 50 ? `20${shortYear}` : `19${shortYear}`;
        convertedDate = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
        break;
      }

      case DateFormat.US_FULL: {
        // MM/DD/YYYY
        const [month, day, year] = cleanValue.split('/');
        convertedDate = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
        break;
      }

      case DateFormat.DOT_SHORT: {
        // DD.MM.YY
        const [day, month, shortYear] = cleanValue.split('.');
        const year = parseInt(shortYear) < 50 ? `20${shortYear}` : `19${shortYear}`;
        convertedDate = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
        break;
      }

      case DateFormat.DOT_FULL: {
        // DD.MM.YYYY
        const [day, month, year] = cleanValue.split('.');
        convertedDate = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
        break;
      }

      case DateFormat.DASH_SHORT: {
        // DD-MM-YY
        const [day, month, shortYear] = cleanValue.split('-');
        const year = parseInt(shortYear) < 50 ? `20${shortYear}` : `19${shortYear}`;
        convertedDate = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
        break;
      }

      case DateFormat.DASH_FULL: {
        // DD-MM-YYYY
        const [day, month, year] = cleanValue.split('-');
        convertedDate = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
        break;
      }

      case DateFormat.MONTH_TEXT:
        // DD Month YYYY - let JavaScript's Date parsing handle this
        convertedDate = new Date(cleanValue);
        break;

      case DateFormat.EXCEL_DATE:
        // Handle Excel's problematic date format (e.g., "01 Jan 45570 00:00")
        // This is likely a MM/DD/YYYY format that Excel misinterpreted
        // Extract the original date from the row data or use current date as fallback
        console.warn(`Detected problematic Excel date format: ${cleanValue}`);

        // Try to extract MM/DD/YYYY pattern if it exists in the original value
        const mmddyyyyMatch = value.match(/(\d{1,2})\/(\d{1,2})\/(\d{4})/);
        if (mmddyyyyMatch) {
          const [_, month, day, year] = mmddyyyyMatch;
          convertedDate = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
          console.log(`Extracted date from original value: ${month}/${day}/${year}`);
        } else {
          // If no pattern found, use current date
          convertedDate = new Date();
          console.warn(`Could not extract date from ${value}, using current date`);
        }
        break;

      default:
        // Try standard JavaScript date parsing as a fallback
        convertedDate = new Date(cleanValue);
        break;
    }

    // Check if the date is valid
    if (isNaN(convertedDate.getTime())) {
      throw new Error('Invalid date');
    }

    // Sanity check: Reject dates too far in the future (more than 100 years from now)
    const maxAllowedYear = new Date().getFullYear() + 100;
    if (convertedDate.getFullYear() > maxAllowedYear) {
      console.warn(`Date ${convertedDate.toISOString()} is too far in the future (year ${convertedDate.getFullYear()}). Likely a parsing error.`);

      // Try to extract MM/DD/YYYY pattern if it exists in the original value
      const mmddyyyyMatch = value.match(/(\d{1,2})\/(\d{1,2})\/(\d{4})/);
      if (mmddyyyyMatch) {
        const [_, month, day, year] = mmddyyyyMatch;
        convertedDate = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
        console.log(`Extracted date from original value: ${month}/${day}/${year}`);
      } else {
        // If no pattern found, use current date
        convertedDate = new Date();
        console.warn(`Could not extract date from ${value}, using current date`);
      }
    }
  } catch (err) {
    error = err instanceof Error ? err.message : 'Unknown error parsing date';
    convertedDate = null;
  }

  return {
    originalValue: value,
    convertedValue: convertedDate,
    success: convertedDate !== null,
    detectedFormat: format,
    error
  };
};

/**
 * Exports data to an Excel file and triggers download
 * @param data The data to export
 * @param fileName The name of the file to download
 */
export const exportToExcel = (data: any[], fileName: string): void => {
  try {
    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Convert data to worksheet
    const worksheet = XLSX.utils.json_to_sheet(data);

    // Add the worksheet to the workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Data');

    // Generate Excel file and trigger download
    XLSX.writeFile(workbook, fileName);
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    throw error;
  }
};

/**
 * Prepares incident data for Excel export
 * @param incidents Array of incidents to export
 * @returns Array of plain objects ready for Excel export
 */
export const prepareIncidentsForExport = (incidents: Incident[]): any[] => {
  return incidents.map(incident => ({
    id: incident.id,
    title: incident.title,
    description: incident.description,
    type: incident.type,
    action: incident.action || '',
    severity: incident.severity,
    status: incident.status,
    latitude: incident.location?.latitude || 0,
    longitude: incident.location?.longitude || 0,
    address: incident.address,
    reportedAt: incident.reportedAt instanceof Date
      ? incident.reportedAt.toISOString()
      : incident.reportedAt,
    resolvedAt: incident.resolvedAt instanceof Date
      ? incident.resolvedAt.toISOString()
      : incident.resolvedAt || '',
    reportedBy: incident.reportedBy,
    assignedTo: incident.assignedTo || '',
    tags: incident.tags ? incident.tags.join(';') : ''
  }));
};

/**
 * Prepares response data for Excel export
 * @param responses Array of responses to export
 * @returns Array of plain objects ready for Excel export
 */
export const prepareResponsesForExport = (responses: Response[]): any[] => {
  return responses.map(response => ({
    id: response.id,
    title: response.title,
    description: response.description,
    type: response.type,
    status: response.status,
    latitude: response.location?.latitude || 0,
    longitude: response.location?.longitude || 0,
    address: response.address,
    startDate: response.startDate instanceof Date
      ? response.startDate.toISOString()
      : response.startDate,
    endDate: response.endDate instanceof Date
      ? response.endDate.toISOString()
      : response.endDate || '',
    commander: response.commander,
    units: response.units ? response.units.join(';') : '',
    relatedIncidents: response.relatedIncidents ? response.relatedIncidents.join(';') : '',
    tags: response.tags ? response.tags.join(';') : '',
    notes: response.notes || '',
    equipmentUsed: response.equipmentUsed ? response.equipmentUsed.join(';') : '',
    personnelCount: response.personnelCount || 0,
    objectives: response.objectives ? response.objectives.join(';') : '',
    outcome: response.outcome || '',
    casualties: response.casualties || 0,
    successRate: response.successRate || 0,
    afterActionReport: response.afterActionReport || ''
  }));
};

/**
 * Parses Excel file data into incidents
 * @param data The raw file data
 * @returns Array of incident objects
 */
export const parseExcelToIncidents = (data: ArrayBuffer): Partial<Incident>[] => {
  try {
    // Read the Excel file
    const workbook = XLSX.read(data, { type: 'array' });

    // Get the first worksheet
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];

    // Convert worksheet to JSON
    const jsonData = XLSX.utils.sheet_to_json(worksheet);

    // Process each row into an incident
    return jsonData.map((row: any) => {
      // Parse coordinates, handling DMS format if present
      let latitude = 0;
      let longitude = 0;

      if (row.latitude) {
        const latValue = String(row.latitude);
        const latFormat = detectCoordinateFormat(latValue);

        if (latFormat === CoordinateFormat.DMS) {
          const parsedLat = parseCoordinate(latValue);
          if (parsedLat !== null) {
            latitude = parsedLat;
            console.log(`Converted DMS latitude: ${latValue} to decimal: ${latitude}`);
          } else {
            latitude = parseFloat(latValue) || 0;
          }
        } else {
          latitude = parseFloat(latValue) || 0;
        }
      }

      if (row.longitude) {
        const lngValue = String(row.longitude);
        const lngFormat = detectCoordinateFormat(lngValue);

        if (lngFormat === CoordinateFormat.DMS) {
          const parsedLng = parseCoordinate(lngValue);
          if (parsedLng !== null) {
            longitude = parsedLng;
            console.log(`Converted DMS longitude: ${lngValue} to decimal: ${longitude}`);
          } else {
            longitude = parseFloat(lngValue) || 0;
          }
        } else {
          longitude = parseFloat(lngValue) || 0;
        }
      }

      const incident: Partial<Incident> = {
        id: row.id || `INC-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        title: row.title || 'Untitled Incident',
        description: row.description || '',
        type: row.type || 'OTHER',
        action: row.action || undefined,
        severity: row.severity || 'MEDIUM',
        status: row.status || 'REPORTED',
        location: {
          latitude: latitude,
          longitude: longitude
        },
        address: row.address || '',
        reportedAt: row.reportedAt ? new Date(row.reportedAt) : new Date(),
        resolvedAt: row.resolvedAt ? new Date(row.resolvedAt) : undefined,
        reportedBy: row.reportedBy || '',
        assignedTo: row.assignedTo || undefined
      };

      // Handle tags if present
      if (row.tags) {
        incident.tags = row.tags.split(';').map((tag: string) => tag.trim()).filter(Boolean);
      }

      return incident;
    });
  } catch (error) {
    console.error('Error parsing Excel to incidents:', error);
    throw error;
  }
};

/**
 * Interface for date conversion warnings
 */
export interface DateConversionWarning {
  field: string;
  originalValue: string;
  convertedValue: string;
  detectedFormat: DateFormat;
}

/**
 * Parses Excel file data into responses
 * @param data The raw file data
 * @returns Array of response objects and any date conversion warnings
 */
export const parseExcelToResponses = (data: ArrayBuffer): { responses: Partial<Response>[]; dateWarnings: DateConversionWarning[] } => {
  try {
    // Read the Excel file
    const workbook = XLSX.read(data, { type: 'array' });

    // Get the first worksheet
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];

    // Convert worksheet to JSON
    const jsonData = XLSX.utils.sheet_to_json(worksheet);

    // Track date conversion warnings
    const dateWarnings: DateConversionWarning[] = [];

    // Process each row into a response
    const responses = jsonData.map((row: any) => {
      // Parse coordinates, handling DMS format if present
      let latitude = 0;
      let longitude = 0;

      if (row.latitude) {
        const latValue = String(row.latitude);
        const latFormat = detectCoordinateFormat(latValue);

        if (latFormat === CoordinateFormat.DMS) {
          const parsedLat = parseCoordinate(latValue);
          if (parsedLat !== null) {
            latitude = parsedLat;
            console.log(`Converted DMS latitude: ${latValue} to decimal: ${latitude}`);
          } else {
            latitude = parseFloat(latValue) || 0;
          }
        } else {
          latitude = parseFloat(latValue) || 0;
        }
      }

      if (row.longitude) {
        const lngValue = String(row.longitude);
        const lngFormat = detectCoordinateFormat(lngValue);

        if (lngFormat === CoordinateFormat.DMS) {
          const parsedLng = parseCoordinate(lngValue);
          if (parsedLng !== null) {
            longitude = parsedLng;
            console.log(`Converted DMS longitude: ${lngValue} to decimal: ${longitude}`);
          } else {
            longitude = parseFloat(lngValue) || 0;
          }
        } else {
          longitude = parseFloat(lngValue) || 0;
        }
      }

      // Handle date conversions with the new utility
      let startDate = new Date();
      if (row.startDate) {
        // Log the raw value for debugging
        console.log(`Raw startDate value from Excel: ${row.startDate} (type: ${typeof row.startDate})`);

        // Check if this is the problematic Excel date format
        const startDateStr = String(row.startDate);
        const isExcelProblemDate = /^\d{1,2}\s+[A-Za-z]+\s+\d{5,}/.test(startDateStr);

        // If it's the problematic format, try to extract the original date from the row
        if (isExcelProblemDate) {
          console.warn(`Detected problematic Excel date format for startDate: ${startDateStr}`);

          // Try to find the original date in the raw data
          // This assumes the original date might be stored in the Excel file's raw data
          const originalDateMatch = JSON.stringify(row).match(/(\d{1,2})\/(\d{1,2})\/(\d{4})/);
          if (originalDateMatch) {
            const [_, month, day, year] = originalDateMatch;
            startDate = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
            console.log(`Extracted startDate from raw data: ${month}/${day}/${year} -> ${startDate.toISOString()}`);

            dateWarnings.push({
              field: 'startDate',
              originalValue: startDateStr,
              convertedValue: startDate.toISOString().split('T')[0],
              detectedFormat: DateFormat.EXCEL_DATE
            });
          } else {
            // If we can't find the original date, use the current date
            startDate = new Date();
            console.warn(`Could not extract original startDate, using current date: ${startDate.toISOString()}`);

            dateWarnings.push({
              field: 'startDate',
              originalValue: startDateStr,
              convertedValue: startDate.toISOString().split('T')[0],
              detectedFormat: DateFormat.EXCEL_DATE
            });
          }
        } else {
          // Normal date conversion
          const startDateResult = convertDateString(startDateStr);
          if (startDateResult.success && startDateResult.convertedValue) {
            startDate = startDateResult.convertedValue;

            // Add warning if format was not ISO
            if (startDateResult.detectedFormat !== DateFormat.ISO) {
              dateWarnings.push({
                field: 'startDate',
                originalValue: startDateResult.originalValue,
                convertedValue: startDate.toISOString().split('T')[0],
                detectedFormat: startDateResult.detectedFormat
              });
            }
          } else {
            console.warn(`Failed to parse startDate: ${row.startDate}. Using current date instead.`);
          }
        }
      }

      // Handle end date conversion
      let endDate: Date | undefined = undefined;
      if (row.endDate) {
        // Log the raw value for debugging
        console.log(`Raw endDate value from Excel: ${row.endDate} (type: ${typeof row.endDate})`);

        // Check if this is the problematic Excel date format
        const endDateStr = String(row.endDate);
        const isExcelProblemDate = /^\d{1,2}\s+[A-Za-z]+\s+\d{5,}/.test(endDateStr);

        // If it's the problematic format, try to extract the original date from the row
        if (isExcelProblemDate) {
          console.warn(`Detected problematic Excel date format for endDate: ${endDateStr}`);

          // Try to find the original date in the raw data
          // This assumes the original date might be stored in the Excel file's raw data
          const originalDateMatch = JSON.stringify(row).match(/(\d{1,2})\/(\d{1,2})\/(\d{4})/g);
          if (originalDateMatch && originalDateMatch.length > 1) {
            // If we found multiple dates, use the second one for endDate
            const secondDate = originalDateMatch[1];
            const [month, day, year] = secondDate.split('/');
            endDate = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
            console.log(`Extracted endDate from raw data: ${secondDate} -> ${endDate.toISOString()}`);

            dateWarnings.push({
              field: 'endDate',
              originalValue: endDateStr,
              convertedValue: endDate.toISOString().split('T')[0],
              detectedFormat: DateFormat.EXCEL_DATE
            });
          } else {
            // If we can't find the original date, use the current date
            endDate = new Date();
            console.warn(`Could not extract original endDate, using current date: ${endDate.toISOString()}`);

            dateWarnings.push({
              field: 'endDate',
              originalValue: endDateStr,
              convertedValue: endDate.toISOString().split('T')[0],
              detectedFormat: DateFormat.EXCEL_DATE
            });
          }
        } else {
          // Normal date conversion
          const endDateResult = convertDateString(endDateStr);
          if (endDateResult.success && endDateResult.convertedValue) {
            endDate = endDateResult.convertedValue;

            // Add warning if format was not ISO
            if (endDateResult.detectedFormat !== DateFormat.ISO) {
              dateWarnings.push({
                field: 'endDate',
                originalValue: endDateResult.originalValue,
                convertedValue: endDate.toISOString().split('T')[0],
                detectedFormat: endDateResult.detectedFormat
              });
            }
          } else {
            console.warn(`Failed to parse endDate: ${row.endDate}. Using undefined instead.`);
          }
        }
      }

      const response: Partial<Response> = {
        id: row.id || `RESP-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        title: row.title || 'Untitled Response',
        description: row.description || '',
        type: row.type || 'NONE',
        status: row.status || 'PLANNED',
        location: {
          latitude: latitude,
          longitude: longitude
        },
        address: row.address || '',
        startDate: startDate,
        endDate: endDate,
        commander: row.commander || ''
      };

      // Handle array fields
      if (row.units) {
        response.units = row.units.split(';').map((unit: string) => unit.trim()).filter(Boolean);
      }

      if (row.relatedIncidents) {
        response.relatedIncidents = row.relatedIncidents.split(';').map((id: string) => id.trim()).filter(Boolean);
      }

      if (row.tags) {
        response.tags = row.tags.split(';').map((tag: string) => tag.trim()).filter(Boolean);
      }

      if (row.equipmentUsed) {
        response.equipmentUsed = row.equipmentUsed.split(';').map((item: string) => item.trim()).filter(Boolean);
      }

      if (row.objectives) {
        response.objectives = row.objectives.split(';').map((obj: string) => obj.trim()).filter(Boolean);
      }

      // Handle other optional fields
      if (row.notes) response.notes = row.notes;
      if (row.personnelCount) response.personnelCount = parseInt(row.personnelCount);
      if (row.outcome) response.outcome = row.outcome;
      if (row.casualties) response.casualties = parseInt(row.casualties);
      if (row.successRate) response.successRate = parseFloat(row.successRate);
      if (row.afterActionReport) response.afterActionReport = row.afterActionReport;

      return response;
    });

    return { responses, dateWarnings };
  } catch (error) {
    console.error('Error parsing Excel to responses:', error);
    throw error;
  }
};
