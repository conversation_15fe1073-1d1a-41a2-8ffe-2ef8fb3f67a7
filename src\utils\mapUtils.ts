import { Coordinates } from '@/types/incident';

/**
 * Validates if the given coordinates are valid for use in a map
 * @param coordinates The coordinates to validate
 * @returns True if coordinates are valid, false otherwise
 */
export const isValidCoordinates = (coordinates: Coordinates | undefined): boolean => {
  if (!coordinates) return false;
  
  const { latitude, longitude } = coordinates;
  
  // Check if latitude and longitude are valid numbers
  if (typeof latitude !== 'number' || typeof longitude !== 'number') return false;
  if (isNaN(latitude) || isNaN(longitude)) return false;
  
  // Check if latitude is within valid range (-90 to 90)
  if (latitude < -90 || latitude > 90) return false;
  
  // Check if longitude is within valid range (-180 to 180)
  if (longitude < -180 || longitude > 180) return false;
  
  return true;
};

/**
 * Gets default coordinates for use when invalid coordinates are encountered
 * @returns Default coordinates (centered on Pakistan)
 */
export const getDefaultCoordinates = (): Coordinates => {
  return { latitude: 30.3753, longitude: 69.3451 };
};

/**
 * Safely gets coordinates, returning default coordinates if the input is invalid
 * @param coordinates The coordinates to validate
 * @returns Valid coordinates (either the input or default coordinates)
 */
export const getSafeCoordinates = (coordinates: Coordinates | undefined): Coordinates => {
  if (isValidCoordinates(coordinates)) {
    return coordinates as Coordinates;
  }
  
  return getDefaultCoordinates();
};
