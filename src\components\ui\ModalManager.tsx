import React, { createContext, useContext, useState, ReactNode } from 'react';
import { X } from 'lucide-react';
import Button from './Button';

// Define modal types
export enum ModalType {
  NONE = 'NONE',
  INCIDENT_FORM = 'INCIDENT_FORM',
  INCIDENT_DETAILS = 'INCIDENT_DETAILS',
  CONFIRM_DELETE = 'CONFIRM_DELETE',
  ERROR = 'ERROR',
  INFO = 'INFO'
}

// Modal context interface
interface ModalContextType {
  modalType: ModalType;
  modalProps: any;
  openModal: (type: ModalType, props?: any) => void;
  closeModal: () => void;
}

// Create context with default values
const ModalContext = createContext<ModalContextType>({
  modalType: ModalType.NONE,
  modalProps: {},
  openModal: () => {},
  closeModal: () => {}
});

// Hook to use the modal context
export const useModal = () => useContext(ModalContext);

// Modal provider component
interface ModalProviderProps {
  children: ReactNode;
}

export const ModalProvider: React.FC<ModalProviderProps> = ({ children }) => {
  const [modalType, setModalType] = useState<ModalType>(ModalType.NONE);
  const [modalProps, setModalProps] = useState<any>({});

  // Open a modal with optional props
  const openModal = (type: ModalType, props: any = {}) => {
    setModalType(type);
    setModalProps(props);
  };

  // Close the current modal
  const closeModal = () => {
    setModalType(ModalType.NONE);
    setModalProps({});
  };

  return (
    <ModalContext.Provider value={{ modalType, modalProps, openModal, closeModal }}>
      {children}
      <ModalRenderer />
    </ModalContext.Provider>
  );
};

// Modal renderer component
const ModalRenderer: React.FC = () => {
  const { modalType, modalProps, closeModal } = useModal();

  // If no modal is open, render nothing
  if (modalType === ModalType.NONE) {
    return null;
  }

  // Render the modal container
  return (
    <div className="fixed inset-0 z-[1000]">
      <div className="absolute inset-0 bg-military-black bg-opacity-80"></div>
      <div className="relative z-[1001] h-full w-full flex items-center justify-center p-4">
        {/* Render different modal content based on type */}
        {modalType === ModalType.ERROR && <ErrorModal {...modalProps} onClose={closeModal} />}
        {modalType === ModalType.INFO && <InfoModal {...modalProps} onClose={closeModal} />}
        {/* Other modal types would be rendered here */}
      </div>
    </div>
  );
};

// Error modal component
interface ErrorModalProps {
  title?: string;
  message: string;
  onClose: () => void;
}

const ErrorModal: React.FC<ErrorModalProps> = ({ 
  title = 'ERROR', 
  message, 
  onClose 
}) => {
  return (
    <div className="bg-military-panel border border-military-red max-w-md w-full"
         style={{ clipPath: 'polygon(0 0, calc(100% - 12px) 0, 100% 12px, 100% 100%, 12px 100%, 0 calc(100% - 12px))' }}>
      <div className="flex items-center justify-between px-6 py-4 bg-military-red border-b border-military-border">
        <h2 className="text-lg font-military-heading text-military-white uppercase tracking-wider">
          {title}
        </h2>
        <Button 
          variant="military" 
          size="sm" 
          onClick={onClose}
          className="military-btn"
        >
          <X size={18} />
        </Button>
      </div>
      <div className="p-6 bg-military-black">
        <p className="text-military-white font-military-body">{message}</p>
        <div className="flex justify-end mt-6">
          <Button 
            variant="military" 
            onClick={onClose}
            className="military-btn"
          >
            CLOSE
          </Button>
        </div>
      </div>
    </div>
  );
};

// Info modal component
interface InfoModalProps {
  title?: string;
  message: string;
  onClose: () => void;
}

const InfoModal: React.FC<InfoModalProps> = ({ 
  title = 'INFORMATION', 
  message, 
  onClose 
}) => {
  return (
    <div className="bg-military-panel border border-military-border max-w-md w-full"
         style={{ clipPath: 'polygon(0 0, calc(100% - 12px) 0, 100% 12px, 100% 100%, 12px 100%, 0 calc(100% - 12px))' }}>
      <div className="flex items-center justify-between px-6 py-4 bg-military-navy border-b border-military-border">
        <h2 className="text-lg font-military-heading text-military-white uppercase tracking-wider">
          {title}
        </h2>
        <Button 
          variant="military" 
          size="sm" 
          onClick={onClose}
          className="military-btn"
        >
          <X size={18} />
        </Button>
      </div>
      <div className="p-6 bg-military-black">
        <p className="text-military-white font-military-body">{message}</p>
        <div className="flex justify-end mt-6">
          <Button 
            variant="military" 
            onClick={onClose}
            className="military-btn bg-military-darkgreen"
          >
            ACKNOWLEDGE
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ModalProvider;
