import L from 'leaflet';
import 'leaflet-draw';

// Ensure Leaflet.Draw is properly initialized
export const initializeLeafletDraw = () => {
  // Check if Leaflet.Draw is available
  if (!L.Draw) {
    console.error('Leaflet.Draw is not available. Make sure it is properly imported.');
    return false;
  }

  // Patch any missing functionality if needed
  if (!L.drawVersion) {
    // Set a version to indicate our initialization has run
    L.drawVersion = '1.0.4';
    console.log('Leaflet.Draw initialized successfully');
  }

  return true;
};

// Create a draw control with military styling
export const createMilitaryDrawControl = (
  map: L.Map, 
  drawnItems: L.FeatureGroup,
  position: L.ControlPosition = 'topright'
) => {
  try {
    // Make sure Leaflet.Draw is initialized
    if (!initializeLeafletDraw()) {
      throw new Error('Failed to initialize Leaflet.Draw');
    }

    // Create draw options with military styling
    const drawOptions = {
      position: position,
      draw: {
        polyline: {
          shapeOptions: {
            color: '#5E8E3E',
            weight: 3
          }
        },
        polygon: {
          allowIntersection: false,
          drawError: {
            color: '#e1e100',
            message: '<strong>Error:</strong> shape edges cannot cross!'
          },
          shapeOptions: {
            color: '#5E8E3E'
          }
        },
        circle: {
          shapeOptions: {
            color: '#5E8E3E'
          }
        },
        rectangle: {
          shapeOptions: {
            color: '#5E8E3E'
          }
        },
        marker: true
      },
      edit: {
        featureGroup: drawnItems,
        remove: true
      }
    };

    // Create the draw control
    const drawControl = new L.Control.Draw(drawOptions);
    
    // Add the control to the map
    map.addControl(drawControl);
    
    // Store the draw control on the map object for access by other components
    // @ts-ignore
    map.drawControl = drawControl;
    
    // Set up event handlers
    map.on('draw:created', (e: any) => {
      const layer = e.layer;
      drawnItems.addLayer(layer);
    });
    
    return drawControl;
  } catch (error) {
    console.error('Error creating military draw control:', error);
    return null;
  }
};

// Enable a specific drawing mode
export const enableDrawingMode = (map: L.Map, drawingType: string) => {
  try {
    // @ts-ignore
    if (!map.drawControl) {
      console.error('Draw control not found on map');
      return false;
    }
    
    // @ts-ignore
    const drawToolbar = map.drawControl._toolbars.draw;
    if (!drawToolbar) {
      console.error('Draw toolbar not found');
      return false;
    }
    
    // Convert drawing type to lowercase for consistency
    const type = drawingType.toLowerCase();
    
    // Check if the drawing mode exists
    if (!drawToolbar._modes[type]) {
      console.error(`Drawing mode ${type} not available`);
      return false;
    }
    
    // Enable the drawing mode
    drawToolbar._modes[type].handler.enable();
    return true;
  } catch (error) {
    console.error('Error enabling drawing mode:', error);
    return false;
  }
};

// Enable edit mode for drawn items
export const enableEditMode = (map: L.Map) => {
  try {
    // @ts-ignore
    if (!map.drawControl) {
      console.error('Draw control not found on map');
      return false;
    }
    
    // @ts-ignore
    const editToolbar = map.drawControl._toolbars.edit;
    if (!editToolbar) {
      console.error('Edit toolbar not found');
      return false;
    }
    
    // Enable edit mode
    editToolbar._modes.edit.handler.enable();
    return true;
  } catch (error) {
    console.error('Error enabling edit mode:', error);
    return false;
  }
};
