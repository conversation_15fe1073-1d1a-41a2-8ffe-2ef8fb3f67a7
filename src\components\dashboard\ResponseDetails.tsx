import React, { useState } from 'react';
import ResponseView from './ResponseView';
import ResponseForm from './ResponseForm';
import { useResponseStore } from '@/store/responseStore';

interface ResponseDetailsProps {
  onClose: () => void;
  onEdit?: () => void;
}

const ResponseDetails: React.FC<ResponseDetailsProps> = ({ onClose, onEdit }) => {
  const { selectedResponse } = useResponseStore();
  const [isEditing, setIsEditing] = useState(false);

  if (!selectedResponse) {
    return null;
  }

  if (isEditing) {
    return (
      <ResponseForm
        response={selectedResponse}
        onClose={() => setIsEditing(false)}
      />
    );
  }

  return (
    <ResponseView
      onClose={onClose}
      onEdit={() => setIsEditing(true)}
    />
  );
};

export default ResponseDetails;
