/**
 * Utility to clean up NATO symbol settings from localStorage
 * This removes any NATO-related settings that may have been saved previously
 */

export const cleanupNATOSymbolSettings = () => {
  try {
    const savedSymbology = localStorage.getItem('symbology');
    if (savedSymbology) {
      const parsed = JSON.parse(savedSymbology);
      
      // Remove NATO-related properties
      if ('useNATOSymbols' in parsed) {
        delete parsed.useNATOSymbols;
        
        // Save the cleaned symbology back to localStorage
        localStorage.setItem('symbology', JSON.stringify(parsed));
        
        console.log('NATO symbol settings cleaned from localStorage');
        return true;
      }
    }
    return false;
  } catch (error) {
    console.error('Failed to cleanup NATO symbol settings:', error);
    return false;
  }
};

// Auto-cleanup on import (runs once when the module is loaded)
if (typeof window !== 'undefined') {
  cleanupNATOSymbolSettings();
}
