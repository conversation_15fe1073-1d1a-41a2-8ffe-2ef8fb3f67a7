import { format, parseISO, differenceInHours } from 'date-fns';
import { db } from '@/services/db/database';
import {
  Incident,
  IncidentType,
  IncidentSeverity,
  IncidentStatus,
  ActionType,
  IncidentStatistics,
  GeoStatistics,
  IncidentFilter,
  Coordinates
} from '@/types/incident';

// Import the custom filter type
import { ExtendedIncidentFilter, CustomFilterFn } from '@/store/incidentStore';

// Get filtered incidents based on criteria
export const getFilteredIncidents = async (filter: ExtendedIncidentFilter): Promise<Incident[]> => {
  let query = db.incidents.toCollection();

  if (filter.types && filter.types.length > 0) {
    query = query.filter(incident => filter.types!.includes(incident.type));
  }

  if (filter.actions && filter.actions.length > 0) {
    query = query.filter(incident => incident.action && filter.actions!.includes(incident.action));
  }

  if (filter.severities && filter.severities.length > 0) {
    query = query.filter(incident => filter.severities!.includes(incident.severity));
  }

  if (filter.statuses && filter.statuses.length > 0) {
    query = query.filter(incident => filter.statuses!.includes(incident.status));
  }

  if (filter.startDate) {
    query = query.filter(incident => incident.reportedAt >= filter.startDate!);
  }

  if (filter.endDate) {
    query = query.filter(incident => incident.reportedAt <= filter.endDate!);
  }

  if (filter.tags && filter.tags.length > 0) {
    query = query.filter(incident =>
      incident.tags && incident.tags.some(tag => filter.tags!.includes(tag))
    );
  }

  if (filter.searchTerm) {
    const term = filter.searchTerm.toLowerCase();
    query = query.filter(incident =>
      incident.title.toLowerCase().includes(term) ||
      incident.description.toLowerCase().includes(term) ||
      incident.address.toLowerCase().includes(term)
    );
  }

  // Get the filtered incidents
  let incidents = await query.toArray();

  // Apply custom filter if provided
  if (filter.customFilter) {
    incidents = incidents.filter(filter.customFilter);
  }

  return incidents;
};

// Generate statistics for incidents
export const generateIncidentStatistics = async (
  incidents: Incident[]
): Promise<IncidentStatistics> => {
  // Initialize counters
  const typeCounter: Record<string, number> = {};

  // Initialize with all incident types from the enum
  Object.values(IncidentType).forEach(type => {
    typeCounter[type] = 0;
  });

  const actionCounter: Record<ActionType, number> = {
    [ActionType.ADO]: 0,
    [ActionType.ASO]: 0,
    [ActionType.IBO]: 0,
    [ActionType.SEARCH_OPS]: 0,
    [ActionType.SEARCH_AND_CLEARANCE]: 0,
    [ActionType.COMPOUND_SEARCH]: 0,
    [ActionType.CARDON_AND_SEARCH]: 0,
    [ActionType.ROUTE_CLEARANCE]: 0,
    [ActionType.ROUTE_PICQUETTING]: 0,
    [ActionType.ROUTE_PATROLLING]: 0,
    [ActionType.ROUTE_RECCE]: 0,
    [ActionType.IO_CAMPAIGN]: 0,
    [ActionType.CIMIC]: 0,
    [ActionType.QIPS]: 0,
    [ActionType.AIR_OPS]: 0,
    [ActionType.DRONE_STRIKES]: 0,
    [ActionType.ISR_MISSIONS]: 0,
    [ActionType.RESCUE_OPS]: 0,
    [ActionType.HOSTAGE_RESCUE]: 0,
    [ActionType.ROUTE_BD]: 0,
    [ActionType.LVL1_SE_CLEARANCE]: 0,
    [ActionType.LVL2_SE_CLEARANCE]: 0,
    [ActionType.LVL3_SE_CLEARANCE]: 0,
    [ActionType.TECH_SWEEP]: 0,
    [ActionType.NONE]: 0
  };

  const severityCounter: Record<IncidentSeverity, number> = {
    [IncidentSeverity.LOW]: 0,
    [IncidentSeverity.MEDIUM]: 0,
    [IncidentSeverity.HIGH]: 0,
    [IncidentSeverity.CRITICAL]: 0
  };

  const statusCounter: Record<IncidentStatus, number> = {
    [IncidentStatus.REPORTED]: 0,
    [IncidentStatus.IN_PROGRESS]: 0,
    [IncidentStatus.RESOLVED]: 0,
    [IncidentStatus.CLOSED]: 0
  };

  // Date-based grouping
  const byDayMap = new Map<string, number>();
  const byMonthMap = new Map<string, number>();

  // Resolution time tracking
  let totalResolutionTime = 0;
  let resolvedCount = 0;

  // Process each incident
  incidents.forEach(incident => {
    // Count by type
    typeCounter[incident.type]++;

    // Count by action
    actionCounter[incident.action || ActionType.NONE]++;

    // Count by severity
    severityCounter[incident.severity]++;

    // Count by status
    statusCounter[incident.status]++;

    // Group by day
    const day = format(new Date(incident.reportedAt), 'yyyy-MM-dd');
    byDayMap.set(day, (byDayMap.get(day) || 0) + 1);

    // Group by month
    const month = format(new Date(incident.reportedAt), 'yyyy-MM');
    byMonthMap.set(month, (byMonthMap.get(month) || 0) + 1);

    // Calculate resolution time
    if (incident.resolvedAt &&
        (incident.status === IncidentStatus.RESOLVED ||
         incident.status === IncidentStatus.CLOSED)) {
      const resolutionTime = differenceInHours(
        new Date(incident.resolvedAt),
        new Date(incident.reportedAt)
      );
      totalResolutionTime += resolutionTime;
      resolvedCount++;
    }
  });

  // Convert day and month maps to sorted arrays
  const byDay = Array.from(byDayMap.entries())
    .map(([date, count]) => ({ date, count }))
    .sort((a, b) => a.date.localeCompare(b.date));

  const byMonth = Array.from(byMonthMap.entries())
    .map(([month, count]) => ({
      month: format(parseISO(`${month}-01`), 'MMM yyyy'),
      count
    }))
    .sort((a, b) => a.month.localeCompare(b.month));

  // Calculate average resolution time
  const avgResolutionTime = resolvedCount > 0
    ? Math.round(totalResolutionTime / resolvedCount)
    : 0;

  return {
    total: incidents.length,
    byType: typeCounter,
    byAction: actionCounter,
    bySeverity: severityCounter,
    byStatus: statusCounter,
    byDay,
    byMonth,
    avgResolutionTime
  };
};

// Generate geographical statistics
export const generateGeoStatistics = async (
  incidents: Incident[]
): Promise<GeoStatistics> => {
  // Identify hotspots (locations with multiple incidents)
  const locationCounter = new Map<string, { location: Coordinates; count: number }>();

  // Clusters by type - initialize with all incident types
  const clustersByType: Record<string, Array<{ location: Coordinates; count: number }>> = {};

  // Initialize with all incident types from the enum
  Object.values(IncidentType).forEach(type => {
    clustersByType[type] = [];
  });

  // Process each incident for geographical analysis
  incidents.forEach(incident => {
    const locKey = `${incident.location.latitude.toFixed(4)},${incident.location.longitude.toFixed(4)}`;

    // Update hotspot counter
    if (!locationCounter.has(locKey)) {
      locationCounter.set(locKey, {
        location: incident.location,
        count: 0
      });
    }
    locationCounter.get(locKey)!.count++;

    // Update clusters by type
    const typeKey = incident.type;
    const existingCluster = clustersByType[typeKey].find(cluster =>
      Math.abs(cluster.location.latitude - incident.location.latitude) < 0.02 &&
      Math.abs(cluster.location.longitude - incident.location.longitude) < 0.02
    );

    if (existingCluster) {
      existingCluster.count++;
    } else {
      clustersByType[typeKey].push({
        location: incident.location,
        count: 1
      });
    }
  });

  // Convert to hotspots array (only points with multiple incidents)
  const hotspots = Array.from(locationCounter.values())
    .filter(item => item.count > 1)
    .map(item => ({
      location: item.location,
      weight: item.count
    }))
    .sort((a, b) => b.weight - a.weight);

  return {
    hotspots,
    clustersByType
  };
};