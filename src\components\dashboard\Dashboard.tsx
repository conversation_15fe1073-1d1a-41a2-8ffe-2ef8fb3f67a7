import React, { useEffect, useState, useRef } from 'react';
import { <PERSON>u, <PERSON>, Settings, Clock, Database, ChevronLeft, ChevronRight } from 'lucide-react';
import StatisticsPanel from './StatisticsPanel';
import ResponseStatisticsPanel from './ResponseStatisticsPanel';
import MapLibrePanelWrapper from '../map/MapLibrePanel';
import IncidentsList from './IncidentsList';
import IncidentDetails from './IncidentDetails';
import IncidentForm from './IncidentForm';
import ResponseList from './ResponseList';
import ResponseDetails from './ResponseDetails';
import ResponseForm from './ResponseForm';
import ResponseView from './ResponseView';
import FilterPanel from './FilterPanel';
import SettingsModal from '../settings/SettingsModal';

import Button from '@/components/ui/Button';
import ErrorDisplay from '@/components/ui/ErrorDisplay';
import { useIncidentStore } from '@/store/incidentStore';
import { useResponseStore } from '@/store/responseStore';
import { useFilterStore } from '@/store/filterStore';
import { initDatabase } from '@/services/db/database';
import LoginPage from './LoginPage';
import { errorService, setupGlobalErrorHandling, LogLevel, ErrorCategory } from '@/services/logging/errorService';

const Dashboard: React.FC = () => {
  const { loadIncidents, selectedIncident, showingForm: showingIncidentForm, formLocation: incidentFormLocation, hideIncidentForm, showIncidentForm } = useIncidentStore();
  const { loadResponses, selectedResponse, showingForm: showingResponseForm, formLocation: responseFormLocation, hideResponseForm, showResponseForm } = useResponseStore();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [incidentsPanelCollapsed, setIncidentsPanelCollapsed] = useState(false);
  const [responsePanelCollapsed, setResponsePanelCollapsed] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);

  // We no longer need the map click handler as it's now handled by the context menu

  // Set up global error handling
  useEffect(() => {
    // Initialize global error handling
    setupGlobalErrorHandling();

    // Log initial application start
    errorService.info('Dashboard initialized', ErrorCategory.UI);

    // Handle storage persistence errors
    const handleStorageError = () => {
      errorService.warn(
        'Storage persistence denied. Some data may not be saved between sessions.',
        ErrorCategory.DATABASE,
        { persistenceType: 'localStorage' }
      );
    };

    // Listen for storage errors
    window.addEventListener('storage-error', handleStorageError);

    return () => {
      window.removeEventListener('storage-error', handleStorageError);
    };
  }, []);

  useEffect(() => {
    let isMounted = true;

    const initApp = async () => {
      try {
        await initDatabase();
        if (isMounted) {
          await loadIncidents();
          await loadResponses();
        }
      } catch (error) {
        console.error('Failed to initialize app:', error);
      }
    };

    if (isAuthenticated) {
      initApp();
    }

    return () => {
      isMounted = false;
    };
  }, [loadIncidents, loadResponses, isAuthenticated]);

  // Handle map events for adding incidents/responses
  useEffect(() => {
    const handleMapIncident = (event: CustomEvent) => {
      const { coordinates } = event.detail;
      showIncidentForm(coordinates);
    };

    const handleMapResponse = (event: CustomEvent) => {
      const { coordinates } = event.detail;
      showResponseForm(coordinates);
    };

    window.addEventListener('map:addIncident', handleMapIncident as EventListener);
    window.addEventListener('map:addResponse', handleMapResponse as EventListener);

    return () => {
      window.removeEventListener('map:addIncident', handleMapIncident as EventListener);
      window.removeEventListener('map:addResponse', handleMapResponse as EventListener);
    };
  }, [showIncidentForm, showResponseForm]);

  if (!isAuthenticated) {
    return <LoginPage onLoginSuccess={() => setIsAuthenticated(true)} />;
  }
  return (
    <div className="min-h-screen military-theme">
      {/* Error Display Component */}
      <ErrorDisplay position="bottom-right" autoHide={false} />

      {/* Military-themed Header */}
      <header className="military-header">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-20">
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <img src="/Resources/logo.png" alt="Logo" className="ml-2 h-14" />
                <h1 className="ml-3 text-2xl font-military-heading text-military-white tracking-wider">FARHAD SCW DECISION SUPPORT SYSTEM</h1>
              </div>
            </div>
            <div className="flex items-center">
              <button className="military-btn p-2">
                <Bell size={18} className="text-military-white" />
              </button>
              <button
                className="military-btn p-2 mx-1"
                onClick={() => setShowSettingsModal(true)}
                title="Settings"
              >
                <Settings size={18} className="text-military-white" />
              </button>
              <div className="ml-2 relative">
                <div className="flex items-center">
                  <div className="h-8 w-8 military-container bg-military-navy flex items-center justify-center text-military-white">
                    A
                  </div>
                  <div className="ml-2 relative group">
                    <button className="text-sm font-military-body text-military-white uppercase">
                      Admin
                    </button>
                    <div className="absolute right-0 mt-2 w-48 military-container bg-military-panel ring-1 ring-military-border hidden group-hover:block z-50">
                      <div className="py-1">
                        <button
                          onClick={() => setShowSettingsModal(true)}
                          className="block w-full text-center px-4 py-2 text-sm font-military-body text-military-white hover:bg-military-darkgreen flex items-center justify-center">
                          <Settings size={14} className="mr-2" /> SETTINGS
                        </button>
                        <button
                          onClick={() => {
                            localStorage.removeItem('isAuthenticated');
                            setIsAuthenticated(false);
                          }}
                          className="block w-full text-center px-4 py-2 text-sm font-military-body text-military-white hover:bg-military-darkgreen">
                          SIGN OUT
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="mb-6 military-container p-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-military-heading text-military-white uppercase tracking-wider">Tactical Operations Dashboard</h1>
              <p className="text-sm font-military-body text-military-white opacity-80 mt-1">
                MONITOR • ANALYZE • RESPOND
              </p>
              <p className="text-xs font-military-body text-military-accent opacity-80 mt-1">

              </p>
            </div>
            <div className="flex space-x-2">
              <Button
                leftIcon={<Clock size={16} />}
                variant="ghost"
                className="military-btn"
              >
                <span className="font-mono text-xs">LAST UPDATE: T-10:00</span>
              </Button>
              <Button
                onClick={() => useIncidentStore.getState().showIncidentForm()}
                className="military-btn bg-military-darkgreen"
              >
                NEW INCIDENT
              </Button>
              <Button
                onClick={() => {
                  console.log('Showing response form');
                  useResponseStore.getState().showResponseForm();
                }}
                className="military-btn bg-military-blue"
              >
                NEW RESPONSE
              </Button>
            </div>
          </div>
        </div>

      {/* Active Filters Panel */}
      <FilterPanel />

      {/* Three-panel layout: Incidents (left), Map (center), Response (right) */}
      <div className="flex flex-col lg:flex-row gap-2 mb-6 relative">
        {/* Incidents Panel - collapsible left panel */}
        <div className={`transition-all duration-300 ${incidentsPanelCollapsed ? 'lg:w-12' : 'lg:w-1/4'} flex flex-col relative`}>
          <div className="military-container h-[65vh] min-h-[400px] overflow-hidden">
            {incidentsPanelCollapsed ? (
              <div className="h-full flex flex-col items-center justify-start p-2">
                <Button
                  variant="ghost"
                  className="military-btn mb-2"
                  onClick={() => setIncidentsPanelCollapsed(false)}
                >
                  <ChevronRight size={20} />
                </Button>
                <div className="rotate-90 whitespace-nowrap text-military-white font-military-body text-sm mt-4">
                  INCIDENTS
                </div>
              </div>
            ) : (
              <div className="flex flex-col h-full">
                <div className="flex justify-between items-center p-2 border-b border-military-border">
                  <h3 className="text-sm font-military-heading text-military-white uppercase">Incidents</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="military-btn"
                    onClick={() => setIncidentsPanelCollapsed(true)}
                  >
                    <ChevronLeft size={16} />
                  </Button>
                </div>
                <div className="p-2 overflow-y-auto flex-1">
                  <IncidentsList />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Map Panel - center */}
        <div className={`transition-all duration-300 ${incidentsPanelCollapsed && responsePanelCollapsed ? 'lg:w-[calc(100%-6rem)]' : (!incidentsPanelCollapsed && !responsePanelCollapsed ? 'lg:w-2/4' : 'lg:w-[calc(75%-3rem)]')} h-[65vh] min-h-[400px]`}>
          <MapLibrePanelWrapper />
        </div>

        {/* Response Panel - collapsible right panel */}
        <div className={`transition-all duration-300 ${responsePanelCollapsed ? 'lg:w-12' : 'lg:w-1/4'} flex flex-col relative`}>
          <div className="military-container h-[65vh] min-h-[400px] overflow-hidden">
            {responsePanelCollapsed ? (
              <div className="h-full flex flex-col items-center justify-start p-2">
                <Button
                  variant="ghost"
                  className="military-btn mb-2"
                  onClick={() => setResponsePanelCollapsed(false)}
                >
                  <ChevronLeft size={20} />
                </Button>
                <div className="rotate-90 whitespace-nowrap text-military-white font-military-body text-sm mt-4">
                  RESPONSES
                </div>
              </div>
            ) : (
              <div className="flex flex-col h-full">
                <div className="flex justify-between items-center p-2 border-b border-military-border">
                  <h3 className="text-sm font-military-heading text-military-white uppercase">Responses</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="military-btn"
                    onClick={() => setResponsePanelCollapsed(true)}
                  >
                    <ChevronRight size={16} />
                  </Button>
                </div>
                <div className="p-2 overflow-y-auto flex-1">
                  <ResponseList />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Statistics panels - Incidents on left, Response on right */}
      <div className="mb-6 grid grid-cols-1 lg:grid-cols-2 gap-4">
        <div className="military-container p-2">
          <h2 className="text-lg font-military-heading text-military-white uppercase tracking-wider mb-2">INCIDENTS STATISTICS</h2>
          <StatisticsPanel />
        </div>
        <div className="military-container p-2">
          <h2 className="text-lg font-military-heading text-military-white uppercase tracking-wider mb-2">RESPONSE STATISTICS</h2>
          <ResponseStatisticsPanel />
        </div>
      </div>
    </main>
         {/* Incident details modal */}
        {selectedIncident && (
          <div className="fixed inset-0 z-[1000]">
            <div className="absolute inset-0 bg-military-black bg-opacity-80"></div>
            <div className="relative z-[1001] h-full w-full flex items-center justify-center p-4">
              <IncidentDetails />
            </div>
          </div>
        )}

        {/* Response details modal */}
        {selectedResponse && (
          <div className="fixed inset-0 z-[1000]">
            <div className="absolute inset-0 bg-military-black bg-opacity-80"></div>
            <div className="relative z-[1001] h-full w-full flex items-center justify-center p-4">
              <ResponseDetails onClose={() => useResponseStore.getState().selectResponse(null)} />
            </div>
          </div>
        )}
      {/* New incident form */}
      {showingIncidentForm && !selectedIncident && ( /* Prevent both modals from showing at once */
        <div className="fixed inset-0 z-[1000]">
          <div className="absolute inset-0 bg-military-black bg-opacity-80"></div>
          <div className="relative z-[1001] h-full w-full flex items-center justify-center p-4">
            <IncidentForm
              onClose={hideIncidentForm}
              mapCoordinates={incidentFormLocation}
            />
          </div>
        </div>
      )}

      {/* New response form */}
      {showingResponseForm && !selectedResponse && ( /* Prevent both modals from showing at once */
        <div className="fixed inset-0 z-[1000]">
          <div className="absolute inset-0 bg-military-black bg-opacity-80"></div>
          <div className="relative z-[1001] h-full w-full flex items-center justify-center p-4">
            <ResponseForm
              onClose={hideResponseForm}
              mapCoordinates={responseFormLocation}
            />
          </div>
        </div>
      )}

      {/* Settings Modal */}
      {showSettingsModal && (
        <div className="fixed inset-0 z-[1000]">
          <div className="absolute inset-0 bg-military-black bg-opacity-80"></div>
          <div className="relative z-[1001] h-full w-full flex items-center justify-center p-4">
            <SettingsModal onClose={() => setShowSettingsModal(false)} />
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;