import { IncidentType, ActionType } from '@/types/incident';

// Symbol configuration interface
export interface SymbolConfig {
  symbol: string;
  color: string;
  description: string;
  shape?: string;
  text?: string;
}

// Tactical symbols for incidents - Updated to match IncidentType enum
export const tacticalSymbols: Record<string, SymbolConfig> = {
  // Original incident types
  PHYSICAL_RAID: {
    symbol: '⚔️',
    color: '#FF4444',
    description: 'Physical Raid',
    shape: 'diamond',
    text: 'PR'
  },
  FIRE_RAID: {
    symbol: '🔥',
    color: '#FF6600',
    description: 'Fire Raid',
    shape: 'triangle',
    text: 'FR'
  },
  AMBUSH: {
    symbol: '🎯',
    color: '#CC0000',
    description: 'Ambush',
    shape: 'star',
    text: 'AMB'
  },
  SNIPING: {
    symbol: '�',
    color: '#990000',
    description: 'Sniping',
    shape: 'diamond',
    text: 'SNP'
  },
  POST_OVERRUN: {
    symbol: '🏰',
    color: '#FF0000',
    description: 'Post Overrun',
    shape: 'square',
    text: 'PO'
  },
  POST_FIRE: {
    symbol: '🔥',
    color: '#FF4400',
    description: 'Post Fire',
    shape: 'triangle',
    text: 'PF'
  },
  DEMONSTRATION: {
    symbol: '👥',
    color: '#FFAA00',
    description: 'Demonstration',
    shape: 'circle',
    text: 'DEM'
  },
  TARGET_KILLING: {
    symbol: '💀',
    color: '#800000',
    description: 'Target Killing',
    shape: 'star',
    text: 'TK'
  },
  ARSON: {
    symbol: '🔥',
    color: '#FF2200',
    description: 'Arson',
    shape: 'triangle',
    text: 'ARS'
  },

  // TS (Tactical Situation) types
  TS_ACTIVITY: {
    symbol: '⚡',
    color: '#FFCC00',
    description: 'TS Activity',
    shape: 'hexagon',
    text: 'TSA'
  },
  TS_INFIL: {
    symbol: '🔍',
    color: '#44FF44',
    description: 'TS Infiltration',
    shape: 'circle',
    text: 'TSI'
  },
  TS_PRESENCE: {
    symbol: '👁️',
    color: '#00AAFF',
    description: 'TS Presence',
    shape: 'circle',
    text: 'TSP'
  },
  TS_MOV: {
    symbol: '🚶',
    color: '#00CCCC',
    description: 'TS Movement',
    shape: 'triangle',
    text: 'TSM'
  },
  TS_TASKEEL: {
    symbol: '🔧',
    color: '#666666',
    description: 'TS Taskeel',
    shape: 'square',
    text: 'TST'
  },
  TS_SB: {
    symbol: '💥',
    color: '#FF8844',
    description: 'TS Sabotage',
    shape: 'triangle',
    text: 'TSS'
  },
  TS_EXTORTION: {
    symbol: '💰',
    color: '#FFAA00',
    description: 'TS Extortion',
    shape: 'hexagon',
    text: 'TSE'
  },
  TS_SUSPECT: {
    symbol: '❓',
    color: '#FFFF44',
    description: 'TS Suspect',
    shape: 'circle',
    text: 'TSU'
  },
  TS_MEETING: {
    symbol: '👥',
    color: '#AA00FF',
    description: 'TS Meeting',
    shape: 'circle',
    text: 'TSM'
  },
  TS_SEEN: {
    symbol: '�',
    color: '#00FF00',
    description: 'TS Seen',
    shape: 'circle',
    text: 'TSN'
  },
  TS_TGT_KILLING: {
    symbol: '🎯',
    color: '#FF0000',
    description: 'TS Target Killing',
    shape: 'star',
    text: 'TTK'
  },
  TS_JIRGA: {
    symbol: '🏛️',
    color: '#8B4513',
    description: 'TS Jirga',
    shape: 'square',
    text: 'TSJ'
  },

  OTHER: {
    symbol: '❓',
    color: '#888888',
    description: 'Other Incident',
    shape: 'circle',
    text: 'OTH'
  }
};

// Response symbols for different action types
export const responseSymbols: Record<string, SymbolConfig> = {
  surveillance: {
    symbol: '👁️',
    color: '#00AA00',
    description: 'Surveillance Operation',
    shape: 'circle',
    text: 'SUR'
  },
  raid: {
    symbol: '🏠',
    color: '#0066CC',
    description: 'Raid Operation',
    shape: 'square',
    text: 'RAID'
  },
  arrest: {
    symbol: '🚔',
    color: '#FF6600',
    description: 'Arrest Operation',
    shape: 'diamond',
    text: 'ARR'
  },
  investigation: {
    symbol: '🔍',
    color: '#9966CC',
    description: 'Investigation',
    shape: 'hexagon',
    text: 'INV'
  },
  patrol: {
    symbol: '🚶',
    color: '#00CCCC',
    description: 'Patrol Operation',
    shape: 'triangle',
    text: 'PAT'
  },
  checkpoint: {
    symbol: '🛑',
    color: '#CC6600',
    description: 'Checkpoint',
    shape: 'square',
    text: 'CP'
  },
  escort: {
    symbol: '🚗',
    color: '#6600CC',
    description: 'Escort Operation',
    shape: 'circle',
    text: 'ESC'
  },
  search: {
    symbol: '🔎',
    color: '#CC0066',
    description: 'Search Operation',
    shape: 'star',
    text: 'SRC'
  },
  other: {
    symbol: '⚙️',
    color: '#666666',
    description: 'Other Response',
    shape: 'circle',
    text: 'OTH'
  }
};

// Function to get incident symbol configuration
export const getIncidentSymbol = (type: IncidentType): SymbolConfig => {
  return tacticalSymbols[type] || tacticalSymbols.other;
};

// Function to get response symbol configuration
export const getResponseSymbol = (actionType: ActionType): SymbolConfig => {
  return responseSymbols[actionType] || responseSymbols.other;
};

// Function to create enhanced tactical icon with military styling
export const createTacticalIcon = (type: IncidentType, size: number = 32): string => {
  const config = getIncidentSymbol(type);
  const shape = config.shape || 'circle';

  // Enhanced military-style colors with better contrast
  const fillColor = config.color;
  const strokeColor = '#000000';
  const textColor = '#FFFFFF';

  // Generate enhanced SVG path with military styling
  let svgPath = '';

  switch (shape) {
    case 'diamond':
      svgPath = `<polygon points="16,3 29,16 16,29 3,16" fill="${fillColor}" stroke="${strokeColor}" stroke-width="2" />`;
      break;
    case 'square':
      svgPath = `<rect x="5" y="5" width="22" height="22" fill="${fillColor}" stroke="${strokeColor}" stroke-width="2" />`;
      break;
    case 'triangle':
      svgPath = `<polygon points="16,3 29,28 3,28" fill="${fillColor}" stroke="${strokeColor}" stroke-width="2" />`;
      break;
    case 'hexagon':
      svgPath = `<polygon points="16,3 3,11 3,21 16,29 29,21 29,11" fill="${fillColor}" stroke="${strokeColor}" stroke-width="2" />`;
      break;
    case 'star':
      svgPath = `<polygon points="16,3 19,11 28,11 21,17 24,27 16,21 8,27 11,17 4,11 13,11" fill="${fillColor}" stroke="${strokeColor}" stroke-width="2" />`;
      break;
    case 'circle':
    default:
      svgPath = `<circle cx="16" cy="16" r="13" fill="${fillColor}" stroke="${strokeColor}" stroke-width="2" />`;
      break;
  }

  // Enhanced text with better readability
  let textElement = '';
  if (config.text) {
    textElement = `
      <text x="16" y="21" font-family="Arial, sans-serif" font-size="7" font-weight="bold"
            text-anchor="middle" fill="${textColor}" stroke="${strokeColor}" stroke-width="0.5">
        ${config.text}
      </text>
    `;
  }

  return `
    <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 32 32">
      <defs>
        <filter id="shadow-${type}" x="-50%" y="-50%" width="200%" height="200%">
          <feDropShadow dx="1" dy="1" stdDeviation="1" flood-color="black" flood-opacity="0.5"/>
        </filter>
      </defs>
      <g filter="url(#shadow-${type})">
        ${svgPath}
        ${textElement}
      </g>
    </svg>
  `;
};

// Function to create enhanced response icon with military styling
export const createResponseIcon = (actionType: ActionType, size: number = 28): string => {
  const config = getResponseSymbol(actionType);
  const shape = config.shape || 'circle';

  // Enhanced military-style colors for responses
  const fillColor = config.color;
  const strokeColor = '#000000';
  const textColor = '#FFFFFF';

  // Generate enhanced SVG path with military styling
  let svgPath = '';

  switch (shape) {
    case 'diamond':
      svgPath = `<polygon points="16,3 29,16 16,29 3,16" fill="${fillColor}" stroke="${strokeColor}" stroke-width="2" />`;
      break;
    case 'square':
      svgPath = `<rect x="5" y="5" width="22" height="22" fill="${fillColor}" stroke="${strokeColor}" stroke-width="2" />`;
      break;
    case 'triangle':
      svgPath = `<polygon points="16,3 29,28 3,28" fill="${fillColor}" stroke="${strokeColor}" stroke-width="2" />`;
      break;
    case 'hexagon':
      svgPath = `<polygon points="16,3 3,11 3,21 16,29 29,21 29,11" fill="${fillColor}" stroke="${strokeColor}" stroke-width="2" />`;
      break;
    case 'star':
      svgPath = `<polygon points="16,3 19,11 28,11 21,17 24,27 16,21 8,27 11,17 4,11 13,11" fill="${fillColor}" stroke="${strokeColor}" stroke-width="2" />`;
      break;
    case 'circle':
    default:
      svgPath = `<circle cx="16" cy="16" r="13" fill="${fillColor}" stroke="${strokeColor}" stroke-width="2" />`;
      break;
  }

  // Enhanced text with better readability
  let textElement = '';
  if (config.text) {
    textElement = `
      <text x="16" y="21" font-family="Arial, sans-serif" font-size="6" font-weight="bold"
            text-anchor="middle" fill="${textColor}" stroke="${strokeColor}" stroke-width="0.5">
        ${config.text}
      </text>
    `;
  }

  return `
    <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 32 32">
      <defs>
        <filter id="shadow-response-${actionType}" x="-50%" y="-50%" width="200%" height="200%">
          <feDropShadow dx="1" dy="1" stdDeviation="1" flood-color="black" flood-opacity="0.5"/>
        </filter>
      </defs>
      <g filter="url(#shadow-response-${actionType})">
        ${svgPath}
        ${textElement}
      </g>
    </svg>
  `;
};

// Military symbol categories for enhanced symbology
export const militarySymbolCategories = {
  friendly: {
    name: 'Friendly Forces',
    color: '#0066CC',
    shape: 'square'
  },
  hostile: {
    name: 'Hostile Forces',
    color: '#CC0000',
    shape: 'diamond'
  },
  neutral: {
    name: 'Neutral/Civilian',
    color: '#00CC00',
    shape: 'circle'
  },
  unknown: {
    name: 'Unknown/Pending',
    color: '#FFCC00',
    shape: 'circle'
  },
  command: 'Command & Control',
  logistics: 'Logistics',
  medical: 'Medical',
  communications: 'Communications'
};

// Enhanced military symbols for tactical operations
export const enhancedMilitarySymbols = {
  // Command and Control
  command_post: {
    symbol: '⌘',
    color: '#FFD700',
    description: 'Command Post',
    shape: 'square',
    text: 'CP'
  },
  observation_post: {
    symbol: '👁',
    color: '#00FF00',
    description: 'Observation Post',
    shape: 'circle',
    text: 'OP'
  },

  // Logistics
  supply_depot: {
    symbol: '📦',
    color: '#8B4513',
    description: 'Supply Depot',
    shape: 'square',
    text: 'SUP'
  },
  fuel_depot: {
    symbol: '⛽',
    color: '#FF8C00',
    description: 'Fuel Depot',
    shape: 'square',
    text: 'FUEL'
  },

  // Medical
  medical_station: {
    symbol: '🏥',
    color: '#FF0000',
    description: 'Medical Station',
    shape: 'circle',
    text: 'MED'
  },
  casualty_collection: {
    symbol: '🚑',
    color: '#FF6B6B',
    description: 'Casualty Collection Point',
    shape: 'circle',
    text: 'CCP'
  },

  // Communications
  radio_relay: {
    symbol: '📡',
    color: '#4169E1',
    description: 'Radio Relay',
    shape: 'triangle',
    text: 'RR'
  },
  communication_hub: {
    symbol: '📻',
    color: '#6495ED',
    description: 'Communication Hub',
    shape: 'hexagon',
    text: 'COMM'
  }
};

// Export all symbols as a combined collection
export const allTacticalSymbols = {
  incidents: tacticalSymbols,
  responses: responseSymbols,
  military: enhancedMilitarySymbols
};

export default {
  tacticalSymbols,
  responseSymbols,
  getIncidentSymbol,
  getResponseSymbol,
  createTacticalIcon,
  createResponseIcon,
  militarySymbolCategories,
  enhancedMilitarySymbols,
  allTacticalSymbols
};
