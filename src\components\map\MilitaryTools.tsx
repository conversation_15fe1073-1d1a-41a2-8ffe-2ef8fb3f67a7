import React, { useState } from 'react';
import { Target, Ruler, Navigation, Crosshair, MapPin } from 'lucide-react';
import Button from '@/components/ui/Button';

interface MilitaryToolsProps {
  className?: string;
}

const MilitaryTools: React.FC<MilitaryToolsProps> = ({ className = '' }) => {
  const [activeTool, setActiveTool] = useState<string | null>(null);

  const handleToolClick = (tool: string) => {
    setActiveTool(activeTool === tool ? null : tool);
    console.log(`${tool} tool ${activeTool === tool ? 'deactivated' : 'activated'}`);
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="text-white text-sm font-bold mb-2">MILITARY TOOLS</div>
      
      <Button
        size="sm"
        variant="ghost"
        className={`w-full text-left justify-start text-white ${activeTool === 'targeting' ? 'bg-blue-600' : ''}`}
        onClick={() => handleToolClick('targeting')}
      >
        <Target size={14} className="mr-2" />
        Targeting
      </Button>

      <Button
        size="sm"
        variant="ghost"
        className={`w-full text-left justify-start text-white ${activeTool === 'measurement' ? 'bg-blue-600' : ''}`}
        onClick={() => handleToolClick('measurement')}
      >
        <Ruler size={14} className="mr-2" />
        Measurement
      </Button>

      <Button
        size="sm"
        variant="ghost"
        className={`w-full text-left justify-start text-white ${activeTool === 'navigation' ? 'bg-blue-600' : ''}`}
        onClick={() => handleToolClick('navigation')}
      >
        <Navigation size={14} className="mr-2" />
        Navigation
      </Button>

      <Button
        size="sm"
        variant="ghost"
        className={`w-full text-left justify-start text-white ${activeTool === 'coordinates' ? 'bg-blue-600' : ''}`}
        onClick={() => handleToolClick('coordinates')}
      >
        <Crosshair size={14} className="mr-2" />
        Coordinates
      </Button>

      <Button
        size="sm"
        variant="ghost"
        className={`w-full text-left justify-start text-white ${activeTool === 'waypoint' ? 'bg-blue-600' : ''}`}
        onClick={() => handleToolClick('waypoint')}
      >
        <MapPin size={14} className="mr-2" />
        Waypoint
      </Button>
    </div>
  );
};

export default MilitaryTools;
